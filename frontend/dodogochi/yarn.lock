# This file is generated by running "yarn install" inside your project.
# Manual changes might be lost - proceed with caution!

__metadata:
  version: 8
  cacheKey: 10

"@adobe/css-tools@npm:^4.4.0":
  version: 4.4.4
  resolution: "@adobe/css-tools@npm:4.4.4"
  checksum: 10/0abd4715737877e5aa5d730d6ec2cffae2131102ddc8310ac5ba3f457ffb2ef453324dbb5b927e3cbc3f81bdd29ce485754014c6e64f4577a49540c76e26ac6b
  languageName: node
  linkType: hard

"@ampproject/remapping@npm:^2.2.0, @ampproject/remapping@npm:^2.3.0":
  version: 2.3.0
  resolution: "@ampproject/remapping@npm:2.3.0"
  dependencies:
    "@jridgewell/gen-mapping": "npm:^0.3.5"
    "@jridgewell/trace-mapping": "npm:^0.3.24"
  checksum: 10/f3451525379c68a73eb0a1e65247fbf28c0cccd126d93af21c75fceff77773d43c0d4a2d51978fb131aff25b5f2cb41a9fe48cc296e61ae65e679c4f6918b0ab
  languageName: node
  linkType: hard

"@asamuzakjp/css-color@npm:^3.2.0":
  version: 3.2.0
  resolution: "@asamuzakjp/css-color@npm:3.2.0"
  dependencies:
    "@csstools/css-calc": "npm:^2.1.3"
    "@csstools/css-color-parser": "npm:^3.0.9"
    "@csstools/css-parser-algorithms": "npm:^3.0.4"
    "@csstools/css-tokenizer": "npm:^3.0.3"
    lru-cache: "npm:^10.4.3"
  checksum: 10/870f661460173174fef8bfebea0799ba26566f3aa7b307e5adabb7aae84fed2da68e40080104ed0c83b43c5be632ee409e65396af13bfe948a3ef4c2c729ecd9
  languageName: node
  linkType: hard

"@babel/code-frame@npm:^7.0.0, @babel/code-frame@npm:^7.10.4, @babel/code-frame@npm:^7.27.1":
  version: 7.27.1
  resolution: "@babel/code-frame@npm:7.27.1"
  dependencies:
    "@babel/helper-validator-identifier": "npm:^7.27.1"
    js-tokens: "npm:^4.0.0"
    picocolors: "npm:^1.1.1"
  checksum: 10/721b8a6e360a1fa0f1c9fe7351ae6c874828e119183688b533c477aa378f1010f37cc9afbfc4722c686d1f5cdd00da02eab4ba7278a0c504fa0d7a321dcd4fdf
  languageName: node
  linkType: hard

"@babel/compat-data@npm:^7.27.2, @babel/compat-data@npm:^7.27.7, @babel/compat-data@npm:^7.28.0":
  version: 7.28.0
  resolution: "@babel/compat-data@npm:7.28.0"
  checksum: 10/1a56a5e48c7259f72cc4329adeca38e72fd650ea09de267ea4aa070e3da91e5c265313b6656823fff77d64a8bab9554f276c66dade9355fdc0d8604deea015aa
  languageName: node
  linkType: hard

"@babel/core@npm:7.28.3, @babel/core@npm:^7.21.3, @babel/core@npm:^7.23.7, @babel/core@npm:^7.23.9, @babel/core@npm:^7.27.4, @babel/core@npm:^7.27.7":
  version: 7.28.3
  resolution: "@babel/core@npm:7.28.3"
  dependencies:
    "@ampproject/remapping": "npm:^2.2.0"
    "@babel/code-frame": "npm:^7.27.1"
    "@babel/generator": "npm:^7.28.3"
    "@babel/helper-compilation-targets": "npm:^7.27.2"
    "@babel/helper-module-transforms": "npm:^7.28.3"
    "@babel/helpers": "npm:^7.28.3"
    "@babel/parser": "npm:^7.28.3"
    "@babel/template": "npm:^7.27.2"
    "@babel/traverse": "npm:^7.28.3"
    "@babel/types": "npm:^7.28.2"
    convert-source-map: "npm:^2.0.0"
    debug: "npm:^4.1.0"
    gensync: "npm:^1.0.0-beta.2"
    json5: "npm:^2.2.3"
    semver: "npm:^6.3.1"
  checksum: 10/0faded84edcfd80f9a5ccc35abd46267360bba23ac295291becc8b8f9c95220f1914491b83b15e297201b19af78bbaf2ad48c2dc9d86b92f3f16a06938de8c72
  languageName: node
  linkType: hard

"@babel/generator@npm:^7.27.5, @babel/generator@npm:^7.28.3":
  version: 7.28.3
  resolution: "@babel/generator@npm:7.28.3"
  dependencies:
    "@babel/parser": "npm:^7.28.3"
    "@babel/types": "npm:^7.28.2"
    "@jridgewell/gen-mapping": "npm:^0.3.12"
    "@jridgewell/trace-mapping": "npm:^0.3.28"
    jsesc: "npm:^3.0.2"
  checksum: 10/d00d1e6b51059e47594aab7920b88ec6fcef6489954a9172235ab57ad2e91b39c95376963a6e2e4cc7e8b88fa4f931018f71f9ab32bbc9c0bc0de35a0231f26c
  languageName: node
  linkType: hard

"@babel/helper-annotate-as-pure@npm:^7.27.1, @babel/helper-annotate-as-pure@npm:^7.27.3":
  version: 7.27.3
  resolution: "@babel/helper-annotate-as-pure@npm:7.27.3"
  dependencies:
    "@babel/types": "npm:^7.27.3"
  checksum: 10/63863a5c936ef82b546ca289c9d1b18fabfc24da5c4ee382830b124e2e79b68d626207febc8d4bffc720f50b2ee65691d7d12cc0308679dee2cd6bdc926b7190
  languageName: node
  linkType: hard

"@babel/helper-compilation-targets@npm:^7.27.1, @babel/helper-compilation-targets@npm:^7.27.2":
  version: 7.27.2
  resolution: "@babel/helper-compilation-targets@npm:7.27.2"
  dependencies:
    "@babel/compat-data": "npm:^7.27.2"
    "@babel/helper-validator-option": "npm:^7.27.1"
    browserslist: "npm:^4.24.0"
    lru-cache: "npm:^5.1.1"
    semver: "npm:^6.3.1"
  checksum: 10/bd53c30a7477049db04b655d11f4c3500aea3bcbc2497cf02161de2ecf994fec7c098aabbcebe210ffabc2ecbdb1e3ffad23fb4d3f18723b814f423ea1749fe8
  languageName: node
  linkType: hard

"@babel/helper-create-class-features-plugin@npm:^7.27.1, @babel/helper-create-class-features-plugin@npm:^7.28.3":
  version: 7.28.3
  resolution: "@babel/helper-create-class-features-plugin@npm:7.28.3"
  dependencies:
    "@babel/helper-annotate-as-pure": "npm:^7.27.3"
    "@babel/helper-member-expression-to-functions": "npm:^7.27.1"
    "@babel/helper-optimise-call-expression": "npm:^7.27.1"
    "@babel/helper-replace-supers": "npm:^7.27.1"
    "@babel/helper-skip-transparent-expression-wrappers": "npm:^7.27.1"
    "@babel/traverse": "npm:^7.28.3"
    semver: "npm:^6.3.1"
  peerDependencies:
    "@babel/core": ^7.0.0
  checksum: 10/32d01bdd601b4d129b1d510058a19644abc764badcc543adaec9e71443e874ef252783cceb2809645bdf0e92b07f206fd439c75a2a48cf702c627aba7f3ee34a
  languageName: node
  linkType: hard

"@babel/helper-create-regexp-features-plugin@npm:^7.18.6, @babel/helper-create-regexp-features-plugin@npm:^7.27.1":
  version: 7.27.1
  resolution: "@babel/helper-create-regexp-features-plugin@npm:7.27.1"
  dependencies:
    "@babel/helper-annotate-as-pure": "npm:^7.27.1"
    regexpu-core: "npm:^6.2.0"
    semver: "npm:^6.3.1"
  peerDependencies:
    "@babel/core": ^7.0.0
  checksum: 10/dea272628cd8874f127ab7b2ee468620aabc1383d38bb40c49a9c7667db2258cdfe6620a1d1412f5f0706583f6301b4b7ad3d5932f24df7fe72e66bf9bc0be45
  languageName: node
  linkType: hard

"@babel/helper-define-polyfill-provider@npm:^0.6.5":
  version: 0.6.5
  resolution: "@babel/helper-define-polyfill-provider@npm:0.6.5"
  dependencies:
    "@babel/helper-compilation-targets": "npm:^7.27.2"
    "@babel/helper-plugin-utils": "npm:^7.27.1"
    debug: "npm:^4.4.1"
    lodash.debounce: "npm:^4.0.8"
    resolve: "npm:^1.22.10"
  peerDependencies:
    "@babel/core": ^7.4.0 || ^8.0.0-0 <8.0.0
  checksum: 10/0bdd2d9654d2f650c33976caa1a2afac2c23cf07e83856acdb482423c7bf4542c499ca0bdc723f2961bb36883501f09e9f4fe061ba81c07996daacfba82a6f62
  languageName: node
  linkType: hard

"@babel/helper-globals@npm:^7.28.0":
  version: 7.28.0
  resolution: "@babel/helper-globals@npm:7.28.0"
  checksum: 10/91445f7edfde9b65dcac47f4f858f68dc1661bf73332060ab67ad7cc7b313421099a2bfc4bda30c3db3842cfa1e86fffbb0d7b2c5205a177d91b22c8d7d9cb47
  languageName: node
  linkType: hard

"@babel/helper-member-expression-to-functions@npm:^7.27.1":
  version: 7.27.1
  resolution: "@babel/helper-member-expression-to-functions@npm:7.27.1"
  dependencies:
    "@babel/traverse": "npm:^7.27.1"
    "@babel/types": "npm:^7.27.1"
  checksum: 10/533a5a2cf1c9a8770d241b86d5f124c88e953c831a359faf1ac7ba1e632749c1748281b83295d227fe6035b202d81f3d3a1ea13891f150c6538e040668d6126a
  languageName: node
  linkType: hard

"@babel/helper-module-imports@npm:^7.18.6, @babel/helper-module-imports@npm:^7.27.1":
  version: 7.27.1
  resolution: "@babel/helper-module-imports@npm:7.27.1"
  dependencies:
    "@babel/traverse": "npm:^7.27.1"
    "@babel/types": "npm:^7.27.1"
  checksum: 10/58e792ea5d4ae71676e0d03d9fef33e886a09602addc3bd01388a98d87df9fcfd192968feb40ac4aedb7e287ec3d0c17b33e3ecefe002592041a91d8a1998a8d
  languageName: node
  linkType: hard

"@babel/helper-module-transforms@npm:^7.27.1, @babel/helper-module-transforms@npm:^7.28.3":
  version: 7.28.3
  resolution: "@babel/helper-module-transforms@npm:7.28.3"
  dependencies:
    "@babel/helper-module-imports": "npm:^7.27.1"
    "@babel/helper-validator-identifier": "npm:^7.27.1"
    "@babel/traverse": "npm:^7.28.3"
  peerDependencies:
    "@babel/core": ^7.0.0
  checksum: 10/598fdd8aa5b91f08542d0ba62a737847d0e752c8b95ae2566bc9d11d371856d6867d93e50db870fb836a6c44cfe481c189d8a2b35ca025a224f070624be9fa87
  languageName: node
  linkType: hard

"@babel/helper-optimise-call-expression@npm:^7.27.1":
  version: 7.27.1
  resolution: "@babel/helper-optimise-call-expression@npm:7.27.1"
  dependencies:
    "@babel/types": "npm:^7.27.1"
  checksum: 10/0fb7ee824a384529d6b74f8a58279f9b56bfe3cce332168067dddeab2552d8eeb56dc8eaf86c04a3a09166a316cb92dfc79c4c623cd034ad4c563952c98b464f
  languageName: node
  linkType: hard

"@babel/helper-plugin-utils@npm:^7.0.0, @babel/helper-plugin-utils@npm:^7.18.6, @babel/helper-plugin-utils@npm:^7.27.1":
  version: 7.27.1
  resolution: "@babel/helper-plugin-utils@npm:7.27.1"
  checksum: 10/96136c2428888e620e2ec493c25888f9ceb4a21099dcf3dd4508ea64b58cdedbd5a9fb6c7b352546de84d6c24edafe482318646932a22c449ebd16d16c22d864
  languageName: node
  linkType: hard

"@babel/helper-remap-async-to-generator@npm:^7.27.1":
  version: 7.27.1
  resolution: "@babel/helper-remap-async-to-generator@npm:7.27.1"
  dependencies:
    "@babel/helper-annotate-as-pure": "npm:^7.27.1"
    "@babel/helper-wrap-function": "npm:^7.27.1"
    "@babel/traverse": "npm:^7.27.1"
  peerDependencies:
    "@babel/core": ^7.0.0
  checksum: 10/0747397ba013f87dbf575454a76c18210d61c7c9af0f697546b4bcac670b54ddc156330234407b397f0c948738c304c228e0223039bc45eab4fbf46966a5e8cc
  languageName: node
  linkType: hard

"@babel/helper-replace-supers@npm:^7.27.1":
  version: 7.27.1
  resolution: "@babel/helper-replace-supers@npm:7.27.1"
  dependencies:
    "@babel/helper-member-expression-to-functions": "npm:^7.27.1"
    "@babel/helper-optimise-call-expression": "npm:^7.27.1"
    "@babel/traverse": "npm:^7.27.1"
  peerDependencies:
    "@babel/core": ^7.0.0
  checksum: 10/72e3f8bef744c06874206bf0d80a0abbedbda269586966511c2491df4f6bf6d47a94700810c7a6737345a545dfb8295222e1e72f506bcd0b40edb3f594f739ea
  languageName: node
  linkType: hard

"@babel/helper-skip-transparent-expression-wrappers@npm:^7.27.1":
  version: 7.27.1
  resolution: "@babel/helper-skip-transparent-expression-wrappers@npm:7.27.1"
  dependencies:
    "@babel/traverse": "npm:^7.27.1"
    "@babel/types": "npm:^7.27.1"
  checksum: 10/4f380c5d0e0769fa6942a468b0c2d7c8f0c438f941aaa88f785f8752c103631d0904c7b4e76207a3b0e6588b2dec376595370d92ca8f8f1b422c14a69aa146d4
  languageName: node
  linkType: hard

"@babel/helper-string-parser@npm:^7.27.1":
  version: 7.27.1
  resolution: "@babel/helper-string-parser@npm:7.27.1"
  checksum: 10/0ae29cc2005084abdae2966afdb86ed14d41c9c37db02c3693d5022fba9f5d59b011d039380b8e537c34daf117c549f52b452398f576e908fb9db3c7abbb3a00
  languageName: node
  linkType: hard

"@babel/helper-validator-identifier@npm:^7.27.1":
  version: 7.27.1
  resolution: "@babel/helper-validator-identifier@npm:7.27.1"
  checksum: 10/75041904d21bdc0cd3b07a8ac90b11d64cd3c881e89cb936fa80edd734bf23c35e6bd1312611e8574c4eab1f3af0f63e8a5894f4699e9cfdf70c06fcf4252320
  languageName: node
  linkType: hard

"@babel/helper-validator-option@npm:^7.27.1":
  version: 7.27.1
  resolution: "@babel/helper-validator-option@npm:7.27.1"
  checksum: 10/db73e6a308092531c629ee5de7f0d04390835b21a263be2644276cb27da2384b64676cab9f22cd8d8dbd854c92b1d7d56fc8517cf0070c35d1c14a8c828b0903
  languageName: node
  linkType: hard

"@babel/helper-wrap-function@npm:^7.27.1":
  version: 7.28.3
  resolution: "@babel/helper-wrap-function@npm:7.28.3"
  dependencies:
    "@babel/template": "npm:^7.27.2"
    "@babel/traverse": "npm:^7.28.3"
    "@babel/types": "npm:^7.28.2"
  checksum: 10/a5ed5fe7b8d9949d3b4f45ccec0b365018b8e444f6a6d794b4c8291e251e680f5b7c79c49c2170de9d14967c78721f59620ce70c5dac2d53c30628ef971d9dce
  languageName: node
  linkType: hard

"@babel/helpers@npm:^7.28.3":
  version: 7.28.3
  resolution: "@babel/helpers@npm:7.28.3"
  dependencies:
    "@babel/template": "npm:^7.27.2"
    "@babel/types": "npm:^7.28.2"
  checksum: 10/6d39031bf07a001c731e5e23e024b3d5e4885a140ce7d46e17f10f0d819f8bdb974204b3aa7127e95b63a009abf0df0d81573ceeac6a8f9a3b28bde3d2e16dd1
  languageName: node
  linkType: hard

"@babel/parser@npm:^7.23.6, @babel/parser@npm:^7.23.9, @babel/parser@npm:^7.25.4, @babel/parser@npm:^7.27.2, @babel/parser@npm:^7.27.5, @babel/parser@npm:^7.28.3":
  version: 7.28.3
  resolution: "@babel/parser@npm:7.28.3"
  dependencies:
    "@babel/types": "npm:^7.28.2"
  bin:
    parser: ./bin/babel-parser.js
  checksum: 10/9fa08282e345b9d892a6757b2789a9a53a00f7b7b34d6254a4ee0bf32c5eb275919091ea96d6f136a948d5de9c8219235957d04a36ab7378a9d93a4cf0799155
  languageName: node
  linkType: hard

"@babel/plugin-bugfix-firefox-class-in-computed-class-key@npm:^7.27.1":
  version: 7.27.1
  resolution: "@babel/plugin-bugfix-firefox-class-in-computed-class-key@npm:7.27.1"
  dependencies:
    "@babel/helper-plugin-utils": "npm:^7.27.1"
    "@babel/traverse": "npm:^7.27.1"
  peerDependencies:
    "@babel/core": ^7.0.0
  checksum: 10/fe65257d5b82558bc6bc0f3a5a7a35b4166f71bed3747714dafb6360fadb15f036d568bc1fbeedae819165008c8feb646633ab91c0e3a95284963972f4fa9751
  languageName: node
  linkType: hard

"@babel/plugin-bugfix-safari-class-field-initializer-scope@npm:^7.27.1":
  version: 7.27.1
  resolution: "@babel/plugin-bugfix-safari-class-field-initializer-scope@npm:7.27.1"
  dependencies:
    "@babel/helper-plugin-utils": "npm:^7.27.1"
  peerDependencies:
    "@babel/core": ^7.0.0
  checksum: 10/eb7f4146dc01f1198ce559a90b077e58b951a07521ec414e3c7d4593bf6c4ab5c2af22242a7e9fec085e20299e0ba6ea97f44a45e84ab148141bf9eb959ad25e
  languageName: node
  linkType: hard

"@babel/plugin-bugfix-safari-id-destructuring-collision-in-function-expression@npm:^7.27.1":
  version: 7.27.1
  resolution: "@babel/plugin-bugfix-safari-id-destructuring-collision-in-function-expression@npm:7.27.1"
  dependencies:
    "@babel/helper-plugin-utils": "npm:^7.27.1"
  peerDependencies:
    "@babel/core": ^7.0.0
  checksum: 10/621cfddfcc99a81e74f8b6f9101fd260b27500cb1a568e3ceae9cc8afe9aee45ac3bca3900a2b66c612b1a2366d29ef67d4df5a1c975be727eaad6906f98c2c6
  languageName: node
  linkType: hard

"@babel/plugin-bugfix-v8-spread-parameters-in-optional-chaining@npm:^7.27.1":
  version: 7.27.1
  resolution: "@babel/plugin-bugfix-v8-spread-parameters-in-optional-chaining@npm:7.27.1"
  dependencies:
    "@babel/helper-plugin-utils": "npm:^7.27.1"
    "@babel/helper-skip-transparent-expression-wrappers": "npm:^7.27.1"
    "@babel/plugin-transform-optional-chaining": "npm:^7.27.1"
  peerDependencies:
    "@babel/core": ^7.13.0
  checksum: 10/f07aa80272bd7a46b7ba11a4644da6c9b6a5a64e848dfaffdad6f02663adefd512e1aaebe664c4dd95f7ed4f80c872c7f8db8d8e34b47aae0930b412a28711a0
  languageName: node
  linkType: hard

"@babel/plugin-bugfix-v8-static-class-fields-redefine-readonly@npm:^7.28.3":
  version: 7.28.3
  resolution: "@babel/plugin-bugfix-v8-static-class-fields-redefine-readonly@npm:7.28.3"
  dependencies:
    "@babel/helper-plugin-utils": "npm:^7.27.1"
    "@babel/traverse": "npm:^7.28.3"
  peerDependencies:
    "@babel/core": ^7.0.0
  checksum: 10/eeacdb7fa5ae19e366cbc4da98736b898e05b9abe572aa23093e6be842c6c8284d08af538528ec771073a3749718033be3713ff455ca008d11a7b0e90e62a53d
  languageName: node
  linkType: hard

"@babel/plugin-proposal-private-property-in-object@npm:7.21.0-placeholder-for-preset-env.2":
  version: 7.21.0-placeholder-for-preset-env.2
  resolution: "@babel/plugin-proposal-private-property-in-object@npm:7.21.0-placeholder-for-preset-env.2"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 10/fab70f399aa869275690ec6c7cedb4ef361d4e8b6f55c3d7b04bfee61d52fb93c87cec2c65d73cddbaca89fb8ef5ec0921fce675c9169d9d51f18305ab34e78a
  languageName: node
  linkType: hard

"@babel/plugin-syntax-import-assertions@npm:^7.27.1":
  version: 7.27.1
  resolution: "@babel/plugin-syntax-import-assertions@npm:7.27.1"
  dependencies:
    "@babel/helper-plugin-utils": "npm:^7.27.1"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 10/fb661d630808d67ecb85eabad25aac4e9696a20464bad4c4a6a0d3d40e4dc22557d47e9be3d591ec06429cf048cfe169b8891c373606344d51c4f3ac0f91d6d0
  languageName: node
  linkType: hard

"@babel/plugin-syntax-import-attributes@npm:^7.27.1":
  version: 7.27.1
  resolution: "@babel/plugin-syntax-import-attributes@npm:7.27.1"
  dependencies:
    "@babel/helper-plugin-utils": "npm:^7.27.1"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 10/97973982fff1bbf86b3d1df13380567042887c50e2ae13a400d02a8ff2c9742a60a75e279bfb73019e1cd9710f04be5e6ab81f896e6678dcfcec8b135e8896cf
  languageName: node
  linkType: hard

"@babel/plugin-syntax-jsx@npm:^7.27.1":
  version: 7.27.1
  resolution: "@babel/plugin-syntax-jsx@npm:7.27.1"
  dependencies:
    "@babel/helper-plugin-utils": "npm:^7.27.1"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 10/c6d1324cff286a369aa95d99b8abd21dd07821b5d3affd5fe7d6058c84cff9190743287826463ee57a7beecd10fa1e4bc99061df532ee14e188c1c8937b13e3a
  languageName: node
  linkType: hard

"@babel/plugin-syntax-typescript@npm:^7.27.1":
  version: 7.27.1
  resolution: "@babel/plugin-syntax-typescript@npm:7.27.1"
  dependencies:
    "@babel/helper-plugin-utils": "npm:^7.27.1"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 10/87836f7e32af624c2914c73cd6b9803cf324e07d43f61dbb973c6a86f75df725e12540d91fac7141c14b697aa9268fd064220998daced156e96ac3062d7afb41
  languageName: node
  linkType: hard

"@babel/plugin-syntax-unicode-sets-regex@npm:^7.18.6":
  version: 7.18.6
  resolution: "@babel/plugin-syntax-unicode-sets-regex@npm:7.18.6"
  dependencies:
    "@babel/helper-create-regexp-features-plugin": "npm:^7.18.6"
    "@babel/helper-plugin-utils": "npm:^7.18.6"
  peerDependencies:
    "@babel/core": ^7.0.0
  checksum: 10/a651d700fe63ff0ddfd7186f4ebc24447ca734f114433139e3c027bc94a900d013cf1ef2e2db8430425ba542e39ae160c3b05f06b59fd4656273a3df97679e9c
  languageName: node
  linkType: hard

"@babel/plugin-transform-arrow-functions@npm:^7.27.1":
  version: 7.27.1
  resolution: "@babel/plugin-transform-arrow-functions@npm:7.27.1"
  dependencies:
    "@babel/helper-plugin-utils": "npm:^7.27.1"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 10/62c2cc0ae2093336b1aa1376741c5ed245c0987d9e4b4c5313da4a38155509a7098b5acce582b6781cc0699381420010da2e3086353344abe0a6a0ec38961eb7
  languageName: node
  linkType: hard

"@babel/plugin-transform-async-generator-functions@npm:^7.28.0":
  version: 7.28.0
  resolution: "@babel/plugin-transform-async-generator-functions@npm:7.28.0"
  dependencies:
    "@babel/helper-plugin-utils": "npm:^7.27.1"
    "@babel/helper-remap-async-to-generator": "npm:^7.27.1"
    "@babel/traverse": "npm:^7.28.0"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 10/8ad31b9969b203dec572738a872e17b14ef76ca45b4ef5ffa76f3514be417ca233d1a0978e5f8de166412a8a745619eb22b07cc5df96f5ebad8ca500f920f61b
  languageName: node
  linkType: hard

"@babel/plugin-transform-async-to-generator@npm:^7.27.1":
  version: 7.27.1
  resolution: "@babel/plugin-transform-async-to-generator@npm:7.27.1"
  dependencies:
    "@babel/helper-module-imports": "npm:^7.27.1"
    "@babel/helper-plugin-utils": "npm:^7.27.1"
    "@babel/helper-remap-async-to-generator": "npm:^7.27.1"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 10/d79d7a7ae7d416f6a48200017d027a6ba94c09c7617eea8b4e9c803630f00094c1a4fc32bf20ce3282567824ce3fcbda51653aac4003c71ea4e681b331338979
  languageName: node
  linkType: hard

"@babel/plugin-transform-block-scoped-functions@npm:^7.27.1":
  version: 7.27.1
  resolution: "@babel/plugin-transform-block-scoped-functions@npm:7.27.1"
  dependencies:
    "@babel/helper-plugin-utils": "npm:^7.27.1"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 10/7fb4988ca80cf1fc8345310d5edfe38e86b3a72a302675cdd09404d5064fe1d1fe1283ebe658ad2b71445ecef857bfb29a748064306b5f6c628e0084759c2201
  languageName: node
  linkType: hard

"@babel/plugin-transform-block-scoping@npm:^7.28.0":
  version: 7.28.0
  resolution: "@babel/plugin-transform-block-scoping@npm:7.28.0"
  dependencies:
    "@babel/helper-plugin-utils": "npm:^7.27.1"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 10/eefa0d0b3cd8005b77ad3239700cec90c2b19612e664772c50da6b917b272d20ebc831db6ff0d9fef011a810d9f02c434fdf551b3a4264eb834afa20090a9434
  languageName: node
  linkType: hard

"@babel/plugin-transform-class-properties@npm:^7.27.1":
  version: 7.27.1
  resolution: "@babel/plugin-transform-class-properties@npm:7.27.1"
  dependencies:
    "@babel/helper-create-class-features-plugin": "npm:^7.27.1"
    "@babel/helper-plugin-utils": "npm:^7.27.1"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 10/475a6e5a9454912fe1bdc171941976ca10ea4e707675d671cdb5ce6b6761d84d1791ac61b6bca81a2e5f6430cb7b9d8e4b2392404110e69c28207a754e196294
  languageName: node
  linkType: hard

"@babel/plugin-transform-class-static-block@npm:^7.28.3":
  version: 7.28.3
  resolution: "@babel/plugin-transform-class-static-block@npm:7.28.3"
  dependencies:
    "@babel/helper-create-class-features-plugin": "npm:^7.28.3"
    "@babel/helper-plugin-utils": "npm:^7.27.1"
  peerDependencies:
    "@babel/core": ^7.12.0
  checksum: 10/c0ba8f0cbf3699287e5a711907dab3b29f346d9c107faa4e424aa26252e45845d74ca08ee6245bfccf32a8c04bc1d07a89b635e51522592c6044b810a48d3f58
  languageName: node
  linkType: hard

"@babel/plugin-transform-classes@npm:^7.28.3":
  version: 7.28.3
  resolution: "@babel/plugin-transform-classes@npm:7.28.3"
  dependencies:
    "@babel/helper-annotate-as-pure": "npm:^7.27.3"
    "@babel/helper-compilation-targets": "npm:^7.27.2"
    "@babel/helper-globals": "npm:^7.28.0"
    "@babel/helper-plugin-utils": "npm:^7.27.1"
    "@babel/helper-replace-supers": "npm:^7.27.1"
    "@babel/traverse": "npm:^7.28.3"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 10/0aefcabe6849f2306838a453e319a7452c7ce21f1307be72664c613d67477f7e5f67dc41ef096989088ff6c390c705e1d041fad7673d614de007b9f0f5871a29
  languageName: node
  linkType: hard

"@babel/plugin-transform-computed-properties@npm:^7.27.1":
  version: 7.27.1
  resolution: "@babel/plugin-transform-computed-properties@npm:7.27.1"
  dependencies:
    "@babel/helper-plugin-utils": "npm:^7.27.1"
    "@babel/template": "npm:^7.27.1"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 10/101f6d4575447070943d5a9efaa5bea8c552ea3083d73a9612f1a16d38b0a0a7b79a5feb65c6cc4e4fcabf28e85a570b97ccd3294da966e8fbbb6dfb97220eda
  languageName: node
  linkType: hard

"@babel/plugin-transform-destructuring@npm:^7.28.0":
  version: 7.28.0
  resolution: "@babel/plugin-transform-destructuring@npm:7.28.0"
  dependencies:
    "@babel/helper-plugin-utils": "npm:^7.27.1"
    "@babel/traverse": "npm:^7.28.0"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 10/cddab2520ff32d18005670fc6646396a253d3811d1ccc49f6f858469f05985ee896c346a0cb34d1cf25155c9be76d1068ff878cf8e8459bd3fa27513ec5a6802
  languageName: node
  linkType: hard

"@babel/plugin-transform-dotall-regex@npm:^7.27.1":
  version: 7.27.1
  resolution: "@babel/plugin-transform-dotall-regex@npm:7.27.1"
  dependencies:
    "@babel/helper-create-regexp-features-plugin": "npm:^7.27.1"
    "@babel/helper-plugin-utils": "npm:^7.27.1"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 10/2173e5b13f403538ffc6bd57b190cedf4caf320abc13a99e5b2721864e7148dbd3bd7c82d92377136af80432818f665fdd9a1fd33bc5549a4c91e24e5ce2413c
  languageName: node
  linkType: hard

"@babel/plugin-transform-duplicate-keys@npm:^7.27.1":
  version: 7.27.1
  resolution: "@babel/plugin-transform-duplicate-keys@npm:7.27.1"
  dependencies:
    "@babel/helper-plugin-utils": "npm:^7.27.1"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 10/987b718d2fab7626f61b72325c8121ead42341d6f46ad3a9b5e5f67f3ec558c903f1b8336277ffc43caac504ce00dd23a5456b5d1da23913333e1da77751f08d
  languageName: node
  linkType: hard

"@babel/plugin-transform-duplicate-named-capturing-groups-regex@npm:^7.27.1":
  version: 7.27.1
  resolution: "@babel/plugin-transform-duplicate-named-capturing-groups-regex@npm:7.27.1"
  dependencies:
    "@babel/helper-create-regexp-features-plugin": "npm:^7.27.1"
    "@babel/helper-plugin-utils": "npm:^7.27.1"
  peerDependencies:
    "@babel/core": ^7.0.0
  checksum: 10/2a109613535e6ac79240dced71429e988affd6a5b3d0cd0f563c8d6c208c51ce7bf2c300bc1150502376b26a51f279119b3358f1c0f2d2f8abca3bcd62e1ae46
  languageName: node
  linkType: hard

"@babel/plugin-transform-dynamic-import@npm:^7.27.1":
  version: 7.27.1
  resolution: "@babel/plugin-transform-dynamic-import@npm:7.27.1"
  dependencies:
    "@babel/helper-plugin-utils": "npm:^7.27.1"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 10/7a9fbc8d17148b7f11a1d1ca3990d2c2cd44bd08a45dcaf14f20a017721235b9044b20e6168b6940282bb1b48fb78e6afbdfb9dd9d82fde614e15baa7d579932
  languageName: node
  linkType: hard

"@babel/plugin-transform-explicit-resource-management@npm:^7.28.0":
  version: 7.28.0
  resolution: "@babel/plugin-transform-explicit-resource-management@npm:7.28.0"
  dependencies:
    "@babel/helper-plugin-utils": "npm:^7.27.1"
    "@babel/plugin-transform-destructuring": "npm:^7.28.0"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 10/93d7835160bf8623c7b7072898046c9a2a46cf911f38fa2a002de40a11045a65bf9c1663c98f2e4e04615037f63391832c20b45d7bc26a16d39a97995d0669bc
  languageName: node
  linkType: hard

"@babel/plugin-transform-exponentiation-operator@npm:^7.27.1":
  version: 7.27.1
  resolution: "@babel/plugin-transform-exponentiation-operator@npm:7.27.1"
  dependencies:
    "@babel/helper-plugin-utils": "npm:^7.27.1"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 10/dbbedd24724c2d590ef59d32cb1fef34e99daba41c5b621f9f4c4da23e15c2bb4b1e3d954c314645016391404cf00f1e4ddec8f1f7891438bcde9aaf16e16ee0
  languageName: node
  linkType: hard

"@babel/plugin-transform-export-namespace-from@npm:^7.27.1":
  version: 7.27.1
  resolution: "@babel/plugin-transform-export-namespace-from@npm:7.27.1"
  dependencies:
    "@babel/helper-plugin-utils": "npm:^7.27.1"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 10/85082923eca317094f08f4953d8ea2a6558b3117826c0b740676983902b7236df1f4213ad844cb38c2dae104753dbe8f1cc51f01567835d476d32f5f544a4385
  languageName: node
  linkType: hard

"@babel/plugin-transform-for-of@npm:^7.27.1":
  version: 7.27.1
  resolution: "@babel/plugin-transform-for-of@npm:7.27.1"
  dependencies:
    "@babel/helper-plugin-utils": "npm:^7.27.1"
    "@babel/helper-skip-transparent-expression-wrappers": "npm:^7.27.1"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 10/705c591d17ef263c309bba8c38e20655e8e74ff7fd21883a9cdaf5bf1df42d724383ad3d88ac01f42926e15b1e1e66f2f7f8c4e87de955afffa290d52314b019
  languageName: node
  linkType: hard

"@babel/plugin-transform-function-name@npm:^7.27.1":
  version: 7.27.1
  resolution: "@babel/plugin-transform-function-name@npm:7.27.1"
  dependencies:
    "@babel/helper-compilation-targets": "npm:^7.27.1"
    "@babel/helper-plugin-utils": "npm:^7.27.1"
    "@babel/traverse": "npm:^7.27.1"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 10/26a2a183c3c52a96495967420a64afc5a09f743a230272a131668abf23001e393afa6371e6f8e6c60f4182bea210ed31d1caf866452d91009c1daac345a52f23
  languageName: node
  linkType: hard

"@babel/plugin-transform-json-strings@npm:^7.27.1":
  version: 7.27.1
  resolution: "@babel/plugin-transform-json-strings@npm:7.27.1"
  dependencies:
    "@babel/helper-plugin-utils": "npm:^7.27.1"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 10/2c05a02f63b49f47069271b3405a66c3c8038de5b995b0700b1bd9a5e2bb3e67abd01e4604629302a521f4d8122a4233944aefa16559fd4373d256cc5d3da57f
  languageName: node
  linkType: hard

"@babel/plugin-transform-literals@npm:^7.27.1":
  version: 7.27.1
  resolution: "@babel/plugin-transform-literals@npm:7.27.1"
  dependencies:
    "@babel/helper-plugin-utils": "npm:^7.27.1"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 10/0a76d12ab19f32dd139964aea7da48cecdb7de0b75e207e576f0f700121fe92367d788f328bf4fb44b8261a0f605c97b44e62ae61cddbb67b14e94c88b411f95
  languageName: node
  linkType: hard

"@babel/plugin-transform-logical-assignment-operators@npm:^7.27.1":
  version: 7.27.1
  resolution: "@babel/plugin-transform-logical-assignment-operators@npm:7.27.1"
  dependencies:
    "@babel/helper-plugin-utils": "npm:^7.27.1"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 10/2757955d81d65cc4701c17b83720745f6858f7a1d1d58117e379c204f47adbeb066b778596b6168bdbf4a22c229aab595d79a9abc261d0c6bfd62d4419466e73
  languageName: node
  linkType: hard

"@babel/plugin-transform-member-expression-literals@npm:^7.27.1":
  version: 7.27.1
  resolution: "@babel/plugin-transform-member-expression-literals@npm:7.27.1"
  dependencies:
    "@babel/helper-plugin-utils": "npm:^7.27.1"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 10/804121430a6dcd431e6ffe99c6d1fbbc44b43478113b79c677629e7f877b4f78a06b69c6bfb2747fd84ee91879fe2eb32e4620b53124603086cf5b727593ebe8
  languageName: node
  linkType: hard

"@babel/plugin-transform-modules-amd@npm:^7.27.1":
  version: 7.27.1
  resolution: "@babel/plugin-transform-modules-amd@npm:7.27.1"
  dependencies:
    "@babel/helper-module-transforms": "npm:^7.27.1"
    "@babel/helper-plugin-utils": "npm:^7.27.1"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 10/5ca9257981f2bbddd9dccf9126f1368de1cb335e7a5ff5cca9282266825af5b18b5f06c144320dcf5d2a200d2b53b6d22d9b801a55dc0509ab5a5838af7e61b7
  languageName: node
  linkType: hard

"@babel/plugin-transform-modules-commonjs@npm:^7.27.1":
  version: 7.27.1
  resolution: "@babel/plugin-transform-modules-commonjs@npm:7.27.1"
  dependencies:
    "@babel/helper-module-transforms": "npm:^7.27.1"
    "@babel/helper-plugin-utils": "npm:^7.27.1"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 10/9059243a977bc1f13e3dccfc6feb6508890e7c7bb191f7eb56626b20672b4b12338051ca835ab55426875a473181502c8f35b4df58ba251bef63b25866d995fe
  languageName: node
  linkType: hard

"@babel/plugin-transform-modules-systemjs@npm:^7.27.1":
  version: 7.27.1
  resolution: "@babel/plugin-transform-modules-systemjs@npm:7.27.1"
  dependencies:
    "@babel/helper-module-transforms": "npm:^7.27.1"
    "@babel/helper-plugin-utils": "npm:^7.27.1"
    "@babel/helper-validator-identifier": "npm:^7.27.1"
    "@babel/traverse": "npm:^7.27.1"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 10/06d7bf76ac4688a36ae8e8d2dde1c3b8bab4594362132b74a00d5a32e6716944d68911b9bc53df60e59f4f9c7f1796525503ce3e3eed42f842d7775ccdfd836e
  languageName: node
  linkType: hard

"@babel/plugin-transform-modules-umd@npm:^7.27.1":
  version: 7.27.1
  resolution: "@babel/plugin-transform-modules-umd@npm:7.27.1"
  dependencies:
    "@babel/helper-module-transforms": "npm:^7.27.1"
    "@babel/helper-plugin-utils": "npm:^7.27.1"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 10/7388932863b4ee01f177eb6c2e2df9e2312005e43ada99897624d5565db4b9cef1e30aa7ad2c79bbe5373f284cfcddea98d8fe212714a24c6aba223272163058
  languageName: node
  linkType: hard

"@babel/plugin-transform-named-capturing-groups-regex@npm:^7.27.1":
  version: 7.27.1
  resolution: "@babel/plugin-transform-named-capturing-groups-regex@npm:7.27.1"
  dependencies:
    "@babel/helper-create-regexp-features-plugin": "npm:^7.27.1"
    "@babel/helper-plugin-utils": "npm:^7.27.1"
  peerDependencies:
    "@babel/core": ^7.0.0
  checksum: 10/a711c92d9753df26cefc1792481e5cbff4fe4f32b383d76b25e36fa865d8023b1b9aa6338cf18f5c0e864c71a7fbe8115e840872ccd61a914d9953849c68de7d
  languageName: node
  linkType: hard

"@babel/plugin-transform-new-target@npm:^7.27.1":
  version: 7.27.1
  resolution: "@babel/plugin-transform-new-target@npm:7.27.1"
  dependencies:
    "@babel/helper-plugin-utils": "npm:^7.27.1"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 10/620d78ee476ae70960989e477dc86031ffa3d554b1b1999e6ec95261629f7a13e5a7b98579c63a009f9fdf14def027db57de1f0ae1f06fb6eaed8908ff65cf68
  languageName: node
  linkType: hard

"@babel/plugin-transform-nullish-coalescing-operator@npm:^7.27.1":
  version: 7.27.1
  resolution: "@babel/plugin-transform-nullish-coalescing-operator@npm:7.27.1"
  dependencies:
    "@babel/helper-plugin-utils": "npm:^7.27.1"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 10/15333f4888ffedc449a2a21a0b1ca7983e089f43faa00cfb71d2466e20221a5fd979cdb1a3f57bc20fc62c67bd3ff3dde054133fb6324a58be8f64d20aefacd2
  languageName: node
  linkType: hard

"@babel/plugin-transform-numeric-separator@npm:^7.27.1":
  version: 7.27.1
  resolution: "@babel/plugin-transform-numeric-separator@npm:7.27.1"
  dependencies:
    "@babel/helper-plugin-utils": "npm:^7.27.1"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 10/049b958911de86d32408cd78017940a207e49c054ae9534ab53a32a57122cc592c0aae3c166d6f29bd1a7d75cc779d71883582dd76cb28b2fbb493e842d8ffca
  languageName: node
  linkType: hard

"@babel/plugin-transform-object-rest-spread@npm:^7.28.0":
  version: 7.28.0
  resolution: "@babel/plugin-transform-object-rest-spread@npm:7.28.0"
  dependencies:
    "@babel/helper-compilation-targets": "npm:^7.27.2"
    "@babel/helper-plugin-utils": "npm:^7.27.1"
    "@babel/plugin-transform-destructuring": "npm:^7.28.0"
    "@babel/plugin-transform-parameters": "npm:^7.27.7"
    "@babel/traverse": "npm:^7.28.0"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 10/55d37dbc0d5d47db860b7cc9fe5e3660d83108113fc3f2a7daecb95c20d4046a70247777969006f7db8fb2005eeeda719b9ff21e9f6d43355d0a62fc41b5880e
  languageName: node
  linkType: hard

"@babel/plugin-transform-object-super@npm:^7.27.1":
  version: 7.27.1
  resolution: "@babel/plugin-transform-object-super@npm:7.27.1"
  dependencies:
    "@babel/helper-plugin-utils": "npm:^7.27.1"
    "@babel/helper-replace-supers": "npm:^7.27.1"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 10/46b819cb9a6cd3cfefe42d07875fee414f18d5e66040366ae856116db560ad4e16f3899a0a7fddd6773e0d1458444f94b208b67c0e3b6977a27ea17a5c13dbf6
  languageName: node
  linkType: hard

"@babel/plugin-transform-optional-catch-binding@npm:^7.27.1":
  version: 7.27.1
  resolution: "@babel/plugin-transform-optional-catch-binding@npm:7.27.1"
  dependencies:
    "@babel/helper-plugin-utils": "npm:^7.27.1"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 10/f4356b04cf21a98480f9788ea50f1f13ee88e89bb6393ba4b84d1f39a4a84c7928c9a4328e8f4c5b6deb218da68a8fd17bf4f46faec7653ddc20ffaaa5ba49f4
  languageName: node
  linkType: hard

"@babel/plugin-transform-optional-chaining@npm:^7.27.1":
  version: 7.27.1
  resolution: "@babel/plugin-transform-optional-chaining@npm:7.27.1"
  dependencies:
    "@babel/helper-plugin-utils": "npm:^7.27.1"
    "@babel/helper-skip-transparent-expression-wrappers": "npm:^7.27.1"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 10/34b0f96400c259a2722740d17a001fe45f78d8ff052c40e29db2e79173be72c1cfe8d9681067e3f5da3989e4a557402df5c982c024c18257587a41e022f95640
  languageName: node
  linkType: hard

"@babel/plugin-transform-parameters@npm:^7.27.7":
  version: 7.27.7
  resolution: "@babel/plugin-transform-parameters@npm:7.27.7"
  dependencies:
    "@babel/helper-plugin-utils": "npm:^7.27.1"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 10/ba0aa8c977a03bf83030668f64c1d721e4e82d8cce89cdde75a2755862b79dbe9e7f58ca955e68c721fd494d6ee3826e46efad3fbf0855fcc92cb269477b4777
  languageName: node
  linkType: hard

"@babel/plugin-transform-private-methods@npm:^7.27.1":
  version: 7.27.1
  resolution: "@babel/plugin-transform-private-methods@npm:7.27.1"
  dependencies:
    "@babel/helper-create-class-features-plugin": "npm:^7.27.1"
    "@babel/helper-plugin-utils": "npm:^7.27.1"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 10/c76f8f6056946466116e67eb9d8014a2d748ade2062636ab82045c1dac9c233aff10e597777bc5af6f26428beb845ceb41b95007abef7d0484da95789da56662
  languageName: node
  linkType: hard

"@babel/plugin-transform-private-property-in-object@npm:^7.27.1":
  version: 7.27.1
  resolution: "@babel/plugin-transform-private-property-in-object@npm:7.27.1"
  dependencies:
    "@babel/helper-annotate-as-pure": "npm:^7.27.1"
    "@babel/helper-create-class-features-plugin": "npm:^7.27.1"
    "@babel/helper-plugin-utils": "npm:^7.27.1"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 10/d4466d42a02c5a318d9d7b8102969fd032b17ff044918dfd462d5cc49bd11f5773ee0794781702afdf4727ba11e9be6cbea1e396bc0a7307761bb9a56399012a
  languageName: node
  linkType: hard

"@babel/plugin-transform-property-literals@npm:^7.27.1":
  version: 7.27.1
  resolution: "@babel/plugin-transform-property-literals@npm:7.27.1"
  dependencies:
    "@babel/helper-plugin-utils": "npm:^7.27.1"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 10/7caec27d5ed8870895c9faf4f71def72745d69da0d8e77903146a4e135fd7bed5778f5f9cebb36c5fba86338e6194dd67a08c033fc84b4299b7eceab6d9630cb
  languageName: node
  linkType: hard

"@babel/plugin-transform-regenerator@npm:^7.28.3":
  version: 7.28.3
  resolution: "@babel/plugin-transform-regenerator@npm:7.28.3"
  dependencies:
    "@babel/helper-plugin-utils": "npm:^7.27.1"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 10/f95e929d41f62d27d390e4b53010df4ca4615738646596a523e62b88d13037433c1f752baef0c53c1e2e784a6d9ec4bb50686e01c55f50b53e65f67abd400963
  languageName: node
  linkType: hard

"@babel/plugin-transform-regexp-modifiers@npm:^7.27.1":
  version: 7.27.1
  resolution: "@babel/plugin-transform-regexp-modifiers@npm:7.27.1"
  dependencies:
    "@babel/helper-create-regexp-features-plugin": "npm:^7.27.1"
    "@babel/helper-plugin-utils": "npm:^7.27.1"
  peerDependencies:
    "@babel/core": ^7.0.0
  checksum: 10/f6cb385fe0e798bff7e9b20cf5912bf40e180895ff3610b1ccdce260f3c20daaebb3a99dc087c8168a99151cd3e16b94f4689fd5a4b01cf1834b45c133e620b2
  languageName: node
  linkType: hard

"@babel/plugin-transform-reserved-words@npm:^7.27.1":
  version: 7.27.1
  resolution: "@babel/plugin-transform-reserved-words@npm:7.27.1"
  dependencies:
    "@babel/helper-plugin-utils": "npm:^7.27.1"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 10/dea0b66742d2863b369c06c053e11e15ba785892ea19cccf7aef3c1bdaa38b6ab082e19984c5ea7810d275d9445c5400fcc385ad71ce707ed9256fadb102af3b
  languageName: node
  linkType: hard

"@babel/plugin-transform-shorthand-properties@npm:^7.27.1":
  version: 7.27.1
  resolution: "@babel/plugin-transform-shorthand-properties@npm:7.27.1"
  dependencies:
    "@babel/helper-plugin-utils": "npm:^7.27.1"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 10/fbba6e2aef0b69681acb68202aa249c0598e470cc0853d7ff5bd0171fd6a7ec31d77cfabcce9df6360fc8349eded7e4a65218c32551bd3fc0caaa1ac899ac6d4
  languageName: node
  linkType: hard

"@babel/plugin-transform-spread@npm:^7.27.1":
  version: 7.27.1
  resolution: "@babel/plugin-transform-spread@npm:7.27.1"
  dependencies:
    "@babel/helper-plugin-utils": "npm:^7.27.1"
    "@babel/helper-skip-transparent-expression-wrappers": "npm:^7.27.1"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 10/3edd28b07e1951f32aa2d380d9a0e0ed408c64a5cea2921d02308541042aca18f146b3a61e82e534d4d61cb3225dbc847f4f063aedfff6230b1a41282e95e8a2
  languageName: node
  linkType: hard

"@babel/plugin-transform-sticky-regex@npm:^7.27.1":
  version: 7.27.1
  resolution: "@babel/plugin-transform-sticky-regex@npm:7.27.1"
  dependencies:
    "@babel/helper-plugin-utils": "npm:^7.27.1"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 10/e1414a502efba92c7974681767e365a8cda6c5e9e5f33472a9eaa0ce2e75cea0a9bef881ff8dda37c7810ad902f98d3c00ead92a3ac3b73a79d011df85b5a189
  languageName: node
  linkType: hard

"@babel/plugin-transform-template-literals@npm:^7.27.1":
  version: 7.27.1
  resolution: "@babel/plugin-transform-template-literals@npm:7.27.1"
  dependencies:
    "@babel/helper-plugin-utils": "npm:^7.27.1"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 10/93aad782503b691faef7c0893372d5243df3219b07f1f22cfc32c104af6a2e7acd6102c128439eab15336d048f1b214ca134b87b0630d8cd568bf447f78b25ce
  languageName: node
  linkType: hard

"@babel/plugin-transform-typeof-symbol@npm:^7.27.1":
  version: 7.27.1
  resolution: "@babel/plugin-transform-typeof-symbol@npm:7.27.1"
  dependencies:
    "@babel/helper-plugin-utils": "npm:^7.27.1"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 10/812d736402a6f9313b86b8adf36740394400be7a09c48e51ee45ab4a383a3f46fc618d656dd12e44934665e42ae71cf143e25b95491b699ef7c737950dbdb862
  languageName: node
  linkType: hard

"@babel/plugin-transform-typescript@npm:^7.27.1":
  version: 7.28.0
  resolution: "@babel/plugin-transform-typescript@npm:7.28.0"
  dependencies:
    "@babel/helper-annotate-as-pure": "npm:^7.27.3"
    "@babel/helper-create-class-features-plugin": "npm:^7.27.1"
    "@babel/helper-plugin-utils": "npm:^7.27.1"
    "@babel/helper-skip-transparent-expression-wrappers": "npm:^7.27.1"
    "@babel/plugin-syntax-typescript": "npm:^7.27.1"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 10/5ad7aae0e900974585c7e0d0ec08bde8cd70a31a9e79f5c9ddadb4f8f6207cb86a5882181c2b262b0fe27558e9f9743306259911bc1445635ec58dd96613cef4
  languageName: node
  linkType: hard

"@babel/plugin-transform-unicode-escapes@npm:^7.27.1":
  version: 7.27.1
  resolution: "@babel/plugin-transform-unicode-escapes@npm:7.27.1"
  dependencies:
    "@babel/helper-plugin-utils": "npm:^7.27.1"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 10/87b9e49dee4ab6e78f4cdcdbdd837d7784f02868a96bfc206c8dbb17dd85db161b5a0ecbe95b19a42e8aea0ce57e80249e1facbf9221d7f4114d52c3b9136c9e
  languageName: node
  linkType: hard

"@babel/plugin-transform-unicode-property-regex@npm:^7.27.1":
  version: 7.27.1
  resolution: "@babel/plugin-transform-unicode-property-regex@npm:7.27.1"
  dependencies:
    "@babel/helper-create-regexp-features-plugin": "npm:^7.27.1"
    "@babel/helper-plugin-utils": "npm:^7.27.1"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 10/5d99c89537d1ebaac3f526c04b162cf95a47d363d4829f78c6701a2c06ab78a48da66a94f853f85f44a3d72153410ba923e072bed4b7166fa097f503eb14131d
  languageName: node
  linkType: hard

"@babel/plugin-transform-unicode-regex@npm:^7.27.1":
  version: 7.27.1
  resolution: "@babel/plugin-transform-unicode-regex@npm:7.27.1"
  dependencies:
    "@babel/helper-create-regexp-features-plugin": "npm:^7.27.1"
    "@babel/helper-plugin-utils": "npm:^7.27.1"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 10/a34d89a2b75fb78e66d97c3dc90d4877f7e31f43316b52176f95a5dee20e9bb56ecf158eafc42a001676ddf7b393d9e67650bad6b32f5405780f25fb83cd68e3
  languageName: node
  linkType: hard

"@babel/plugin-transform-unicode-sets-regex@npm:^7.27.1":
  version: 7.27.1
  resolution: "@babel/plugin-transform-unicode-sets-regex@npm:7.27.1"
  dependencies:
    "@babel/helper-create-regexp-features-plugin": "npm:^7.27.1"
    "@babel/helper-plugin-utils": "npm:^7.27.1"
  peerDependencies:
    "@babel/core": ^7.0.0
  checksum: 10/295126074c7388ab05c82ef3ed0907a1ee4666bbdd763477ead9aba6eb2c74bdf65669416861ac93d337a4a27640963bb214acadc2697275ce95aab14868d57f
  languageName: node
  linkType: hard

"@babel/preset-env@npm:7.28.3":
  version: 7.28.3
  resolution: "@babel/preset-env@npm:7.28.3"
  dependencies:
    "@babel/compat-data": "npm:^7.28.0"
    "@babel/helper-compilation-targets": "npm:^7.27.2"
    "@babel/helper-plugin-utils": "npm:^7.27.1"
    "@babel/helper-validator-option": "npm:^7.27.1"
    "@babel/plugin-bugfix-firefox-class-in-computed-class-key": "npm:^7.27.1"
    "@babel/plugin-bugfix-safari-class-field-initializer-scope": "npm:^7.27.1"
    "@babel/plugin-bugfix-safari-id-destructuring-collision-in-function-expression": "npm:^7.27.1"
    "@babel/plugin-bugfix-v8-spread-parameters-in-optional-chaining": "npm:^7.27.1"
    "@babel/plugin-bugfix-v8-static-class-fields-redefine-readonly": "npm:^7.28.3"
    "@babel/plugin-proposal-private-property-in-object": "npm:7.21.0-placeholder-for-preset-env.2"
    "@babel/plugin-syntax-import-assertions": "npm:^7.27.1"
    "@babel/plugin-syntax-import-attributes": "npm:^7.27.1"
    "@babel/plugin-syntax-unicode-sets-regex": "npm:^7.18.6"
    "@babel/plugin-transform-arrow-functions": "npm:^7.27.1"
    "@babel/plugin-transform-async-generator-functions": "npm:^7.28.0"
    "@babel/plugin-transform-async-to-generator": "npm:^7.27.1"
    "@babel/plugin-transform-block-scoped-functions": "npm:^7.27.1"
    "@babel/plugin-transform-block-scoping": "npm:^7.28.0"
    "@babel/plugin-transform-class-properties": "npm:^7.27.1"
    "@babel/plugin-transform-class-static-block": "npm:^7.28.3"
    "@babel/plugin-transform-classes": "npm:^7.28.3"
    "@babel/plugin-transform-computed-properties": "npm:^7.27.1"
    "@babel/plugin-transform-destructuring": "npm:^7.28.0"
    "@babel/plugin-transform-dotall-regex": "npm:^7.27.1"
    "@babel/plugin-transform-duplicate-keys": "npm:^7.27.1"
    "@babel/plugin-transform-duplicate-named-capturing-groups-regex": "npm:^7.27.1"
    "@babel/plugin-transform-dynamic-import": "npm:^7.27.1"
    "@babel/plugin-transform-explicit-resource-management": "npm:^7.28.0"
    "@babel/plugin-transform-exponentiation-operator": "npm:^7.27.1"
    "@babel/plugin-transform-export-namespace-from": "npm:^7.27.1"
    "@babel/plugin-transform-for-of": "npm:^7.27.1"
    "@babel/plugin-transform-function-name": "npm:^7.27.1"
    "@babel/plugin-transform-json-strings": "npm:^7.27.1"
    "@babel/plugin-transform-literals": "npm:^7.27.1"
    "@babel/plugin-transform-logical-assignment-operators": "npm:^7.27.1"
    "@babel/plugin-transform-member-expression-literals": "npm:^7.27.1"
    "@babel/plugin-transform-modules-amd": "npm:^7.27.1"
    "@babel/plugin-transform-modules-commonjs": "npm:^7.27.1"
    "@babel/plugin-transform-modules-systemjs": "npm:^7.27.1"
    "@babel/plugin-transform-modules-umd": "npm:^7.27.1"
    "@babel/plugin-transform-named-capturing-groups-regex": "npm:^7.27.1"
    "@babel/plugin-transform-new-target": "npm:^7.27.1"
    "@babel/plugin-transform-nullish-coalescing-operator": "npm:^7.27.1"
    "@babel/plugin-transform-numeric-separator": "npm:^7.27.1"
    "@babel/plugin-transform-object-rest-spread": "npm:^7.28.0"
    "@babel/plugin-transform-object-super": "npm:^7.27.1"
    "@babel/plugin-transform-optional-catch-binding": "npm:^7.27.1"
    "@babel/plugin-transform-optional-chaining": "npm:^7.27.1"
    "@babel/plugin-transform-parameters": "npm:^7.27.7"
    "@babel/plugin-transform-private-methods": "npm:^7.27.1"
    "@babel/plugin-transform-private-property-in-object": "npm:^7.27.1"
    "@babel/plugin-transform-property-literals": "npm:^7.27.1"
    "@babel/plugin-transform-regenerator": "npm:^7.28.3"
    "@babel/plugin-transform-regexp-modifiers": "npm:^7.27.1"
    "@babel/plugin-transform-reserved-words": "npm:^7.27.1"
    "@babel/plugin-transform-shorthand-properties": "npm:^7.27.1"
    "@babel/plugin-transform-spread": "npm:^7.27.1"
    "@babel/plugin-transform-sticky-regex": "npm:^7.27.1"
    "@babel/plugin-transform-template-literals": "npm:^7.27.1"
    "@babel/plugin-transform-typeof-symbol": "npm:^7.27.1"
    "@babel/plugin-transform-unicode-escapes": "npm:^7.27.1"
    "@babel/plugin-transform-unicode-property-regex": "npm:^7.27.1"
    "@babel/plugin-transform-unicode-regex": "npm:^7.27.1"
    "@babel/plugin-transform-unicode-sets-regex": "npm:^7.27.1"
    "@babel/preset-modules": "npm:0.1.6-no-external-plugins"
    babel-plugin-polyfill-corejs2: "npm:^0.4.14"
    babel-plugin-polyfill-corejs3: "npm:^0.13.0"
    babel-plugin-polyfill-regenerator: "npm:^0.6.5"
    core-js-compat: "npm:^3.43.0"
    semver: "npm:^6.3.1"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 10/b09991276a5ea4f2f95077bb451420f683e19d59405bc1fbbb392bb3571592edc922daac4eaa50b2b407c0b24c4e1e9df0f76738c3c573dac4e6bcf028daa8c5
  languageName: node
  linkType: hard

"@babel/preset-modules@npm:0.1.6-no-external-plugins":
  version: 0.1.6-no-external-plugins
  resolution: "@babel/preset-modules@npm:0.1.6-no-external-plugins"
  dependencies:
    "@babel/helper-plugin-utils": "npm:^7.0.0"
    "@babel/types": "npm:^7.4.4"
    esutils: "npm:^2.0.2"
  peerDependencies:
    "@babel/core": ^7.0.0-0 || ^8.0.0-0 <8.0.0
  checksum: 10/039aba98a697b920d6440c622aaa6104bb6076d65356b29dad4b3e6627ec0354da44f9621bafbeefd052cd4ac4d7f88c9a2ab094efcb50963cb352781d0c6428
  languageName: node
  linkType: hard

"@babel/preset-typescript@npm:^7.27.1":
  version: 7.27.1
  resolution: "@babel/preset-typescript@npm:7.27.1"
  dependencies:
    "@babel/helper-plugin-utils": "npm:^7.27.1"
    "@babel/helper-validator-option": "npm:^7.27.1"
    "@babel/plugin-syntax-jsx": "npm:^7.27.1"
    "@babel/plugin-transform-modules-commonjs": "npm:^7.27.1"
    "@babel/plugin-transform-typescript": "npm:^7.27.1"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 10/9d8e75326b3c93fa016ba7aada652800fc77bc05fcc181888700a049935e8cf1284b549de18a5d62ef3591d02f097ea6de1111f7d71a991aaf36ba74657bd145
  languageName: node
  linkType: hard

"@babel/runtime@npm:^7.12.5":
  version: 7.28.3
  resolution: "@babel/runtime@npm:7.28.3"
  checksum: 10/f2415e4dbface7496f6fc561d640b44be203071fb0dfb63fbe338c7d2d2047419cb054ef13d1ebb8fc11e35d2b55aa3045def4b985e8b82aea5d7e58e1133e52
  languageName: node
  linkType: hard

"@babel/template@npm:^7.27.1, @babel/template@npm:^7.27.2":
  version: 7.27.2
  resolution: "@babel/template@npm:7.27.2"
  dependencies:
    "@babel/code-frame": "npm:^7.27.1"
    "@babel/parser": "npm:^7.27.2"
    "@babel/types": "npm:^7.27.1"
  checksum: 10/fed15a84beb0b9340e5f81566600dbee5eccd92e4b9cc42a944359b1aa1082373391d9d5fc3656981dff27233ec935d0bc96453cf507f60a4b079463999244d8
  languageName: node
  linkType: hard

"@babel/traverse@npm:^7.23.7, @babel/traverse@npm:^7.27.1, @babel/traverse@npm:^7.27.7, @babel/traverse@npm:^7.28.0, @babel/traverse@npm:^7.28.3":
  version: 7.28.3
  resolution: "@babel/traverse@npm:7.28.3"
  dependencies:
    "@babel/code-frame": "npm:^7.27.1"
    "@babel/generator": "npm:^7.28.3"
    "@babel/helper-globals": "npm:^7.28.0"
    "@babel/parser": "npm:^7.28.3"
    "@babel/template": "npm:^7.27.2"
    "@babel/types": "npm:^7.28.2"
    debug: "npm:^4.3.1"
  checksum: 10/fe521591b719db010a89d9a39874386d0d67b79ee7e947eee7a8ef944bd3277cd92f3b39723fc9790dc4fb77f26b818db95712e147c599b9c4d98921eb4bc70b
  languageName: node
  linkType: hard

"@babel/types@npm:^7.21.3, @babel/types@npm:^7.23.6, @babel/types@npm:^7.25.4, @babel/types@npm:^7.27.1, @babel/types@npm:^7.27.3, @babel/types@npm:^7.27.7, @babel/types@npm:^7.28.2, @babel/types@npm:^7.4.4":
  version: 7.28.2
  resolution: "@babel/types@npm:7.28.2"
  dependencies:
    "@babel/helper-string-parser": "npm:^7.27.1"
    "@babel/helper-validator-identifier": "npm:^7.27.1"
  checksum: 10/a8de404a2e3109651f346d892dc020ce2c82046068f4ce24de7f487738dfbfa7bd716b35f1dcd6d6c32dde96208dc74a56b7f56a2c0bcb5af0ddc56cbee13533
  languageName: node
  linkType: hard

"@bcoe/v8-coverage@npm:^1.0.2":
  version: 1.0.2
  resolution: "@bcoe/v8-coverage@npm:1.0.2"
  checksum: 10/46600b2dde460269b07a8e4f12b72e418eae1337b85c979f43af3336c9a1c65b04e42508ab6b245f1e0e3c64328e1c38d8cd733e4a7cebc4fbf9cf65c6e59937
  languageName: node
  linkType: hard

"@csstools/color-helpers@npm:^5.1.0":
  version: 5.1.0
  resolution: "@csstools/color-helpers@npm:5.1.0"
  checksum: 10/0138b3d5ccbe77aeccf6721fd008a53523c70e932f0c82dca24a1277ca780447e1d8357da47512ebf96358476f8764de57002f3e491920d67e69202f5a74c383
  languageName: node
  linkType: hard

"@csstools/css-calc@npm:^2.1.3, @csstools/css-calc@npm:^2.1.4":
  version: 2.1.4
  resolution: "@csstools/css-calc@npm:2.1.4"
  peerDependencies:
    "@csstools/css-parser-algorithms": ^3.0.5
    "@csstools/css-tokenizer": ^3.0.4
  checksum: 10/06975b650c0f44c60eeb7afdb3fd236f2dd607b2c622e0bc908d3f54de39eb84e0692833320d03dac04bd6c1ab0154aa3fa0dd442bd9e5f917cf14d8e2ba8d74
  languageName: node
  linkType: hard

"@csstools/css-color-parser@npm:^3.0.9":
  version: 3.1.0
  resolution: "@csstools/css-color-parser@npm:3.1.0"
  dependencies:
    "@csstools/color-helpers": "npm:^5.1.0"
    "@csstools/css-calc": "npm:^2.1.4"
  peerDependencies:
    "@csstools/css-parser-algorithms": ^3.0.5
    "@csstools/css-tokenizer": ^3.0.4
  checksum: 10/4741095fdc4501e8e7ada4ed14fbf9dbbe6fea9b989818790ebca15657c29c62defbebacf18592cde2aa638a1d098bbe86d742d2c84ba932fbc00fac51cb8805
  languageName: node
  linkType: hard

"@csstools/css-parser-algorithms@npm:^3.0.4":
  version: 3.0.5
  resolution: "@csstools/css-parser-algorithms@npm:3.0.5"
  peerDependencies:
    "@csstools/css-tokenizer": ^3.0.4
  checksum: 10/e93083b5cb36a3c1e7a47ce10cf62961d05bd1e4c608bb3ee50186ff740157ab0ec16a3956f7b86251efd10703034d849693201eea858ae904848c68d2d46ada
  languageName: node
  linkType: hard

"@csstools/css-tokenizer@npm:^3.0.3":
  version: 3.0.4
  resolution: "@csstools/css-tokenizer@npm:3.0.4"
  checksum: 10/eb6c84c086312f6bb8758dfe2c85addd7475b0927333c5e39a4d59fb210b9810f8c346972046f95e60a721329cffe98895abe451e51de753ad1ca7a8c24ec65f
  languageName: node
  linkType: hard

"@dimforge/rapier3d-compat@npm:~0.12.0":
  version: 0.12.0
  resolution: "@dimforge/rapier3d-compat@npm:0.12.0"
  checksum: 10/56cd538b59d534d2ed91de3eb7efd804af5defe146f65294e8ce81982a4bd6c27d61c1014c15a77bb5f5eff0de7a12fbc9b16bf895a2ec56ea5496fd80c78e26
  languageName: node
  linkType: hard

"@dodobrands/frontlogger@npm:6.5.1":
  version: 6.5.1
  resolution: "@dodobrands/frontlogger@npm:6.5.1"
  checksum: 10/b585835f256009cfedac59762aa6233c4e6b22a7553bbb71bcd87a6cd23ff08cfb2be231d1d6dbfabc125a2043e04ad3888abbd74081cc9834bccac484c7c3a9
  languageName: node
  linkType: hard

"@dodobrands/react-logger@npm:6.5.1":
  version: 6.5.1
  resolution: "@dodobrands/react-logger@npm:6.5.1"
  dependencies:
    "@dodobrands/frontlogger": "npm:6.5.1"
  peerDependencies:
    react: ^17.0.0 || ^18.0.0
  checksum: 10/28b1eb8599f60a5fb1edeae379ce1988350726ba6b64b53b4234cf96bf4c8d2e9f1e7da16dd819e52111dca11a208caebc96e2c9fdc4f12a89c391dea7115210
  languageName: node
  linkType: hard

"@dodopizza/eslint-plugin@npm:4.1.4":
  version: 4.1.4
  resolution: "@dodopizza/eslint-plugin@npm:4.1.4::__archiveUrl=https%3A%2F%2Fnpm.pkg.github.com%2Fdownload%2F%40dodopizza%2Feslint-plugin%2F4.1.4%2F54efaffe106a7277a6eac88c2b985b19a00695d1"
  dependencies:
    "@eslint/compat": "npm:1.2.8"
    "@eslint/eslintrc": "npm:3.3.1"
    "@feature-sliced/eslint-config": "npm:0.1.1"
    "@stylistic/eslint-plugin": "npm:4.2.0"
    "@typescript-eslint/eslint-plugin": "npm:8.39.0"
    "@typescript-eslint/parser": "npm:8.39.0"
    "@typescript-eslint/types": "npm:8.39.0"
    eslint-config-airbnb: "npm:19.0.4"
    eslint-config-airbnb-typescript: "npm:18.0.0"
    eslint-config-prettier: "npm:10.1.2"
    eslint-import-resolver-typescript: "npm:4.3.2"
    eslint-plugin-boundaries: "npm:5.0.1"
    eslint-plugin-i18next: "npm:6.1.1"
    eslint-plugin-import: "npm:2.31.0"
    eslint-plugin-jest: "npm:28.11.0"
    eslint-plugin-json: "npm:4.0.1"
    eslint-plugin-jsx-a11y: "npm:6.10.2"
    eslint-plugin-optimize-regex: "npm:1.2.1"
    eslint-plugin-prefer-arrow-functions: "npm:3.6.2"
    eslint-plugin-prettier: "npm:5.2.6"
    eslint-plugin-promise: "npm:7.2.1"
    eslint-plugin-react: "npm:7.37.5"
    eslint-plugin-react-hooks: "npm:5.2.0"
    eslint-plugin-simple-import-sort: "npm:12.1.1"
    eslint-plugin-sonarjs: "npm:3.0.2"
    eslint-plugin-storybook: "npm:0.12.0"
    eslint-plugin-styled-components-varname: "npm:1.0.1"
    eslint-plugin-testing-library: "npm:7.1.1"
    eslint-plugin-vitest: "npm:0.5.4"
    postcss: "npm:8.5.3"
    prettier: "npm:3.5.3"
    typescript-eslint: "npm:8.39.0"
  peerDependencies:
    eslint: ">=9.0.0"
    typescript: ">=5.0.0"
  checksum: 10/09435b8e324eceacd20663065643cafc978f57237dcc3707d8fa1491b50ee21f5c5e294f3b1862e5bcdf1e73cc35985622d25659a1b21a9e6145c601672d4772
  languageName: node
  linkType: hard

"@dodopizza/frontend-scripts@npm:3.5.1":
  version: 3.5.1
  resolution: "@dodopizza/frontend-scripts@npm:3.5.1::__archiveUrl=https%3A%2F%2Fnpm.pkg.github.com%2Fdownload%2F%40dodopizza%2Ffrontend-scripts%2F3.5.1%2F52669da925b8bafdc4b49e7e5236d874abdda00a"
  dependencies:
    "@babel/core": "npm:7.28.3"
    "@babel/preset-env": "npm:7.28.3"
    "@optimize-lodash/rollup-plugin": "npm:5.0.2"
    "@rollup/plugin-babel": "npm:6.0.4"
    "@swc/plugin-styled-components": "npm:>=9.0.0"
    "@testing-library/jest-dom": "npm:6.8.0"
    "@vitejs/plugin-react-swc": "npm:^4.0.1"
    "@vitest/coverage-istanbul": "npm:3.2.4"
    "@vitest/coverage-v8": "npm:3.2.4"
    "@vitest/ui": "npm:3.2.4"
    abortcontroller-polyfill: "npm:1.7.8"
    core-js: "npm:3.45.1"
    isomorphic-fetch: "npm:3.0.0"
    jsdom: "npm:26.1.0"
    magic-string: "npm:0.30.17"
    minimist: "npm:1.2.8"
    mockjs: "npm:1.1.0"
    raf: "npm:3.4.1"
    regenerator-runtime: "npm:0.14.1"
    systemjs: "npm:6.15.1"
    terser: "npm:5.43.1"
    vite: "npm:7.1.3"
    vite-plugin-checker: "npm:0.10.2"
    vite-plugin-css-injected-by-js: "npm:3.5.2"
    vite-plugin-dynamic-base: "npm:1.0.0"
    vite-plugin-mkcert: "npm:1.17.8"
    vite-plugin-mock: "npm:3.0.2"
    vite-plugin-svgr: "npm:4.3.0"
    vite-tsconfig-paths: "npm:5.1.4"
    vitest: "npm:3.2.4"
    vitest-preview: "npm:^0.0.1"
  peerDependencies:
    "@testing-library/react": ^14.0.0
    typescript: ^5.0.0
  bin:
    dodo-react-scripts: ./dist/bin/init.js
  checksum: 10/b3f4cb207061a6e89874b05564a7fa09ac15ab8e1fe36b88634c1042e2601fa6d1c19f24774826919b09fbeed199b08e1cedb428d345df7c12141d21ca73cb5f
  languageName: node
  linkType: hard

"@dodopizza/ts-config@npm:1.0.1":
  version: 1.0.1
  resolution: "@dodopizza/ts-config@npm:1.0.1::__archiveUrl=https%3A%2F%2Fnpm.pkg.github.com%2Fdownload%2F%40dodopizza%2Fts-config%2F1.0.1%2Fcdfab1ee2d3d3c269dec9829d0a15a387c9d1009"
  checksum: 10/4de5d01a10e232e990127ad4a9f7c6792525f7f1a50cc367c21c6a983f715fa186a237869688af774e0fc1bde3a4d8ee0d23b16a169544917a198a1d10bc4daa
  languageName: node
  linkType: hard

"@emnapi/core@npm:^1.4.3":
  version: 1.5.0
  resolution: "@emnapi/core@npm:1.5.0"
  dependencies:
    "@emnapi/wasi-threads": "npm:1.1.0"
    tslib: "npm:^2.4.0"
  checksum: 10/b500a69df001580731b0d355298b58832d44ab176937c0db7d10073a396f7a801ebcca10581f125a1cd88af4e6ecd6fbb04b78629cc703a424218b3a36d7bf50
  languageName: node
  linkType: hard

"@emnapi/runtime@npm:^1.4.3":
  version: 1.5.0
  resolution: "@emnapi/runtime@npm:1.5.0"
  dependencies:
    tslib: "npm:^2.4.0"
  checksum: 10/5311ce854306babc77f4bd94c2f973722714a0fab93c126239104ad52dea16a147bfed4c4cff3ca1eb32709607221c25d2f747ae8524cbeb9088058f02ff962b
  languageName: node
  linkType: hard

"@emnapi/wasi-threads@npm:1.1.0":
  version: 1.1.0
  resolution: "@emnapi/wasi-threads@npm:1.1.0"
  dependencies:
    tslib: "npm:^2.4.0"
  checksum: 10/0d557e75262d2f4c95cb2a456ba0785ef61f919ce488c1d76e5e3acfd26e00c753ef928cd80068363e0c166ba8cc0141305daf0f81aad5afcd421f38f11e0f4e
  languageName: node
  linkType: hard

"@emotion/is-prop-valid@npm:1.2.2":
  version: 1.2.2
  resolution: "@emotion/is-prop-valid@npm:1.2.2"
  dependencies:
    "@emotion/memoize": "npm:^0.8.1"
  checksum: 10/0fa3960abfbe845d40cc230ab8c9408e1f33d3c03b321980359911c7212133cdcb0344d249e9dab23342b304567eece7a10ec44b986f7230e0640ba00049dceb
  languageName: node
  linkType: hard

"@emotion/memoize@npm:^0.8.1":
  version: 0.8.1
  resolution: "@emotion/memoize@npm:0.8.1"
  checksum: 10/a19cc01a29fcc97514948eaab4dc34d8272e934466ed87c07f157887406bc318000c69ae6f813a9001c6a225364df04249842a50e692ef7a9873335fbcc141b0
  languageName: node
  linkType: hard

"@emotion/unitless@npm:0.8.1":
  version: 0.8.1
  resolution: "@emotion/unitless@npm:0.8.1"
  checksum: 10/918f73c46ac0b7161e3c341cc07d651ce87e31ab1695e74b12adb7da6bb98dfbff8c69cf68a4e40d9eb3d820ca055dc1267aeb3007927ce88f98b885bf729b63
  languageName: node
  linkType: hard

"@esbuild/aix-ppc64@npm:0.25.9":
  version: 0.25.9
  resolution: "@esbuild/aix-ppc64@npm:0.25.9"
  conditions: os=aix & cpu=ppc64
  languageName: node
  linkType: hard

"@esbuild/android-arm64@npm:0.25.9":
  version: 0.25.9
  resolution: "@esbuild/android-arm64@npm:0.25.9"
  conditions: os=android & cpu=arm64
  languageName: node
  linkType: hard

"@esbuild/android-arm@npm:0.15.18":
  version: 0.15.18
  resolution: "@esbuild/android-arm@npm:0.15.18"
  conditions: os=android & cpu=arm
  languageName: node
  linkType: hard

"@esbuild/android-arm@npm:0.25.9":
  version: 0.25.9
  resolution: "@esbuild/android-arm@npm:0.25.9"
  conditions: os=android & cpu=arm
  languageName: node
  linkType: hard

"@esbuild/android-x64@npm:0.25.9":
  version: 0.25.9
  resolution: "@esbuild/android-x64@npm:0.25.9"
  conditions: os=android & cpu=x64
  languageName: node
  linkType: hard

"@esbuild/darwin-arm64@npm:0.25.9":
  version: 0.25.9
  resolution: "@esbuild/darwin-arm64@npm:0.25.9"
  conditions: os=darwin & cpu=arm64
  languageName: node
  linkType: hard

"@esbuild/darwin-x64@npm:0.25.9":
  version: 0.25.9
  resolution: "@esbuild/darwin-x64@npm:0.25.9"
  conditions: os=darwin & cpu=x64
  languageName: node
  linkType: hard

"@esbuild/freebsd-arm64@npm:0.25.9":
  version: 0.25.9
  resolution: "@esbuild/freebsd-arm64@npm:0.25.9"
  conditions: os=freebsd & cpu=arm64
  languageName: node
  linkType: hard

"@esbuild/freebsd-x64@npm:0.25.9":
  version: 0.25.9
  resolution: "@esbuild/freebsd-x64@npm:0.25.9"
  conditions: os=freebsd & cpu=x64
  languageName: node
  linkType: hard

"@esbuild/linux-arm64@npm:0.25.9":
  version: 0.25.9
  resolution: "@esbuild/linux-arm64@npm:0.25.9"
  conditions: os=linux & cpu=arm64
  languageName: node
  linkType: hard

"@esbuild/linux-arm@npm:0.25.9":
  version: 0.25.9
  resolution: "@esbuild/linux-arm@npm:0.25.9"
  conditions: os=linux & cpu=arm
  languageName: node
  linkType: hard

"@esbuild/linux-ia32@npm:0.25.9":
  version: 0.25.9
  resolution: "@esbuild/linux-ia32@npm:0.25.9"
  conditions: os=linux & cpu=ia32
  languageName: node
  linkType: hard

"@esbuild/linux-loong64@npm:0.15.18":
  version: 0.15.18
  resolution: "@esbuild/linux-loong64@npm:0.15.18"
  conditions: os=linux & cpu=loong64
  languageName: node
  linkType: hard

"@esbuild/linux-loong64@npm:0.25.9":
  version: 0.25.9
  resolution: "@esbuild/linux-loong64@npm:0.25.9"
  conditions: os=linux & cpu=loong64
  languageName: node
  linkType: hard

"@esbuild/linux-mips64el@npm:0.25.9":
  version: 0.25.9
  resolution: "@esbuild/linux-mips64el@npm:0.25.9"
  conditions: os=linux & cpu=mips64el
  languageName: node
  linkType: hard

"@esbuild/linux-ppc64@npm:0.25.9":
  version: 0.25.9
  resolution: "@esbuild/linux-ppc64@npm:0.25.9"
  conditions: os=linux & cpu=ppc64
  languageName: node
  linkType: hard

"@esbuild/linux-riscv64@npm:0.25.9":
  version: 0.25.9
  resolution: "@esbuild/linux-riscv64@npm:0.25.9"
  conditions: os=linux & cpu=riscv64
  languageName: node
  linkType: hard

"@esbuild/linux-s390x@npm:0.25.9":
  version: 0.25.9
  resolution: "@esbuild/linux-s390x@npm:0.25.9"
  conditions: os=linux & cpu=s390x
  languageName: node
  linkType: hard

"@esbuild/linux-x64@npm:0.25.9":
  version: 0.25.9
  resolution: "@esbuild/linux-x64@npm:0.25.9"
  conditions: os=linux & cpu=x64
  languageName: node
  linkType: hard

"@esbuild/netbsd-arm64@npm:0.25.9":
  version: 0.25.9
  resolution: "@esbuild/netbsd-arm64@npm:0.25.9"
  conditions: os=netbsd & cpu=arm64
  languageName: node
  linkType: hard

"@esbuild/netbsd-x64@npm:0.25.9":
  version: 0.25.9
  resolution: "@esbuild/netbsd-x64@npm:0.25.9"
  conditions: os=netbsd & cpu=x64
  languageName: node
  linkType: hard

"@esbuild/openbsd-arm64@npm:0.25.9":
  version: 0.25.9
  resolution: "@esbuild/openbsd-arm64@npm:0.25.9"
  conditions: os=openbsd & cpu=arm64
  languageName: node
  linkType: hard

"@esbuild/openbsd-x64@npm:0.25.9":
  version: 0.25.9
  resolution: "@esbuild/openbsd-x64@npm:0.25.9"
  conditions: os=openbsd & cpu=x64
  languageName: node
  linkType: hard

"@esbuild/openharmony-arm64@npm:0.25.9":
  version: 0.25.9
  resolution: "@esbuild/openharmony-arm64@npm:0.25.9"
  conditions: os=openharmony & cpu=arm64
  languageName: node
  linkType: hard

"@esbuild/sunos-x64@npm:0.25.9":
  version: 0.25.9
  resolution: "@esbuild/sunos-x64@npm:0.25.9"
  conditions: os=sunos & cpu=x64
  languageName: node
  linkType: hard

"@esbuild/win32-arm64@npm:0.25.9":
  version: 0.25.9
  resolution: "@esbuild/win32-arm64@npm:0.25.9"
  conditions: os=win32 & cpu=arm64
  languageName: node
  linkType: hard

"@esbuild/win32-ia32@npm:0.25.9":
  version: 0.25.9
  resolution: "@esbuild/win32-ia32@npm:0.25.9"
  conditions: os=win32 & cpu=ia32
  languageName: node
  linkType: hard

"@esbuild/win32-x64@npm:0.25.9":
  version: 0.25.9
  resolution: "@esbuild/win32-x64@npm:0.25.9"
  conditions: os=win32 & cpu=x64
  languageName: node
  linkType: hard

"@eslint-community/eslint-utils@npm:^4.2.0, @eslint-community/eslint-utils@npm:^4.4.0, @eslint-community/eslint-utils@npm:^4.7.0":
  version: 4.8.0
  resolution: "@eslint-community/eslint-utils@npm:4.8.0"
  dependencies:
    eslint-visitor-keys: "npm:^3.4.3"
  peerDependencies:
    eslint: ^6.0.0 || ^7.0.0 || >=8.0.0
  checksum: 10/d5d51162513c05cc5f055482f97336e813706a12d45cfc6c372c1802da7d30e31204eeb8c0ab9de0abc0fe1a3477c14711c3e43f3d58cedb8ade5d953576fa91
  languageName: node
  linkType: hard

"@eslint-community/regexpp@npm:4.12.1, @eslint-community/regexpp@npm:^4.10.0, @eslint-community/regexpp@npm:^4.12.1, @eslint-community/regexpp@npm:^4.8.0":
  version: 4.12.1
  resolution: "@eslint-community/regexpp@npm:4.12.1"
  checksum: 10/c08f1dd7dd18fbb60bdd0d85820656d1374dd898af9be7f82cb00451313402a22d5e30569c150315b4385907cdbca78c22389b2a72ab78883b3173be317620cc
  languageName: node
  linkType: hard

"@eslint/compat@npm:1.2.8":
  version: 1.2.8
  resolution: "@eslint/compat@npm:1.2.8"
  peerDependencies:
    eslint: ^9.10.0
  peerDependenciesMeta:
    eslint:
      optional: true
  checksum: 10/f344d73e6b46a2303504b55198f9c480be4881ee10d35c12cae57ba1565575157540765764a971afaafd990163b2ed2227f9efa363a6ed4d27ea350dc2847a52
  languageName: node
  linkType: hard

"@eslint/config-array@npm:^0.20.0":
  version: 0.20.1
  resolution: "@eslint/config-array@npm:0.20.1"
  dependencies:
    "@eslint/object-schema": "npm:^2.1.6"
    debug: "npm:^4.3.1"
    minimatch: "npm:^3.1.2"
  checksum: 10/d72cc90f516c5730da5f37fa04aa8ba26ea0d92c7457ee77980902158f844f3483518272ccfe16f273c3313c3bfec8da713d4e51d3da49bdeccd34e919a2b903
  languageName: node
  linkType: hard

"@eslint/config-helpers@npm:^0.2.0":
  version: 0.2.3
  resolution: "@eslint/config-helpers@npm:0.2.3"
  checksum: 10/1f5082248f65555cc666942f7c991a2cfd6821758fb45338f43b28ea0f6b77d0c48b35097400d9b8fe1b4b10150085452e0b8f2d6d9ba17a84e16a6c7e4b341d
  languageName: node
  linkType: hard

"@eslint/core@npm:^0.12.0":
  version: 0.12.0
  resolution: "@eslint/core@npm:0.12.0"
  dependencies:
    "@types/json-schema": "npm:^7.0.15"
  checksum: 10/ee8a2c65ee49af727e167b180a8672739e468ad0b1b9ac52558e61bb120f1a93af23f9e723e0e58f273adfe30ccd98167b59598c7be07440489fa38f669b59ae
  languageName: node
  linkType: hard

"@eslint/core@npm:^0.13.0":
  version: 0.13.0
  resolution: "@eslint/core@npm:0.13.0"
  dependencies:
    "@types/json-schema": "npm:^7.0.15"
  checksum: 10/737fd1c237405b62592e8daa4b7e25b45ab22108bfec65258cabd091d5717b7c9573acea1f27c4ee7198cefc5a0874f5caefe3d9636851227b1f12d28ef52cf2
  languageName: node
  linkType: hard

"@eslint/eslintrc@npm:3.3.1, @eslint/eslintrc@npm:^3.3.1":
  version: 3.3.1
  resolution: "@eslint/eslintrc@npm:3.3.1"
  dependencies:
    ajv: "npm:^6.12.4"
    debug: "npm:^4.3.2"
    espree: "npm:^10.0.1"
    globals: "npm:^14.0.0"
    ignore: "npm:^5.2.0"
    import-fresh: "npm:^3.2.1"
    js-yaml: "npm:^4.1.0"
    minimatch: "npm:^3.1.2"
    strip-json-comments: "npm:^3.1.1"
  checksum: 10/cc240addbab3c5fceaa65b2c8d5d4fd77ddbbf472c2f74f0270b9d33263dc9116840b6099c46b64c9680301146250439b044ed79278a1bcc557da412a4e3c1bb
  languageName: node
  linkType: hard

"@eslint/js@npm:9.24.0":
  version: 9.24.0
  resolution: "@eslint/js@npm:9.24.0"
  checksum: 10/d210114c147a1c1ebfaed5f32734e7c1f8ef551a5ea48ea67f9469668aa4079565ccd038412437bca87515d51dc9e8b8c788473dcf3d08e35dfb27e92cb3ce1b
  languageName: node
  linkType: hard

"@eslint/object-schema@npm:^2.1.6":
  version: 2.1.6
  resolution: "@eslint/object-schema@npm:2.1.6"
  checksum: 10/266085c8d3fa6cd99457fb6350dffb8ee39db9c6baf28dc2b86576657373c92a568aec4bae7d142978e798b74c271696672e103202d47a0c148da39154351ed6
  languageName: node
  linkType: hard

"@eslint/plugin-kit@npm:^0.2.7":
  version: 0.2.8
  resolution: "@eslint/plugin-kit@npm:0.2.8"
  dependencies:
    "@eslint/core": "npm:^0.13.0"
    levn: "npm:^0.4.1"
  checksum: 10/2e7fe7a88ebdbbf805e9e7265347b7dcfb6bf50beec314def997572b2e8ae4a7b9504fb67b1698a70c348a0dd87251d1e9028292a96fd49b58cb5277d88bdea7
  languageName: node
  linkType: hard

"@feature-sliced/eslint-config@npm:0.1.1":
  version: 0.1.1
  resolution: "@feature-sliced/eslint-config@npm:0.1.1"
  peerDependencies:
    eslint-plugin-boundaries: ">=2"
    eslint-plugin-import: ">=2"
  checksum: 10/50f25dac48fea82b53d0edc48c749f7a0753f1ff5d0b88e3e4f7c9b4c23a2d90ccfbba6954fb41448ce31e18b16ce1730261968b4b4890e8ea4374c608f25dbe
  languageName: node
  linkType: hard

"@humanfs/core@npm:^0.19.1":
  version: 0.19.1
  resolution: "@humanfs/core@npm:0.19.1"
  checksum: 10/270d936be483ab5921702623bc74ce394bf12abbf57d9145a69e8a0d1c87eb1c768bd2d93af16c5705041e257e6d9cc7529311f63a1349f3678abc776fc28523
  languageName: node
  linkType: hard

"@humanfs/node@npm:^0.16.6":
  version: 0.16.7
  resolution: "@humanfs/node@npm:0.16.7"
  dependencies:
    "@humanfs/core": "npm:^0.19.1"
    "@humanwhocodes/retry": "npm:^0.4.0"
  checksum: 10/b3633d3dce898592cac515ba5e6693c78e6be92863541d3eaf2c009b10f52b2fa62ff6e6e06f240f2447ddbe7b5f1890bc34e9308470675c876eee207553a08d
  languageName: node
  linkType: hard

"@humanwhocodes/module-importer@npm:^1.0.1":
  version: 1.0.1
  resolution: "@humanwhocodes/module-importer@npm:1.0.1"
  checksum: 10/e993950e346331e5a32eefb27948ecdee2a2c4ab3f072b8f566cd213ef485dd50a3ca497050608db91006f5479e43f91a439aef68d2a313bd3ded06909c7c5b3
  languageName: node
  linkType: hard

"@humanwhocodes/retry@npm:^0.4.0, @humanwhocodes/retry@npm:^0.4.2":
  version: 0.4.3
  resolution: "@humanwhocodes/retry@npm:0.4.3"
  checksum: 10/0b32cfd362bea7a30fbf80bb38dcaf77fee9c2cae477ee80b460871d03590110ac9c77d654f04ec5beaf71b6f6a89851bdf6c1e34ccdf2f686bd86fcd97d9e61
  languageName: node
  linkType: hard

"@isaacs/cliui@npm:^8.0.2":
  version: 8.0.2
  resolution: "@isaacs/cliui@npm:8.0.2"
  dependencies:
    string-width: "npm:^5.1.2"
    string-width-cjs: "npm:string-width@^4.2.0"
    strip-ansi: "npm:^7.0.1"
    strip-ansi-cjs: "npm:strip-ansi@^6.0.1"
    wrap-ansi: "npm:^8.1.0"
    wrap-ansi-cjs: "npm:wrap-ansi@^7.0.0"
  checksum: 10/e9ed5fd27c3aec1095e3a16e0c0cf148d1fee55a38665c35f7b3f86a9b5d00d042ddaabc98e8a1cb7463b9378c15f22a94eb35e99469c201453eb8375191f243
  languageName: node
  linkType: hard

"@isaacs/fs-minipass@npm:^4.0.0":
  version: 4.0.1
  resolution: "@isaacs/fs-minipass@npm:4.0.1"
  dependencies:
    minipass: "npm:^7.0.4"
  checksum: 10/4412e9e6713c89c1e66d80bb0bb5a2a93192f10477623a27d08f228ba0316bb880affabc5bfe7f838f58a34d26c2c190da726e576cdfc18c49a72e89adabdcf5
  languageName: node
  linkType: hard

"@istanbuljs/schema@npm:^0.1.2, @istanbuljs/schema@npm:^0.1.3":
  version: 0.1.3
  resolution: "@istanbuljs/schema@npm:0.1.3"
  checksum: 10/a9b1e49acdf5efc2f5b2359f2df7f90c5c725f2656f16099e8b2cd3a000619ecca9fc48cf693ba789cf0fd989f6e0df6a22bc05574be4223ecdbb7997d04384b
  languageName: node
  linkType: hard

"@jridgewell/gen-mapping@npm:^0.3.12, @jridgewell/gen-mapping@npm:^0.3.5":
  version: 0.3.13
  resolution: "@jridgewell/gen-mapping@npm:0.3.13"
  dependencies:
    "@jridgewell/sourcemap-codec": "npm:^1.5.0"
    "@jridgewell/trace-mapping": "npm:^0.3.24"
  checksum: 10/902f8261dcf450b4af7b93f9656918e02eec80a2169e155000cb2059f90113dd98f3ccf6efc6072cee1dd84cac48cade51da236972d942babc40e4c23da4d62a
  languageName: node
  linkType: hard

"@jridgewell/remapping@npm:^2.3.5":
  version: 2.3.5
  resolution: "@jridgewell/remapping@npm:2.3.5"
  dependencies:
    "@jridgewell/gen-mapping": "npm:^0.3.5"
    "@jridgewell/trace-mapping": "npm:^0.3.24"
  checksum: 10/c2bb01856e65b506d439455f28aceacf130d6c023d1d4e3b48705e88def3571753e1a887daa04b078b562316c92d26ce36408a60534bceca3f830aec88a339ad
  languageName: node
  linkType: hard

"@jridgewell/resolve-uri@npm:^3.1.0":
  version: 3.1.2
  resolution: "@jridgewell/resolve-uri@npm:3.1.2"
  checksum: 10/97106439d750a409c22c8bff822d648f6a71f3aa9bc8e5129efdc36343cd3096ddc4eeb1c62d2fe48e9bdd4db37b05d4646a17114ecebd3bbcacfa2de51c3c1d
  languageName: node
  linkType: hard

"@jridgewell/source-map@npm:^0.3.3":
  version: 0.3.11
  resolution: "@jridgewell/source-map@npm:0.3.11"
  dependencies:
    "@jridgewell/gen-mapping": "npm:^0.3.5"
    "@jridgewell/trace-mapping": "npm:^0.3.25"
  checksum: 10/847f1177d3d133a0966ef61ca29abea0d79788a0652f90ee1893b3da968c190b7e31c3534cc53701179dd6b14601eef3d78644e727e05b1a08c68d281aedc4ba
  languageName: node
  linkType: hard

"@jridgewell/sourcemap-codec@npm:^1.4.14, @jridgewell/sourcemap-codec@npm:^1.5.0, @jridgewell/sourcemap-codec@npm:^1.5.5":
  version: 1.5.5
  resolution: "@jridgewell/sourcemap-codec@npm:1.5.5"
  checksum: 10/5d9d207b462c11e322d71911e55e21a4e2772f71ffe8d6f1221b8eb5ae6774458c1d242f897fb0814e8714ca9a6b498abfa74dfe4f434493342902b1a48b33a5
  languageName: node
  linkType: hard

"@jridgewell/trace-mapping@npm:^0.3.23, @jridgewell/trace-mapping@npm:^0.3.24, @jridgewell/trace-mapping@npm:^0.3.25, @jridgewell/trace-mapping@npm:^0.3.28, @jridgewell/trace-mapping@npm:^0.3.30":
  version: 0.3.30
  resolution: "@jridgewell/trace-mapping@npm:0.3.30"
  dependencies:
    "@jridgewell/resolve-uri": "npm:^3.1.0"
    "@jridgewell/sourcemap-codec": "npm:^1.4.14"
  checksum: 10/f9fabe1122058f4fedc242bdc43fcaacb6b222c6fb712b7904c37704dcb16e50e07ca137ff99043da44292b18a8720296ff97f7703e960678b8ef51d0db4d250
  languageName: node
  linkType: hard

"@napi-rs/wasm-runtime@npm:^0.2.11":
  version: 0.2.12
  resolution: "@napi-rs/wasm-runtime@npm:0.2.12"
  dependencies:
    "@emnapi/core": "npm:^1.4.3"
    "@emnapi/runtime": "npm:^1.4.3"
    "@tybys/wasm-util": "npm:^0.10.0"
  checksum: 10/5fd518182427980c28bc724adf06c5f32f9a8915763ef560b5f7d73607d30cd15ac86d0cbd2eb80d4cfab23fc80d0876d89ca36a9daadcb864bc00917c94187c
  languageName: node
  linkType: hard

"@nodelib/fs.scandir@npm:2.1.5":
  version: 2.1.5
  resolution: "@nodelib/fs.scandir@npm:2.1.5"
  dependencies:
    "@nodelib/fs.stat": "npm:2.0.5"
    run-parallel: "npm:^1.1.9"
  checksum: 10/6ab2a9b8a1d67b067922c36f259e3b3dfd6b97b219c540877a4944549a4d49ea5ceba5663905ab5289682f1f3c15ff441d02f0447f620a42e1cb5e1937174d4b
  languageName: node
  linkType: hard

"@nodelib/fs.stat@npm:2.0.5, @nodelib/fs.stat@npm:^2.0.2":
  version: 2.0.5
  resolution: "@nodelib/fs.stat@npm:2.0.5"
  checksum: 10/012480b5ca9d97bff9261571dbbec7bbc6033f69cc92908bc1ecfad0792361a5a1994bc48674b9ef76419d056a03efadfce5a6cf6dbc0a36559571a7a483f6f0
  languageName: node
  linkType: hard

"@nodelib/fs.walk@npm:^1.2.3":
  version: 1.2.8
  resolution: "@nodelib/fs.walk@npm:1.2.8"
  dependencies:
    "@nodelib/fs.scandir": "npm:2.1.5"
    fastq: "npm:^1.6.0"
  checksum: 10/40033e33e96e97d77fba5a238e4bba4487b8284678906a9f616b5579ddaf868a18874c0054a75402c9fbaaa033a25ceae093af58c9c30278e35c23c9479e79b0
  languageName: node
  linkType: hard

"@npmcli/agent@npm:^3.0.0":
  version: 3.0.0
  resolution: "@npmcli/agent@npm:3.0.0"
  dependencies:
    agent-base: "npm:^7.1.0"
    http-proxy-agent: "npm:^7.0.0"
    https-proxy-agent: "npm:^7.0.1"
    lru-cache: "npm:^10.0.1"
    socks-proxy-agent: "npm:^8.0.3"
  checksum: 10/775c9a7eb1f88c195dfb3bce70c31d0fe2a12b28b754e25c08a3edb4bc4816bfedb7ac64ef1e730579d078ca19dacf11630e99f8f3c3e0fd7b23caa5fd6d30a6
  languageName: node
  linkType: hard

"@npmcli/fs@npm:^4.0.0":
  version: 4.0.0
  resolution: "@npmcli/fs@npm:4.0.0"
  dependencies:
    semver: "npm:^7.3.5"
  checksum: 10/405c4490e1ff11cf299775449a3c254a366a4b1ffc79d87159b0ee7d5558ac9f6a2f8c0735fd6ff3873cef014cb1a44a5f9127cb6a1b2dbc408718cca9365b5a
  languageName: node
  linkType: hard

"@optimize-lodash/rollup-plugin@npm:5.0.2":
  version: 5.0.2
  resolution: "@optimize-lodash/rollup-plugin@npm:5.0.2"
  dependencies:
    "@optimize-lodash/transform": "npm:3.0.6"
    "@rollup/pluginutils": "npm:^5.1.0"
  peerDependencies:
    rollup: ">= 4.x"
  checksum: 10/b9e4ce75c4a4cf23f7b8def0900e3f5b18d38f487002ab8f8d3c686c14fc02e4a86112be096a4410a98b90ed3c2958ad04f3121cd066633c0658c33e88407bf9
  languageName: node
  linkType: hard

"@optimize-lodash/transform@npm:3.0.6":
  version: 3.0.6
  resolution: "@optimize-lodash/transform@npm:3.0.6"
  dependencies:
    estree-walker: "npm:^2.0.2"
    magic-string: "npm:~0.30.11"
  checksum: 10/10f01eeb84a74eaa2e3ee0e1643d08112f35363477aed1ca7099d9f25081a9d1c1719d8d42f5f2ac80ff6bc82075d332d97c048dd992b51865e1b9b8fdda2a06
  languageName: node
  linkType: hard

"@pkgjs/parseargs@npm:^0.11.0":
  version: 0.11.0
  resolution: "@pkgjs/parseargs@npm:0.11.0"
  checksum: 10/115e8ceeec6bc69dff2048b35c0ab4f8bbee12d8bb6c1f4af758604586d802b6e669dcb02dda61d078de42c2b4ddce41b3d9e726d7daa6b4b850f4adbf7333ff
  languageName: node
  linkType: hard

"@pkgr/core@npm:^0.2.9":
  version: 0.2.9
  resolution: "@pkgr/core@npm:0.2.9"
  checksum: 10/bb2fb86977d63f836f8f5b09015d74e6af6488f7a411dcd2bfdca79d76b5a681a9112f41c45bdf88a9069f049718efc6f3900d7f1de66a2ec966068308ae517f
  languageName: node
  linkType: hard

"@polka/url@npm:^1.0.0-next.24":
  version: 1.0.0-next.29
  resolution: "@polka/url@npm:1.0.0-next.29"
  checksum: 10/69ca11ab15a4ffec7f0b07fcc4e1f01489b3d9683a7e1867758818386575c60c213401259ba3705b8a812228d17e2bfd18e6f021194d943fff4bca389c9d4f28
  languageName: node
  linkType: hard

"@rolldown/pluginutils@npm:1.0.0-beta.32":
  version: 1.0.0-beta.32
  resolution: "@rolldown/pluginutils@npm:1.0.0-beta.32"
  checksum: 10/c0bdfda4ed00f1c06810ef2df0fe7133bd3253d54d82e3ca9e5d6e40560ea809997a2267927e83c3f3e5d3fb1df9d5da5251dc734150cca0b1a9c44947ade22f
  languageName: node
  linkType: hard

"@rollup/plugin-babel@npm:6.0.4":
  version: 6.0.4
  resolution: "@rollup/plugin-babel@npm:6.0.4"
  dependencies:
    "@babel/helper-module-imports": "npm:^7.18.6"
    "@rollup/pluginutils": "npm:^5.0.1"
  peerDependencies:
    "@babel/core": ^7.0.0
    "@types/babel__core": ^7.1.9
    rollup: ^1.20.0||^2.0.0||^3.0.0||^4.0.0
  peerDependenciesMeta:
    "@types/babel__core":
      optional: true
    rollup:
      optional: true
  checksum: 10/89210c8c597b41fd4c561749505c85827697d8d0918c492270f522bd85fac8db3af701cf05480a9e594fcc0df9be42fb7ab025b02a272bde74bb2f63c8c39de2
  languageName: node
  linkType: hard

"@rollup/pluginutils@npm:^5.0.1, @rollup/pluginutils@npm:^5.1.0, @rollup/pluginutils@npm:^5.1.3":
  version: 5.3.0
  resolution: "@rollup/pluginutils@npm:5.3.0"
  dependencies:
    "@types/estree": "npm:^1.0.0"
    estree-walker: "npm:^2.0.2"
    picomatch: "npm:^4.0.2"
  peerDependencies:
    rollup: ^1.20.0||^2.0.0||^3.0.0||^4.0.0
  peerDependenciesMeta:
    rollup:
      optional: true
  checksum: 10/6c7dbab90e0ca5918a36875f745a0f30b47d5e0f45b42ed381ad8f7fed76b23e935766b66e3ae75375a42a80369569913abc8fd2529f4338471a1b2b4dfebaff
  languageName: node
  linkType: hard

"@rollup/rollup-android-arm-eabi@npm:4.50.0":
  version: 4.50.0
  resolution: "@rollup/rollup-android-arm-eabi@npm:4.50.0"
  conditions: os=android & cpu=arm
  languageName: node
  linkType: hard

"@rollup/rollup-android-arm64@npm:4.50.0":
  version: 4.50.0
  resolution: "@rollup/rollup-android-arm64@npm:4.50.0"
  conditions: os=android & cpu=arm64
  languageName: node
  linkType: hard

"@rollup/rollup-darwin-arm64@npm:4.50.0":
  version: 4.50.0
  resolution: "@rollup/rollup-darwin-arm64@npm:4.50.0"
  conditions: os=darwin & cpu=arm64
  languageName: node
  linkType: hard

"@rollup/rollup-darwin-x64@npm:4.50.0":
  version: 4.50.0
  resolution: "@rollup/rollup-darwin-x64@npm:4.50.0"
  conditions: os=darwin & cpu=x64
  languageName: node
  linkType: hard

"@rollup/rollup-freebsd-arm64@npm:4.50.0":
  version: 4.50.0
  resolution: "@rollup/rollup-freebsd-arm64@npm:4.50.0"
  conditions: os=freebsd & cpu=arm64
  languageName: node
  linkType: hard

"@rollup/rollup-freebsd-x64@npm:4.50.0":
  version: 4.50.0
  resolution: "@rollup/rollup-freebsd-x64@npm:4.50.0"
  conditions: os=freebsd & cpu=x64
  languageName: node
  linkType: hard

"@rollup/rollup-linux-arm-gnueabihf@npm:4.50.0":
  version: 4.50.0
  resolution: "@rollup/rollup-linux-arm-gnueabihf@npm:4.50.0"
  conditions: os=linux & cpu=arm & libc=glibc
  languageName: node
  linkType: hard

"@rollup/rollup-linux-arm-musleabihf@npm:4.50.0":
  version: 4.50.0
  resolution: "@rollup/rollup-linux-arm-musleabihf@npm:4.50.0"
  conditions: os=linux & cpu=arm & libc=musl
  languageName: node
  linkType: hard

"@rollup/rollup-linux-arm64-gnu@npm:4.50.0":
  version: 4.50.0
  resolution: "@rollup/rollup-linux-arm64-gnu@npm:4.50.0"
  conditions: os=linux & cpu=arm64 & libc=glibc
  languageName: node
  linkType: hard

"@rollup/rollup-linux-arm64-musl@npm:4.50.0":
  version: 4.50.0
  resolution: "@rollup/rollup-linux-arm64-musl@npm:4.50.0"
  conditions: os=linux & cpu=arm64 & libc=musl
  languageName: node
  linkType: hard

"@rollup/rollup-linux-loongarch64-gnu@npm:4.50.0":
  version: 4.50.0
  resolution: "@rollup/rollup-linux-loongarch64-gnu@npm:4.50.0"
  conditions: os=linux & cpu=loong64 & libc=glibc
  languageName: node
  linkType: hard

"@rollup/rollup-linux-ppc64-gnu@npm:4.50.0":
  version: 4.50.0
  resolution: "@rollup/rollup-linux-ppc64-gnu@npm:4.50.0"
  conditions: os=linux & cpu=ppc64 & libc=glibc
  languageName: node
  linkType: hard

"@rollup/rollup-linux-riscv64-gnu@npm:4.50.0":
  version: 4.50.0
  resolution: "@rollup/rollup-linux-riscv64-gnu@npm:4.50.0"
  conditions: os=linux & cpu=riscv64 & libc=glibc
  languageName: node
  linkType: hard

"@rollup/rollup-linux-riscv64-musl@npm:4.50.0":
  version: 4.50.0
  resolution: "@rollup/rollup-linux-riscv64-musl@npm:4.50.0"
  conditions: os=linux & cpu=riscv64 & libc=musl
  languageName: node
  linkType: hard

"@rollup/rollup-linux-s390x-gnu@npm:4.50.0":
  version: 4.50.0
  resolution: "@rollup/rollup-linux-s390x-gnu@npm:4.50.0"
  conditions: os=linux & cpu=s390x & libc=glibc
  languageName: node
  linkType: hard

"@rollup/rollup-linux-x64-gnu@npm:4.50.0":
  version: 4.50.0
  resolution: "@rollup/rollup-linux-x64-gnu@npm:4.50.0"
  conditions: os=linux & cpu=x64 & libc=glibc
  languageName: node
  linkType: hard

"@rollup/rollup-linux-x64-musl@npm:4.50.0":
  version: 4.50.0
  resolution: "@rollup/rollup-linux-x64-musl@npm:4.50.0"
  conditions: os=linux & cpu=x64 & libc=musl
  languageName: node
  linkType: hard

"@rollup/rollup-openharmony-arm64@npm:4.50.0":
  version: 4.50.0
  resolution: "@rollup/rollup-openharmony-arm64@npm:4.50.0"
  conditions: os=openharmony & cpu=arm64
  languageName: node
  linkType: hard

"@rollup/rollup-win32-arm64-msvc@npm:4.50.0":
  version: 4.50.0
  resolution: "@rollup/rollup-win32-arm64-msvc@npm:4.50.0"
  conditions: os=win32 & cpu=arm64
  languageName: node
  linkType: hard

"@rollup/rollup-win32-ia32-msvc@npm:4.50.0":
  version: 4.50.0
  resolution: "@rollup/rollup-win32-ia32-msvc@npm:4.50.0"
  conditions: os=win32 & cpu=ia32
  languageName: node
  linkType: hard

"@rollup/rollup-win32-x64-msvc@npm:4.50.0":
  version: 4.50.0
  resolution: "@rollup/rollup-win32-x64-msvc@npm:4.50.0"
  conditions: os=win32 & cpu=x64
  languageName: node
  linkType: hard

"@rtsao/scc@npm:^1.1.0":
  version: 1.1.0
  resolution: "@rtsao/scc@npm:1.1.0"
  checksum: 10/17d04adf404e04c1e61391ed97bca5117d4c2767a76ae3e879390d6dec7b317fcae68afbf9e98badee075d0b64fa60f287729c4942021b4d19cd01db77385c01
  languageName: node
  linkType: hard

"@storybook/csf@npm:^0.1.11":
  version: 0.1.13
  resolution: "@storybook/csf@npm:0.1.13"
  dependencies:
    type-fest: "npm:^2.19.0"
  checksum: 10/8a590703c44180798869fd12c1f314cb96de18349415a33bcfe30ef6af11fdc1cdb755ea620dedfd5eb7666cf05af5647b77fe28b63000aa52b53b0dc3c77bb5
  languageName: node
  linkType: hard

"@stylistic/eslint-plugin@npm:4.2.0":
  version: 4.2.0
  resolution: "@stylistic/eslint-plugin@npm:4.2.0"
  dependencies:
    "@typescript-eslint/utils": "npm:^8.23.0"
    eslint-visitor-keys: "npm:^4.2.0"
    espree: "npm:^10.3.0"
    estraverse: "npm:^5.3.0"
    picomatch: "npm:^4.0.2"
  peerDependencies:
    eslint: ">=9.0.0"
  checksum: 10/e7913327038f3eac31f10859d3c407b06949bc9660d6a9ee4c132536587af93aca1ddfde5ee152ce5ef75690ecca515c0abf07a1192d62bc14b7d8d8d6430b83
  languageName: node
  linkType: hard

"@svgr/babel-plugin-add-jsx-attribute@npm:8.0.0":
  version: 8.0.0
  resolution: "@svgr/babel-plugin-add-jsx-attribute@npm:8.0.0"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 10/3fc8e35d16f5abe0af5efe5851f27581225ac405d6a1ca44cda0df064cddfcc29a428c48c2e4bef6cebf627c9ac2f652a096030edb02cf5a120ce28d3c234710
  languageName: node
  linkType: hard

"@svgr/babel-plugin-remove-jsx-attribute@npm:8.0.0":
  version: 8.0.0
  resolution: "@svgr/babel-plugin-remove-jsx-attribute@npm:8.0.0"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 10/ff992893c6c4ac802713ba3a97c13be34e62e6d981c813af40daabcd676df68a72a61bd1e692bb1eda3587f1b1d700ea462222ae2153bb0f46886632d4f88d08
  languageName: node
  linkType: hard

"@svgr/babel-plugin-remove-jsx-empty-expression@npm:8.0.0":
  version: 8.0.0
  resolution: "@svgr/babel-plugin-remove-jsx-empty-expression@npm:8.0.0"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 10/0fb691b63a21bac00da3aa2dccec50d0d5a5b347ff408d60803b84410d8af168f2656e4ba1ee1f24dab0ae4e4af77901f2928752bb0434c1f6788133ec599ec8
  languageName: node
  linkType: hard

"@svgr/babel-plugin-replace-jsx-attribute-value@npm:8.0.0":
  version: 8.0.0
  resolution: "@svgr/babel-plugin-replace-jsx-attribute-value@npm:8.0.0"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 10/1edda65ef4f4dd8f021143c8ec276a08f6baa6f733b8e8ee2e7775597bf6b97afb47fdeefd579d6ae6c959fe2e634f55cd61d99377631212228c8cfb351b8921
  languageName: node
  linkType: hard

"@svgr/babel-plugin-svg-dynamic-title@npm:8.0.0":
  version: 8.0.0
  resolution: "@svgr/babel-plugin-svg-dynamic-title@npm:8.0.0"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 10/876cec891488992e6a9aebb8155e2bea4ec461b4718c51de36e988e00e271c6d9d01ef6be17b9effd44b2b3d7db0b41c161a5904a46ae6f38b26b387ad7f3709
  languageName: node
  linkType: hard

"@svgr/babel-plugin-svg-em-dimensions@npm:8.0.0":
  version: 8.0.0
  resolution: "@svgr/babel-plugin-svg-em-dimensions@npm:8.0.0"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 10/be0e2d391164428327d9ec469a52cea7d93189c6b0e2c290999e048f597d777852f701c64dca44cd45b31ed14a7f859520326e2e4ad7c3a4545d0aa235bc7e9a
  languageName: node
  linkType: hard

"@svgr/babel-plugin-transform-react-native-svg@npm:8.1.0":
  version: 8.1.0
  resolution: "@svgr/babel-plugin-transform-react-native-svg@npm:8.1.0"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 10/85b434a57572f53bd2b9f0606f253e1fcf57b4a8c554ec3f2d43ed17f50d8cae200cb3aaf1ec9d626e1456e8b135dce530ae047eb0bed6d4bf98a752d6640459
  languageName: node
  linkType: hard

"@svgr/babel-plugin-transform-svg-component@npm:8.0.0":
  version: 8.0.0
  resolution: "@svgr/babel-plugin-transform-svg-component@npm:8.0.0"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 10/86ca139c0be0e7df05f103c5f10874387ada1434ca0286584ba9cd367c259d74bf9c86700b856449f46cf674bd6f0cf18f8f034f6d3f0e2ce5e5435c25dbff4b
  languageName: node
  linkType: hard

"@svgr/babel-preset@npm:8.1.0":
  version: 8.1.0
  resolution: "@svgr/babel-preset@npm:8.1.0"
  dependencies:
    "@svgr/babel-plugin-add-jsx-attribute": "npm:8.0.0"
    "@svgr/babel-plugin-remove-jsx-attribute": "npm:8.0.0"
    "@svgr/babel-plugin-remove-jsx-empty-expression": "npm:8.0.0"
    "@svgr/babel-plugin-replace-jsx-attribute-value": "npm:8.0.0"
    "@svgr/babel-plugin-svg-dynamic-title": "npm:8.0.0"
    "@svgr/babel-plugin-svg-em-dimensions": "npm:8.0.0"
    "@svgr/babel-plugin-transform-react-native-svg": "npm:8.1.0"
    "@svgr/babel-plugin-transform-svg-component": "npm:8.0.0"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 10/3a67930f080b8891e1e8e2595716b879c944d253112bae763dce59807ba23454d162216c8d66a0a0e3d4f38a649ecd6c387e545d1e1261dd69a68e9a3392ee08
  languageName: node
  linkType: hard

"@svgr/core@npm:^8.1.0":
  version: 8.1.0
  resolution: "@svgr/core@npm:8.1.0"
  dependencies:
    "@babel/core": "npm:^7.21.3"
    "@svgr/babel-preset": "npm:8.1.0"
    camelcase: "npm:^6.2.0"
    cosmiconfig: "npm:^8.1.3"
    snake-case: "npm:^3.0.4"
  checksum: 10/bc98cd5fc349ab9dcf0c13c2279164726d45878cdac8999090765379c6e897a1b24aca641c12a3c33f578d06f7a09252fb090962a4695c753fb02b627a56bfe6
  languageName: node
  linkType: hard

"@svgr/hast-util-to-babel-ast@npm:8.0.0":
  version: 8.0.0
  resolution: "@svgr/hast-util-to-babel-ast@npm:8.0.0"
  dependencies:
    "@babel/types": "npm:^7.21.3"
    entities: "npm:^4.4.0"
  checksum: 10/243aa9c92d66aa3f1fc82851fe1fa376808a08fcc02719fed38ebfb4e25cf3e3c1282c185300c29953d047c36acb9e3ac588d46b0af55a3b7a5186a6badec8a9
  languageName: node
  linkType: hard

"@svgr/plugin-jsx@npm:^8.1.0":
  version: 8.1.0
  resolution: "@svgr/plugin-jsx@npm:8.1.0"
  dependencies:
    "@babel/core": "npm:^7.21.3"
    "@svgr/babel-preset": "npm:8.1.0"
    "@svgr/hast-util-to-babel-ast": "npm:8.0.0"
    svg-parser: "npm:^2.0.4"
  peerDependencies:
    "@svgr/core": "*"
  checksum: 10/0418a9780753d3544912ee2dad5d2cf8d12e1ba74df8053651b3886aeda54d5f0f7d2dece0af5e0d838332c4f139a57f0dabaa3ca1afa4d1a765efce6a7656f2
  languageName: node
  linkType: hard

"@swc/core-darwin-arm64@npm:1.13.5":
  version: 1.13.5
  resolution: "@swc/core-darwin-arm64@npm:1.13.5"
  conditions: os=darwin & cpu=arm64
  languageName: node
  linkType: hard

"@swc/core-darwin-x64@npm:1.13.5":
  version: 1.13.5
  resolution: "@swc/core-darwin-x64@npm:1.13.5"
  conditions: os=darwin & cpu=x64
  languageName: node
  linkType: hard

"@swc/core-linux-arm-gnueabihf@npm:1.13.5":
  version: 1.13.5
  resolution: "@swc/core-linux-arm-gnueabihf@npm:1.13.5"
  conditions: os=linux & cpu=arm
  languageName: node
  linkType: hard

"@swc/core-linux-arm64-gnu@npm:1.13.5":
  version: 1.13.5
  resolution: "@swc/core-linux-arm64-gnu@npm:1.13.5"
  conditions: os=linux & cpu=arm64 & libc=glibc
  languageName: node
  linkType: hard

"@swc/core-linux-arm64-musl@npm:1.13.5":
  version: 1.13.5
  resolution: "@swc/core-linux-arm64-musl@npm:1.13.5"
  conditions: os=linux & cpu=arm64 & libc=musl
  languageName: node
  linkType: hard

"@swc/core-linux-x64-gnu@npm:1.13.5":
  version: 1.13.5
  resolution: "@swc/core-linux-x64-gnu@npm:1.13.5"
  conditions: os=linux & cpu=x64 & libc=glibc
  languageName: node
  linkType: hard

"@swc/core-linux-x64-musl@npm:1.13.5":
  version: 1.13.5
  resolution: "@swc/core-linux-x64-musl@npm:1.13.5"
  conditions: os=linux & cpu=x64 & libc=musl
  languageName: node
  linkType: hard

"@swc/core-win32-arm64-msvc@npm:1.13.5":
  version: 1.13.5
  resolution: "@swc/core-win32-arm64-msvc@npm:1.13.5"
  conditions: os=win32 & cpu=arm64
  languageName: node
  linkType: hard

"@swc/core-win32-ia32-msvc@npm:1.13.5":
  version: 1.13.5
  resolution: "@swc/core-win32-ia32-msvc@npm:1.13.5"
  conditions: os=win32 & cpu=ia32
  languageName: node
  linkType: hard

"@swc/core-win32-x64-msvc@npm:1.13.5":
  version: 1.13.5
  resolution: "@swc/core-win32-x64-msvc@npm:1.13.5"
  conditions: os=win32 & cpu=x64
  languageName: node
  linkType: hard

"@swc/core@npm:^1.13.2, @swc/core@npm:^1.3.61":
  version: 1.13.5
  resolution: "@swc/core@npm:1.13.5"
  dependencies:
    "@swc/core-darwin-arm64": "npm:1.13.5"
    "@swc/core-darwin-x64": "npm:1.13.5"
    "@swc/core-linux-arm-gnueabihf": "npm:1.13.5"
    "@swc/core-linux-arm64-gnu": "npm:1.13.5"
    "@swc/core-linux-arm64-musl": "npm:1.13.5"
    "@swc/core-linux-x64-gnu": "npm:1.13.5"
    "@swc/core-linux-x64-musl": "npm:1.13.5"
    "@swc/core-win32-arm64-msvc": "npm:1.13.5"
    "@swc/core-win32-ia32-msvc": "npm:1.13.5"
    "@swc/core-win32-x64-msvc": "npm:1.13.5"
    "@swc/counter": "npm:^0.1.3"
    "@swc/types": "npm:^0.1.24"
  peerDependencies:
    "@swc/helpers": ">=0.5.17"
  dependenciesMeta:
    "@swc/core-darwin-arm64":
      optional: true
    "@swc/core-darwin-x64":
      optional: true
    "@swc/core-linux-arm-gnueabihf":
      optional: true
    "@swc/core-linux-arm64-gnu":
      optional: true
    "@swc/core-linux-arm64-musl":
      optional: true
    "@swc/core-linux-x64-gnu":
      optional: true
    "@swc/core-linux-x64-musl":
      optional: true
    "@swc/core-win32-arm64-msvc":
      optional: true
    "@swc/core-win32-ia32-msvc":
      optional: true
    "@swc/core-win32-x64-msvc":
      optional: true
  peerDependenciesMeta:
    "@swc/helpers":
      optional: true
  checksum: 10/75b44c5008cd043ca2aa33697aee677b3a30792c7e844d1b636d30fe8f865cbc95e606d396969c4bea9b95eb00bd3fc183b3359ec77d9c6a6f737c473ec2146e
  languageName: node
  linkType: hard

"@swc/counter@npm:^0.1.3":
  version: 0.1.3
  resolution: "@swc/counter@npm:0.1.3"
  checksum: 10/df8f9cfba9904d3d60f511664c70d23bb323b3a0803ec9890f60133954173047ba9bdeabce28cd70ba89ccd3fd6c71c7b0bd58be85f611e1ffbe5d5c18616598
  languageName: node
  linkType: hard

"@swc/plugin-styled-components@npm:>=9.0.0":
  version: 9.1.0
  resolution: "@swc/plugin-styled-components@npm:9.1.0"
  dependencies:
    "@swc/counter": "npm:^0.1.3"
  checksum: 10/34ce101caed1c3c327ded2c8ffac65ad22579c187684528301e9b225a3e7ce3ee7b8614ad6bd72beb7babe94ee6662a40b0e28f7fde3a349a7348ae32142df13
  languageName: node
  linkType: hard

"@swc/types@npm:^0.1.24":
  version: 0.1.24
  resolution: "@swc/types@npm:0.1.24"
  dependencies:
    "@swc/counter": "npm:^0.1.3"
  checksum: 10/fef06c7345b5a9b1efcda039fa223c13a466e395881af1a1d4b1474cf43dfeabd7f84fcec6cd3a41953ab6757b0456ecf396af5c8ca8043f352bb243658cdfaa
  languageName: node
  linkType: hard

"@tanstack/eslint-plugin-query@npm:5.86.0":
  version: 5.86.0
  resolution: "@tanstack/eslint-plugin-query@npm:5.86.0"
  dependencies:
    "@typescript-eslint/utils": "npm:^8.37.0"
  peerDependencies:
    eslint: ^8.57.0 || ^9.0.0
  checksum: 10/a72d99b929006f6516f916d4fecfe88f116d7acd4fc22b6c7c7d23c821e8ac1b2b14149c7f9afe432eabd3b3efc661d5c3f29b03ccf1603216209e325d77a114
  languageName: node
  linkType: hard

"@tanstack/history@npm:1.131.2":
  version: 1.131.2
  resolution: "@tanstack/history@npm:1.131.2"
  checksum: 10/6bc5a7609b07948ff6993fc16237c568360d01308fb2c9858e48ff8dba79f07498b3f963d0e9339ca89bd2c12716aa3fc0e720ca8add6e307f129817c3430002
  languageName: node
  linkType: hard

"@tanstack/query-core@npm:5.86.0":
  version: 5.86.0
  resolution: "@tanstack/query-core@npm:5.86.0"
  checksum: 10/66c6811dc39e56d59d8c3a996743b067ca45add7eab193f788e6041713b730bf7859b693f31cbda0eda92fef4dd3ff969f1ac0bbd553f554c497da322ff45871
  languageName: node
  linkType: hard

"@tanstack/query-devtools@npm:5.86.0":
  version: 5.86.0
  resolution: "@tanstack/query-devtools@npm:5.86.0"
  checksum: 10/97b24d00c22dc5c94e75b35106e3b07c0a873fa2f89646620ad62fd46098f98a86a2aa442e90a71bca59e1ef036622d7b5e98e9a97a9879f6a60b13ca43f792a
  languageName: node
  linkType: hard

"@tanstack/react-query-devtools@npm:5.86.0":
  version: 5.86.0
  resolution: "@tanstack/react-query-devtools@npm:5.86.0"
  dependencies:
    "@tanstack/query-devtools": "npm:5.86.0"
  peerDependencies:
    "@tanstack/react-query": ^5.86.0
    react: ^18 || ^19
  checksum: 10/3143d309907f72beaba37877a95a5888838973720d86a80c319da05e0edf09472d1c56646746ce5e6e92e1504f1be6866b8fb95b5e6079d2f1150a6330565156
  languageName: node
  linkType: hard

"@tanstack/react-query@npm:5.86.0":
  version: 5.86.0
  resolution: "@tanstack/react-query@npm:5.86.0"
  dependencies:
    "@tanstack/query-core": "npm:5.86.0"
  peerDependencies:
    react: ^18 || ^19
  checksum: 10/1d78a52c917f14bb5add0e644218a3399b636c9935356d6d68148668371f4a098489bba0ce35a854b0ff0a3935ee2c493ad020a178afc28fc1f08e78e8a26443
  languageName: node
  linkType: hard

"@tanstack/react-router-devtools@npm:1.131.35":
  version: 1.131.35
  resolution: "@tanstack/react-router-devtools@npm:1.131.35"
  dependencies:
    "@tanstack/router-devtools-core": "npm:1.131.35"
  peerDependencies:
    "@tanstack/react-router": ^1.131.35
    react: ">=18.0.0 || >=19.0.0"
    react-dom: ">=18.0.0 || >=19.0.0"
  checksum: 10/a42a088d57384183e0887a710b5912d2aa11e995aeb93a1dcdb4689297f038e92dcc7e40eefebb7d18d3ce70bad54ef5789cc187dd0d9926ac9b508eab3bc5d8
  languageName: node
  linkType: hard

"@tanstack/react-router@npm:1.131.35":
  version: 1.131.35
  resolution: "@tanstack/react-router@npm:1.131.35"
  dependencies:
    "@tanstack/history": "npm:1.131.2"
    "@tanstack/react-store": "npm:^0.7.0"
    "@tanstack/router-core": "npm:1.131.35"
    isbot: "npm:^5.1.22"
    tiny-invariant: "npm:^1.3.3"
    tiny-warning: "npm:^1.0.3"
  peerDependencies:
    react: ">=18.0.0 || >=19.0.0"
    react-dom: ">=18.0.0 || >=19.0.0"
  checksum: 10/0a78554a6290950a4024bd466efc730b612531f6d1cf9374d00f465d83628ce0d29da10c09894ef269901b9103c3fbb932501ae512d3cbf0da338dfe76c26c94
  languageName: node
  linkType: hard

"@tanstack/react-store@npm:^0.7.0":
  version: 0.7.4
  resolution: "@tanstack/react-store@npm:0.7.4"
  dependencies:
    "@tanstack/store": "npm:0.7.4"
    use-sync-external-store: "npm:^1.5.0"
  peerDependencies:
    react: ^16.8.0 || ^17.0.0 || ^18.0.0 || ^19.0.0
    react-dom: ^16.8.0 || ^17.0.0 || ^18.0.0 || ^19.0.0
  checksum: 10/b5f2a529132b6361134a1e1f6f0e1951d0982f9ace0ecef0e0ace8a3bb64471ebb182dcbc3e346c1b9a4fbc5dd39edc4bb5f130b7eb68c1934c66c79e62fa332
  languageName: node
  linkType: hard

"@tanstack/router-core@npm:1.131.35":
  version: 1.131.35
  resolution: "@tanstack/router-core@npm:1.131.35"
  dependencies:
    "@tanstack/history": "npm:1.131.2"
    "@tanstack/store": "npm:^0.7.0"
    cookie-es: "npm:^1.2.2"
    seroval: "npm:^1.3.2"
    seroval-plugins: "npm:^1.3.2"
    tiny-invariant: "npm:^1.3.3"
    tiny-warning: "npm:^1.0.3"
  checksum: 10/ff994e1238b0d062445530195c477a6c6f8912713b10660c4bb821f8e83b568bc8679de8e17b131726cc9ba4f864a6352d730d6471e8cc90ef1772946cef4f69
  languageName: node
  linkType: hard

"@tanstack/router-devtools-core@npm:1.131.35":
  version: 1.131.35
  resolution: "@tanstack/router-devtools-core@npm:1.131.35"
  dependencies:
    clsx: "npm:^2.1.1"
    goober: "npm:^2.1.16"
    solid-js: "npm:^1.9.5"
  peerDependencies:
    "@tanstack/router-core": ^1.131.35
    csstype: ^3.0.10
    solid-js: ">=1.9.5"
    tiny-invariant: ^1.3.3
  peerDependenciesMeta:
    csstype:
      optional: true
  checksum: 10/56ab7ec8ec411e9bf0c7aa23581ffd71d8c07e0188884a74faa8b94d9789a7b40e10c40cba2a9a20d1c3a01b5f6701dfd7c15734998136aa7549520937b369a6
  languageName: node
  linkType: hard

"@tanstack/router-generator@npm:1.131.35":
  version: 1.131.35
  resolution: "@tanstack/router-generator@npm:1.131.35"
  dependencies:
    "@tanstack/router-core": "npm:1.131.35"
    "@tanstack/router-utils": "npm:1.131.2"
    "@tanstack/virtual-file-routes": "npm:1.131.2"
    prettier: "npm:^3.5.0"
    recast: "npm:^0.23.11"
    source-map: "npm:^0.7.4"
    tsx: "npm:^4.19.2"
    zod: "npm:^3.24.2"
  checksum: 10/7bfcd0fe395b64f5550d3a3774e02238b473193e8800c127dda08bb4c941a8c5b2141ae0bebac5ecc68561b87dd9bb20c9cffb342c901cdf32b0971fc99278a5
  languageName: node
  linkType: hard

"@tanstack/router-plugin@npm:1.131.35":
  version: 1.131.35
  resolution: "@tanstack/router-plugin@npm:1.131.35"
  dependencies:
    "@babel/core": "npm:^7.27.7"
    "@babel/plugin-syntax-jsx": "npm:^7.27.1"
    "@babel/plugin-syntax-typescript": "npm:^7.27.1"
    "@babel/template": "npm:^7.27.2"
    "@babel/traverse": "npm:^7.27.7"
    "@babel/types": "npm:^7.27.7"
    "@tanstack/router-core": "npm:1.131.35"
    "@tanstack/router-generator": "npm:1.131.35"
    "@tanstack/router-utils": "npm:1.131.2"
    "@tanstack/virtual-file-routes": "npm:1.131.2"
    babel-dead-code-elimination: "npm:^1.0.10"
    chokidar: "npm:^3.6.0"
    unplugin: "npm:^2.1.2"
    zod: "npm:^3.24.2"
  peerDependencies:
    "@rsbuild/core": ">=1.0.2"
    "@tanstack/react-router": ^1.131.35
    vite: ">=5.0.0 || >=6.0.0"
    vite-plugin-solid: ^2.11.2
    webpack: ">=5.92.0"
  peerDependenciesMeta:
    "@rsbuild/core":
      optional: true
    "@tanstack/react-router":
      optional: true
    vite:
      optional: true
    vite-plugin-solid:
      optional: true
    webpack:
      optional: true
  checksum: 10/622b841dad7313bb04f4b9c200a007ef4ba567a34ca2846c2fdc4ddfb1a1d788ca078c99135133b18768ad4ae371583aa036c4595f2ca223b75b730eaf8765fe
  languageName: node
  linkType: hard

"@tanstack/router-utils@npm:1.131.2":
  version: 1.131.2
  resolution: "@tanstack/router-utils@npm:1.131.2"
  dependencies:
    "@babel/core": "npm:^7.27.4"
    "@babel/generator": "npm:^7.27.5"
    "@babel/parser": "npm:^7.27.5"
    "@babel/preset-typescript": "npm:^7.27.1"
    ansis: "npm:^4.1.0"
    diff: "npm:^8.0.2"
  checksum: 10/a8a97863134286560ea480c30b3ffa791801138a9a54bee0a1226856e7cd58b4a3662e47d33e8ddff3223a71f1e297962ae88f40c91d7c32566d232e756b486a
  languageName: node
  linkType: hard

"@tanstack/store@npm:0.7.4, @tanstack/store@npm:^0.7.0":
  version: 0.7.4
  resolution: "@tanstack/store@npm:0.7.4"
  checksum: 10/f0c183dbc4a4d1db63c51585650fe31a0bf232feb9e6e9bfdb82ffba231273673e3af8ceb36f863bd52483dd960f2e8b4ca61f59cc6436cdc53cc64fd5842789
  languageName: node
  linkType: hard

"@tanstack/virtual-file-routes@npm:1.131.2":
  version: 1.131.2
  resolution: "@tanstack/virtual-file-routes@npm:1.131.2"
  checksum: 10/21d9b53caf33ab89dadf42e9f684b627a122af49330f30b08b4c1590d68deb2a0fdfac35bb8d408b45926014f99725a08b173dcc8e7d3e4952b5ea0b1c7b80c7
  languageName: node
  linkType: hard

"@testing-library/dom@npm:^9.0.0":
  version: 9.3.4
  resolution: "@testing-library/dom@npm:9.3.4"
  dependencies:
    "@babel/code-frame": "npm:^7.10.4"
    "@babel/runtime": "npm:^7.12.5"
    "@types/aria-query": "npm:^5.0.1"
    aria-query: "npm:5.1.3"
    chalk: "npm:^4.1.0"
    dom-accessibility-api: "npm:^0.5.9"
    lz-string: "npm:^1.5.0"
    pretty-format: "npm:^27.0.2"
  checksum: 10/510da752ea76f4a10a0a4e3a77917b0302cf03effe576cd3534cab7e796533ee2b0e9fb6fb11b911a1ebd7c70a0bb6f235bf4f816c9b82b95b8fe0cddfd10975
  languageName: node
  linkType: hard

"@testing-library/jest-dom@npm:6.8.0":
  version: 6.8.0
  resolution: "@testing-library/jest-dom@npm:6.8.0"
  dependencies:
    "@adobe/css-tools": "npm:^4.4.0"
    aria-query: "npm:^5.0.0"
    css.escape: "npm:^1.5.1"
    dom-accessibility-api: "npm:^0.6.3"
    picocolors: "npm:^1.1.1"
    redent: "npm:^3.0.0"
  checksum: 10/d9bebf1f32e46fdde7e12a2b1ee1f8d113b2fb56e86720f185c32ccae2b76e74c76ecaa69c3aeee98a5fded3417b47032891e7ec9be83d4e6bf888ed8356032f
  languageName: node
  linkType: hard

"@testing-library/react@npm:14.1.2":
  version: 14.1.2
  resolution: "@testing-library/react@npm:14.1.2"
  dependencies:
    "@babel/runtime": "npm:^7.12.5"
    "@testing-library/dom": "npm:^9.0.0"
    "@types/react-dom": "npm:^18.0.0"
  peerDependencies:
    react: ^18.0.0
    react-dom: ^18.0.0
  checksum: 10/1664990ad9673403ee1d74c1c1b60ec30591d42a3fe1e2175c28cb935cd49bc9a4ba398707f702acc3278c3b0cb492ee57fe66f41ceb040c5da57de98cba5414
  languageName: node
  linkType: hard

"@testing-library/user-event@npm:14.5.1":
  version: 14.5.1
  resolution: "@testing-library/user-event@npm:14.5.1"
  peerDependencies:
    "@testing-library/dom": ">=7.21.4"
  checksum: 10/696e1328c230b0a7063a41d82b5350c6be926696106809a4d79d446d190ff56bebb850fe564ff0952cb74ae81e59a6f10534a88ecbb3792083271a249e04e728
  languageName: node
  linkType: hard

"@tweenjs/tween.js@npm:~23.1.3":
  version: 23.1.3
  resolution: "@tweenjs/tween.js@npm:23.1.3"
  checksum: 10/10ecaf311c975162459bd016ef7eb1f3118a257050c4886de9737dee823fc829100a0887f3fc298f7a5577140eac054f9609b823eb0748ca7b653ec85ffba75e
  languageName: node
  linkType: hard

"@tybys/wasm-util@npm:^0.10.0":
  version: 0.10.0
  resolution: "@tybys/wasm-util@npm:0.10.0"
  dependencies:
    tslib: "npm:^2.4.0"
  checksum: 10/779d047a77e8a619b6e26b6fe556f413316d846e9a35438668a15510a4d6e7294388c998f65911f6f1a13838745575d7793cb1d27182752f6f95991725b15d45
  languageName: node
  linkType: hard

"@types/aria-query@npm:^5.0.1":
  version: 5.0.4
  resolution: "@types/aria-query@npm:5.0.4"
  checksum: 10/c0084c389dc030daeaf0115a92ce43a3f4d42fc8fef2d0e22112d87a42798d4a15aac413019d4a63f868327d52ad6740ab99609462b442fe6b9286b172d2e82e
  languageName: node
  linkType: hard

"@types/body-parser@npm:*":
  version: 1.19.6
  resolution: "@types/body-parser@npm:1.19.6"
  dependencies:
    "@types/connect": "npm:*"
    "@types/node": "npm:*"
  checksum: 10/33041e88eae00af2cfa0827e951e5f1751eafab2a8b6fce06cd89ef368a988907996436b1325180edaeddd1c0c7d0d0d4c20a6c9ff294a91e0039a9db9e9b658
  languageName: node
  linkType: hard

"@types/chai@npm:^5.2.2":
  version: 5.2.2
  resolution: "@types/chai@npm:5.2.2"
  dependencies:
    "@types/deep-eql": "npm:*"
  checksum: 10/de425e7b02cc1233a93923866e019dffbafa892774813940b780ebb1ac9f8a8c57b7438c78686bf4e5db05cd3fc8a970fedf6b83638543995ecca88ef2060668
  languageName: node
  linkType: hard

"@types/connect@npm:*":
  version: 3.4.38
  resolution: "@types/connect@npm:3.4.38"
  dependencies:
    "@types/node": "npm:*"
  checksum: 10/7eb1bc5342a9604facd57598a6c62621e244822442976c443efb84ff745246b10d06e8b309b6e80130026a396f19bf6793b7cecd7380169f369dac3bfc46fb99
  languageName: node
  linkType: hard

"@types/deep-eql@npm:*":
  version: 4.0.2
  resolution: "@types/deep-eql@npm:4.0.2"
  checksum: 10/249a27b0bb22f6aa28461db56afa21ec044fa0e303221a62dff81831b20c8530502175f1a49060f7099e7be06181078548ac47c668de79ff9880241968d43d0c
  languageName: node
  linkType: hard

"@types/estree@npm:1.0.8, @types/estree@npm:^1.0.0, @types/estree@npm:^1.0.6":
  version: 1.0.8
  resolution: "@types/estree@npm:1.0.8"
  checksum: 10/25a4c16a6752538ffde2826c2cc0c6491d90e69cd6187bef4a006dd2c3c45469f049e643d7e516c515f21484dc3d48fd5c870be158a5beb72f5baf3dc43e4099
  languageName: node
  linkType: hard

"@types/express-serve-static-core@npm:^4.17.33":
  version: 4.19.6
  resolution: "@types/express-serve-static-core@npm:4.19.6"
  dependencies:
    "@types/node": "npm:*"
    "@types/qs": "npm:*"
    "@types/range-parser": "npm:*"
    "@types/send": "npm:*"
  checksum: 10/a2e00b6c5993f0dd63ada2239be81076fe0220314b9e9fde586e8946c9c09ce60f9a2dd0d74410ee2b5fd10af8c3e755a32bb3abf134533e2158142488995455
  languageName: node
  linkType: hard

"@types/express@npm:^4.17.14":
  version: 4.17.23
  resolution: "@types/express@npm:4.17.23"
  dependencies:
    "@types/body-parser": "npm:*"
    "@types/express-serve-static-core": "npm:^4.17.33"
    "@types/qs": "npm:*"
    "@types/serve-static": "npm:*"
  checksum: 10/cf4d540bbd90801cdc79a46107b8873404698a7fd0c3e8dd42989d52d3bd7f5b8768672e54c20835e41e27349c319bb47a404ad14c0f8db0e9d055ba1cb8a05b
  languageName: node
  linkType: hard

"@types/http-errors@npm:*":
  version: 2.0.5
  resolution: "@types/http-errors@npm:2.0.5"
  checksum: 10/a88da669366bc483e8f3b3eb3d34ada5f8d13eeeef851b1204d77e2ba6fc42aba4566d877cca5c095204a3f4349b87fe397e3e21288837bdd945dd514120755b
  languageName: node
  linkType: hard

"@types/json-schema@npm:^7.0.15":
  version: 7.0.15
  resolution: "@types/json-schema@npm:7.0.15"
  checksum: 10/1a3c3e06236e4c4aab89499c428d585527ce50c24fe8259e8b3926d3df4cfbbbcf306cfc73ddfb66cbafc973116efd15967020b0f738f63e09e64c7d260519e7
  languageName: node
  linkType: hard

"@types/json5@npm:^0.0.29":
  version: 0.0.29
  resolution: "@types/json5@npm:0.0.29"
  checksum: 10/4e5aed58cabb2bbf6f725da13421aa50a49abb6bc17bfab6c31b8774b073fa7b50d557c61f961a09a85f6056151190f8ac95f13f5b48136ba5841f7d4484ec56
  languageName: node
  linkType: hard

"@types/mime@npm:^1":
  version: 1.3.5
  resolution: "@types/mime@npm:1.3.5"
  checksum: 10/e29a5f9c4776f5229d84e525b7cd7dd960b51c30a0fb9a028c0821790b82fca9f672dab56561e2acd9e8eed51d431bde52eafdfef30f643586c4162f1aecfc78
  languageName: node
  linkType: hard

"@types/node@npm:*":
  version: 24.3.1
  resolution: "@types/node@npm:24.3.1"
  dependencies:
    undici-types: "npm:~7.10.0"
  checksum: 10/b9a52ed8f2fb1fa764ce988828b8d579761f944f1bb0492adbdeaa676a161086bad1a807b590799681ad93b3d272468b5c5ddccf5678735d487d1b65e481bc07
  languageName: node
  linkType: hard

"@types/node@npm:22.13.10":
  version: 22.13.10
  resolution: "@types/node@npm:22.13.10"
  dependencies:
    undici-types: "npm:~6.20.0"
  checksum: 10/57dc6a5e0110ca9edea8d7047082e649fa7fa813f79e4a901653b9174141c622f4336435648baced5b38d9f39843f404fa2d8d7a10981610da26066bc8caab48
  languageName: node
  linkType: hard

"@types/node@npm:^18.11.3":
  version: 18.19.124
  resolution: "@types/node@npm:18.19.124"
  dependencies:
    undici-types: "npm:~5.26.4"
  checksum: 10/64641bb4bb4e18c75f40c63b41f9c3d4939b6f589999258f5ac3001040b201f399056f9c3cf3d6c5fa23e94f469c1e48ebbc0e31fe4d8eae8ef8bccf5e902942
  languageName: node
  linkType: hard

"@types/qs@npm:*":
  version: 6.14.0
  resolution: "@types/qs@npm:6.14.0"
  checksum: 10/1909205514d22b3cbc7c2314e2bd8056d5f05dfb21cf4377f0730ee5e338ea19957c41735d5e4806c746176563f50005bbab602d8358432e25d900bdf4970826
  languageName: node
  linkType: hard

"@types/range-parser@npm:*":
  version: 1.2.7
  resolution: "@types/range-parser@npm:1.2.7"
  checksum: 10/95640233b689dfbd85b8c6ee268812a732cf36d5affead89e806fe30da9a430767af8ef2cd661024fd97e19d61f3dec75af2df5e80ec3bea000019ab7028629a
  languageName: node
  linkType: hard

"@types/react-dom@npm:^18.0.0":
  version: 18.3.7
  resolution: "@types/react-dom@npm:18.3.7"
  peerDependencies:
    "@types/react": ^18.0.0
  checksum: 10/317569219366d487a3103ba1e5e47154e95a002915fdcf73a44162c48fe49c3a57fcf7f57fc6979e70d447112681e6b13c6c3c1df289db8b544df4aab2d318f3
  languageName: node
  linkType: hard

"@types/react-dom@npm:^19":
  version: 19.1.9
  resolution: "@types/react-dom@npm:19.1.9"
  peerDependencies:
    "@types/react": ^19.0.0
  checksum: 10/207acb79f6c3c9704938138960e21429efdf2db2184f17c166e8ec3f3180dfe6445b282c5302f559a71b2d09ab2fafef7735f3d24fd01cda4e5c7bf0cea1d5b9
  languageName: node
  linkType: hard

"@types/react@npm:^19":
  version: 19.1.12
  resolution: "@types/react@npm:19.1.12"
  dependencies:
    csstype: "npm:^3.0.2"
  checksum: 10/c03d595b84faecb15079757555c96871e84ea6eef9a5eddb13ec1f648f718f9624bebc4199e86267edb825b23df97758324ea39ff840d9ad328386f96971f588
  languageName: node
  linkType: hard

"@types/send@npm:*":
  version: 0.17.5
  resolution: "@types/send@npm:0.17.5"
  dependencies:
    "@types/mime": "npm:^1"
    "@types/node": "npm:*"
  checksum: 10/b68ae8f9ba9328a4f276cd010914ed43b96371fbf34c7aa08a9111bff36661810bb14b96647e4a92e319dbd2689dc107fb0f9194ec3fa9335c162dc134026240
  languageName: node
  linkType: hard

"@types/serve-static@npm:*":
  version: 1.15.8
  resolution: "@types/serve-static@npm:1.15.8"
  dependencies:
    "@types/http-errors": "npm:*"
    "@types/node": "npm:*"
    "@types/send": "npm:*"
  checksum: 10/c031f870df6056a4c0a5a0ae94c5584006ab55400c74ae44de4d68d89338fbe982422861bad478b89a073f671efca454689fd28b6147358d6adc8edbc599caea
  languageName: node
  linkType: hard

"@types/stats.js@npm:*":
  version: 0.17.4
  resolution: "@types/stats.js@npm:0.17.4"
  checksum: 10/6f1a20e09da0c0b371b3c095b2787a91a9397944679aa506a0886160faf0ad185b911a34b4ec325f146810c5be969f085e81264e2dbe39fd57eb8ff98347b5f4
  languageName: node
  linkType: hard

"@types/stylis@npm:4.2.5":
  version: 4.2.5
  resolution: "@types/stylis@npm:4.2.5"
  checksum: 10/f8dde326432a7047b6684b96442f0e2ade2cfe8c29bf56217fb8cbbe4763997051fa9dc0f8dba4aeed2fddb794b4bc91feba913b780666b3adc28198ac7c63d4
  languageName: node
  linkType: hard

"@types/three@npm:^0":
  version: 0.180.0
  resolution: "@types/three@npm:0.180.0"
  dependencies:
    "@dimforge/rapier3d-compat": "npm:~0.12.0"
    "@tweenjs/tween.js": "npm:~23.1.3"
    "@types/stats.js": "npm:*"
    "@types/webxr": "npm:*"
    "@webgpu/types": "npm:*"
    fflate: "npm:~0.8.2"
    meshoptimizer: "npm:~0.22.0"
  checksum: 10/0289529c46bf99967cbcdc3b56fe9038048ef02dc2003a32a911547fce7a73b832d9c7dd31ed3ccbe761f3ee9b33285921fff8c1c7b9317ff274f13744f2374b
  languageName: node
  linkType: hard

"@types/webxr@npm:*":
  version: 0.5.23
  resolution: "@types/webxr@npm:0.5.23"
  checksum: 10/9b0e737a09e7bf55fd685b7e8200979af2b0cba08a59cb6523cb1077b9e3ad305995dc467103bea560ed552636eecdbbd0dcdfccff52e7caaae52a48ec4cf900
  languageName: node
  linkType: hard

"@typescript-eslint/eslint-plugin@npm:8.39.0":
  version: 8.39.0
  resolution: "@typescript-eslint/eslint-plugin@npm:8.39.0"
  dependencies:
    "@eslint-community/regexpp": "npm:^4.10.0"
    "@typescript-eslint/scope-manager": "npm:8.39.0"
    "@typescript-eslint/type-utils": "npm:8.39.0"
    "@typescript-eslint/utils": "npm:8.39.0"
    "@typescript-eslint/visitor-keys": "npm:8.39.0"
    graphemer: "npm:^1.4.0"
    ignore: "npm:^7.0.0"
    natural-compare: "npm:^1.4.0"
    ts-api-utils: "npm:^2.1.0"
  peerDependencies:
    "@typescript-eslint/parser": ^8.39.0
    eslint: ^8.57.0 || ^9.0.0
    typescript: ">=4.8.4 <6.0.0"
  checksum: 10/31f879990aaedbd0eaf4ccb670c0b8d0686db8ced8fe5b58fabe509e38cd46e385b38a89a192ffb9d8486bce0ae5bf0032623149e07f07fab1b6548cbe52c373
  languageName: node
  linkType: hard

"@typescript-eslint/parser@npm:8.39.0":
  version: 8.39.0
  resolution: "@typescript-eslint/parser@npm:8.39.0"
  dependencies:
    "@typescript-eslint/scope-manager": "npm:8.39.0"
    "@typescript-eslint/types": "npm:8.39.0"
    "@typescript-eslint/typescript-estree": "npm:8.39.0"
    "@typescript-eslint/visitor-keys": "npm:8.39.0"
    debug: "npm:^4.3.4"
  peerDependencies:
    eslint: ^8.57.0 || ^9.0.0
    typescript: ">=4.8.4 <6.0.0"
  checksum: 10/9785994ff0cacaa168b460120177e4a78570dfbe105bfc9d27e8f58545ea07071cc8989910e72e3ae881945ad33986e004d14570824803016cc29408e9ec08b0
  languageName: node
  linkType: hard

"@typescript-eslint/project-service@npm:8.39.0":
  version: 8.39.0
  resolution: "@typescript-eslint/project-service@npm:8.39.0"
  dependencies:
    "@typescript-eslint/tsconfig-utils": "npm:^8.39.0"
    "@typescript-eslint/types": "npm:^8.39.0"
    debug: "npm:^4.3.4"
  peerDependencies:
    typescript: ">=4.8.4 <6.0.0"
  checksum: 10/990ae23308993ac5fede1e2544fb3f3938424539d40adf810574d17283211533ebdddc42eb99c463ee493208a6700283401cede50f175cce0f2159bc61de96f7
  languageName: node
  linkType: hard

"@typescript-eslint/project-service@npm:8.42.0":
  version: 8.42.0
  resolution: "@typescript-eslint/project-service@npm:8.42.0"
  dependencies:
    "@typescript-eslint/tsconfig-utils": "npm:^8.42.0"
    "@typescript-eslint/types": "npm:^8.42.0"
    debug: "npm:^4.3.4"
  peerDependencies:
    typescript: ">=4.8.4 <6.0.0"
  checksum: 10/3e91fd4b4d60edd6fe3e108e8e75947de8aa060aab1de63c23017e8afeca72ef405faa6fcdd17e8aa0023261a81135d095072dc31343c57395e50450258d9fa5
  languageName: node
  linkType: hard

"@typescript-eslint/scope-manager@npm:7.18.0":
  version: 7.18.0
  resolution: "@typescript-eslint/scope-manager@npm:7.18.0"
  dependencies:
    "@typescript-eslint/types": "npm:7.18.0"
    "@typescript-eslint/visitor-keys": "npm:7.18.0"
  checksum: 10/9eb2ae5d69d9f723e706c16b2b97744fc016996a5473bed596035ac4d12429b3d24e7340a8235d704efa57f8f52e1b3b37925ff7c2e3384859d28b23a99b8bcc
  languageName: node
  linkType: hard

"@typescript-eslint/scope-manager@npm:8.19.1":
  version: 8.19.1
  resolution: "@typescript-eslint/scope-manager@npm:8.19.1"
  dependencies:
    "@typescript-eslint/types": "npm:8.19.1"
    "@typescript-eslint/visitor-keys": "npm:8.19.1"
  checksum: 10/6ffc78b15367f211eb6650459ca2bb6bfe4c1fa95a3474adc08ee9a20c250b2e0e02fd99be36bd3dad74967ecd9349e792b5d818d85735cba40f1b5c236074d1
  languageName: node
  linkType: hard

"@typescript-eslint/scope-manager@npm:8.39.0":
  version: 8.39.0
  resolution: "@typescript-eslint/scope-manager@npm:8.39.0"
  dependencies:
    "@typescript-eslint/types": "npm:8.39.0"
    "@typescript-eslint/visitor-keys": "npm:8.39.0"
  checksum: 10/c2b232a172221fc787eaef12e8ac59bbe211b7273c44c7f426c5b85dabb7470df899184a61ae35eded7d8de503d6c515856e4e5c2a616e7cf7f0ccd6a43ee255
  languageName: node
  linkType: hard

"@typescript-eslint/scope-manager@npm:8.42.0, @typescript-eslint/scope-manager@npm:^8.15.0":
  version: 8.42.0
  resolution: "@typescript-eslint/scope-manager@npm:8.42.0"
  dependencies:
    "@typescript-eslint/types": "npm:8.42.0"
    "@typescript-eslint/visitor-keys": "npm:8.42.0"
  checksum: 10/81be2d908a9d2d83bc9fe5e9219b04277b9fa466bfa7faf45dc076e4b33b39db2fb99b34b8832e329c7db48ddfdc7b78f6c92b564cd6eec99e124d3feaad8645
  languageName: node
  linkType: hard

"@typescript-eslint/tsconfig-utils@npm:8.39.0":
  version: 8.39.0
  resolution: "@typescript-eslint/tsconfig-utils@npm:8.39.0"
  peerDependencies:
    typescript: ">=4.8.4 <6.0.0"
  checksum: 10/3457da49e7292bd07b1bd41f19661ea79481f3b6f6593fb1ecf6557c38c0f5fd77df397bc096d0bad400c4142d939d5fbaf060a524f9272c749cc02c53a65852
  languageName: node
  linkType: hard

"@typescript-eslint/tsconfig-utils@npm:8.42.0, @typescript-eslint/tsconfig-utils@npm:^8.39.0, @typescript-eslint/tsconfig-utils@npm:^8.42.0":
  version: 8.42.0
  resolution: "@typescript-eslint/tsconfig-utils@npm:8.42.0"
  peerDependencies:
    typescript: ">=4.8.4 <6.0.0"
  checksum: 10/927aa127983a62ddcbfbcd18806fd278e0bf18fade3cca658946f9ff4915e6a5c5cc85926afaa490512c88dd2950b2059f22b50b6d1f4461c9dbd755a4c71c1c
  languageName: node
  linkType: hard

"@typescript-eslint/type-utils@npm:8.39.0":
  version: 8.39.0
  resolution: "@typescript-eslint/type-utils@npm:8.39.0"
  dependencies:
    "@typescript-eslint/types": "npm:8.39.0"
    "@typescript-eslint/typescript-estree": "npm:8.39.0"
    "@typescript-eslint/utils": "npm:8.39.0"
    debug: "npm:^4.3.4"
    ts-api-utils: "npm:^2.1.0"
  peerDependencies:
    eslint: ^8.57.0 || ^9.0.0
    typescript: ">=4.8.4 <6.0.0"
  checksum: 10/3efe4001b6b89bc8a32245fffc16b6aa6f1a5d571d7a103991fdb9f41afb1e7a3319ff3876182658a4296032a1475f8aa7853f6ad489e1491a1dda1d5c1da95e
  languageName: node
  linkType: hard

"@typescript-eslint/types@npm:7.18.0":
  version: 7.18.0
  resolution: "@typescript-eslint/types@npm:7.18.0"
  checksum: 10/0e30c73a3cc3c67dd06360a5a12fd12cee831e4092750eec3d6c031bdc4feafcb0ab1d882910a73e66b451a4f6e1dd015e9e2c4d45bf6bf716a474e5d123ddf0
  languageName: node
  linkType: hard

"@typescript-eslint/types@npm:8.19.1":
  version: 8.19.1
  resolution: "@typescript-eslint/types@npm:8.19.1"
  checksum: 10/5833a5f8fdac4a490dd3906a0243a0713fbf138fabb451870c70b0b089c539a9624b467b0913ddc0a225a8284342e7fd31cd506dec53c1a6d8f3c8c8902b9cae
  languageName: node
  linkType: hard

"@typescript-eslint/types@npm:8.39.0":
  version: 8.39.0
  resolution: "@typescript-eslint/types@npm:8.39.0"
  checksum: 10/b08a42e8b5cc57f9b950150433386ac5da03d7f5e24b743fa0cb55f5672f314b5defa3cf9b1ed82af8e4de1265c9c79deab304910104091a24d41c70f2d98ff9
  languageName: node
  linkType: hard

"@typescript-eslint/types@npm:8.42.0, @typescript-eslint/types@npm:^8.39.0, @typescript-eslint/types@npm:^8.42.0":
  version: 8.42.0
  resolution: "@typescript-eslint/types@npm:8.42.0"
  checksum: 10/7c39a35e5bb7083070872edc797ea60a3d6ceff0e3bdf85701919b71da83a51963562053a4b35c9e2a2b08c138fb595e14bc0b5c450e671a26059b58f8d8b4f4
  languageName: node
  linkType: hard

"@typescript-eslint/typescript-estree@npm:7.18.0":
  version: 7.18.0
  resolution: "@typescript-eslint/typescript-estree@npm:7.18.0"
  dependencies:
    "@typescript-eslint/types": "npm:7.18.0"
    "@typescript-eslint/visitor-keys": "npm:7.18.0"
    debug: "npm:^4.3.4"
    globby: "npm:^11.1.0"
    is-glob: "npm:^4.0.3"
    minimatch: "npm:^9.0.4"
    semver: "npm:^7.6.0"
    ts-api-utils: "npm:^1.3.0"
  peerDependenciesMeta:
    typescript:
      optional: true
  checksum: 10/b01e66235a91aa4439d02081d4a5f8b4a7cf9cb24f26b334812f657e3c603493e5f41e5c1e89cf4efae7d64509fa1f73affc16afc5e15cb7f83f724577c82036
  languageName: node
  linkType: hard

"@typescript-eslint/typescript-estree@npm:8.19.1":
  version: 8.19.1
  resolution: "@typescript-eslint/typescript-estree@npm:8.19.1"
  dependencies:
    "@typescript-eslint/types": "npm:8.19.1"
    "@typescript-eslint/visitor-keys": "npm:8.19.1"
    debug: "npm:^4.3.4"
    fast-glob: "npm:^3.3.2"
    is-glob: "npm:^4.0.3"
    minimatch: "npm:^9.0.4"
    semver: "npm:^7.6.0"
    ts-api-utils: "npm:^2.0.0"
  peerDependencies:
    typescript: ">=4.8.4 <5.8.0"
  checksum: 10/5de467452d5ef1a380d441b06cd0134652a0c98cdb4ce31b93eb589f7dc75ef60364d03fd80ca0a48d0c8b268f7258d4f6528b16fe1b89442d60a4bc960fe5f5
  languageName: node
  linkType: hard

"@typescript-eslint/typescript-estree@npm:8.39.0":
  version: 8.39.0
  resolution: "@typescript-eslint/typescript-estree@npm:8.39.0"
  dependencies:
    "@typescript-eslint/project-service": "npm:8.39.0"
    "@typescript-eslint/tsconfig-utils": "npm:8.39.0"
    "@typescript-eslint/types": "npm:8.39.0"
    "@typescript-eslint/visitor-keys": "npm:8.39.0"
    debug: "npm:^4.3.4"
    fast-glob: "npm:^3.3.2"
    is-glob: "npm:^4.0.3"
    minimatch: "npm:^9.0.4"
    semver: "npm:^7.6.0"
    ts-api-utils: "npm:^2.1.0"
  peerDependencies:
    typescript: ">=4.8.4 <6.0.0"
  checksum: 10/7e9dc461fe692a1b3a17fe0f8f3d9a361f3af0df115c2e3f72c82ee271d37107c1cddeeb976707d6fc3e24e87431c381d6045de5c187aff92c71847a22118ee8
  languageName: node
  linkType: hard

"@typescript-eslint/typescript-estree@npm:8.42.0":
  version: 8.42.0
  resolution: "@typescript-eslint/typescript-estree@npm:8.42.0"
  dependencies:
    "@typescript-eslint/project-service": "npm:8.42.0"
    "@typescript-eslint/tsconfig-utils": "npm:8.42.0"
    "@typescript-eslint/types": "npm:8.42.0"
    "@typescript-eslint/visitor-keys": "npm:8.42.0"
    debug: "npm:^4.3.4"
    fast-glob: "npm:^3.3.2"
    is-glob: "npm:^4.0.3"
    minimatch: "npm:^9.0.4"
    semver: "npm:^7.6.0"
    ts-api-utils: "npm:^2.1.0"
  peerDependencies:
    typescript: ">=4.8.4 <6.0.0"
  checksum: 10/9bb5df97a2ac31e6e3ee6941e10702498a76d23235ba28a23d93e09aa75a2cbcd40dc74935d86706c8e2e55e1a8b6a34bb9fb234461920ed3d8a5abed68ba36b
  languageName: node
  linkType: hard

"@typescript-eslint/utils@npm:8.19.1":
  version: 8.19.1
  resolution: "@typescript-eslint/utils@npm:8.19.1"
  dependencies:
    "@eslint-community/eslint-utils": "npm:^4.4.0"
    "@typescript-eslint/scope-manager": "npm:8.19.1"
    "@typescript-eslint/types": "npm:8.19.1"
    "@typescript-eslint/typescript-estree": "npm:8.19.1"
  peerDependencies:
    eslint: ^8.57.0 || ^9.0.0
    typescript: ">=4.8.4 <5.8.0"
  checksum: 10/bb92116a53fe143ee87e830941afb21d4222a64ca3f2b6dac5c2d9984f981408e60e52b04c32d95208896075ac222fb4ee631c5b0c4826b87d4bd8091c421ab1
  languageName: node
  linkType: hard

"@typescript-eslint/utils@npm:8.39.0":
  version: 8.39.0
  resolution: "@typescript-eslint/utils@npm:8.39.0"
  dependencies:
    "@eslint-community/eslint-utils": "npm:^4.7.0"
    "@typescript-eslint/scope-manager": "npm:8.39.0"
    "@typescript-eslint/types": "npm:8.39.0"
    "@typescript-eslint/typescript-estree": "npm:8.39.0"
  peerDependencies:
    eslint: ^8.57.0 || ^9.0.0
    typescript: ">=4.8.4 <6.0.0"
  checksum: 10/ed340f36fa0788fbc2ca1be6676e05be8f3f91497617701f0a77b61b94622f6ea3606fff4871623dfde261811cccc30e4dbe567f9400d056185a7f47054d903c
  languageName: node
  linkType: hard

"@typescript-eslint/utils@npm:^6.0.0 || ^7.0.0 || ^8.0.0, @typescript-eslint/utils@npm:^8.15.0, @typescript-eslint/utils@npm:^8.23.0, @typescript-eslint/utils@npm:^8.37.0, @typescript-eslint/utils@npm:^8.8.1":
  version: 8.42.0
  resolution: "@typescript-eslint/utils@npm:8.42.0"
  dependencies:
    "@eslint-community/eslint-utils": "npm:^4.7.0"
    "@typescript-eslint/scope-manager": "npm:8.42.0"
    "@typescript-eslint/types": "npm:8.42.0"
    "@typescript-eslint/typescript-estree": "npm:8.42.0"
  peerDependencies:
    eslint: ^8.57.0 || ^9.0.0
    typescript: ">=4.8.4 <6.0.0"
  checksum: 10/41c6c0d01c414c94d7109e21deee73b416547b3be26240d0237a3004c6198f146afefc75feee5333bc957ece6a0856518750655e794fd68c96feec1001edbfe8
  languageName: node
  linkType: hard

"@typescript-eslint/utils@npm:^7.7.1":
  version: 7.18.0
  resolution: "@typescript-eslint/utils@npm:7.18.0"
  dependencies:
    "@eslint-community/eslint-utils": "npm:^4.4.0"
    "@typescript-eslint/scope-manager": "npm:7.18.0"
    "@typescript-eslint/types": "npm:7.18.0"
    "@typescript-eslint/typescript-estree": "npm:7.18.0"
  peerDependencies:
    eslint: ^8.56.0
  checksum: 10/f43fedb4f4d2e3836bdf137889449063a55c0ece74fdb283929cd376197b992313be8ef4df920c1c801b5c3076b92964c84c6c3b9b749d263b648d0011f5926e
  languageName: node
  linkType: hard

"@typescript-eslint/visitor-keys@npm:7.18.0":
  version: 7.18.0
  resolution: "@typescript-eslint/visitor-keys@npm:7.18.0"
  dependencies:
    "@typescript-eslint/types": "npm:7.18.0"
    eslint-visitor-keys: "npm:^3.4.3"
  checksum: 10/b7cfe6fdeae86c507357ac6b2357813c64fb2fbf1aaf844393ba82f73a16e2599b41981b34200d9fc7765d70bc3a8181d76b503051e53f04bcb7c9afef637eab
  languageName: node
  linkType: hard

"@typescript-eslint/visitor-keys@npm:8.19.1":
  version: 8.19.1
  resolution: "@typescript-eslint/visitor-keys@npm:8.19.1"
  dependencies:
    "@typescript-eslint/types": "npm:8.19.1"
    eslint-visitor-keys: "npm:^4.2.0"
  checksum: 10/510eb196e7b7d59d3981d672a75454615159e931fe78e2a64b09607c3cfa45110709b0eb5ac3dd271d757a0d98cf4868ad2f45bf9193f96e9efec3efa92a19c1
  languageName: node
  linkType: hard

"@typescript-eslint/visitor-keys@npm:8.39.0":
  version: 8.39.0
  resolution: "@typescript-eslint/visitor-keys@npm:8.39.0"
  dependencies:
    "@typescript-eslint/types": "npm:8.39.0"
    eslint-visitor-keys: "npm:^4.2.1"
  checksum: 10/2eb89b9e4d531d52de414591869bc208b45dd71b5f758302f176ef92bc3b922e60be5a046a2788cc0e16724631b2dc95aad849b866716a9c7a6361f994c97379
  languageName: node
  linkType: hard

"@typescript-eslint/visitor-keys@npm:8.42.0":
  version: 8.42.0
  resolution: "@typescript-eslint/visitor-keys@npm:8.42.0"
  dependencies:
    "@typescript-eslint/types": "npm:8.42.0"
    eslint-visitor-keys: "npm:^4.2.1"
  checksum: 10/ef3aeabf7b01eb72e176053a4fe7a4c4f0769a9f58d1f7a920c97d365305b950c402ad34227209781996ae187652ccf0f47c31015f992c502b5fa898a9d44bd5
  languageName: node
  linkType: hard

"@unrs/resolver-binding-android-arm-eabi@npm:1.11.1":
  version: 1.11.1
  resolution: "@unrs/resolver-binding-android-arm-eabi@npm:1.11.1"
  conditions: os=android & cpu=arm
  languageName: node
  linkType: hard

"@unrs/resolver-binding-android-arm64@npm:1.11.1":
  version: 1.11.1
  resolution: "@unrs/resolver-binding-android-arm64@npm:1.11.1"
  conditions: os=android & cpu=arm64
  languageName: node
  linkType: hard

"@unrs/resolver-binding-darwin-arm64@npm:1.11.1":
  version: 1.11.1
  resolution: "@unrs/resolver-binding-darwin-arm64@npm:1.11.1"
  conditions: os=darwin & cpu=arm64
  languageName: node
  linkType: hard

"@unrs/resolver-binding-darwin-x64@npm:1.11.1":
  version: 1.11.1
  resolution: "@unrs/resolver-binding-darwin-x64@npm:1.11.1"
  conditions: os=darwin & cpu=x64
  languageName: node
  linkType: hard

"@unrs/resolver-binding-freebsd-x64@npm:1.11.1":
  version: 1.11.1
  resolution: "@unrs/resolver-binding-freebsd-x64@npm:1.11.1"
  conditions: os=freebsd & cpu=x64
  languageName: node
  linkType: hard

"@unrs/resolver-binding-linux-arm-gnueabihf@npm:1.11.1":
  version: 1.11.1
  resolution: "@unrs/resolver-binding-linux-arm-gnueabihf@npm:1.11.1"
  conditions: os=linux & cpu=arm
  languageName: node
  linkType: hard

"@unrs/resolver-binding-linux-arm-musleabihf@npm:1.11.1":
  version: 1.11.1
  resolution: "@unrs/resolver-binding-linux-arm-musleabihf@npm:1.11.1"
  conditions: os=linux & cpu=arm
  languageName: node
  linkType: hard

"@unrs/resolver-binding-linux-arm64-gnu@npm:1.11.1":
  version: 1.11.1
  resolution: "@unrs/resolver-binding-linux-arm64-gnu@npm:1.11.1"
  conditions: os=linux & cpu=arm64 & libc=glibc
  languageName: node
  linkType: hard

"@unrs/resolver-binding-linux-arm64-musl@npm:1.11.1":
  version: 1.11.1
  resolution: "@unrs/resolver-binding-linux-arm64-musl@npm:1.11.1"
  conditions: os=linux & cpu=arm64 & libc=musl
  languageName: node
  linkType: hard

"@unrs/resolver-binding-linux-ppc64-gnu@npm:1.11.1":
  version: 1.11.1
  resolution: "@unrs/resolver-binding-linux-ppc64-gnu@npm:1.11.1"
  conditions: os=linux & cpu=ppc64 & libc=glibc
  languageName: node
  linkType: hard

"@unrs/resolver-binding-linux-riscv64-gnu@npm:1.11.1":
  version: 1.11.1
  resolution: "@unrs/resolver-binding-linux-riscv64-gnu@npm:1.11.1"
  conditions: os=linux & cpu=riscv64 & libc=glibc
  languageName: node
  linkType: hard

"@unrs/resolver-binding-linux-riscv64-musl@npm:1.11.1":
  version: 1.11.1
  resolution: "@unrs/resolver-binding-linux-riscv64-musl@npm:1.11.1"
  conditions: os=linux & cpu=riscv64 & libc=musl
  languageName: node
  linkType: hard

"@unrs/resolver-binding-linux-s390x-gnu@npm:1.11.1":
  version: 1.11.1
  resolution: "@unrs/resolver-binding-linux-s390x-gnu@npm:1.11.1"
  conditions: os=linux & cpu=s390x & libc=glibc
  languageName: node
  linkType: hard

"@unrs/resolver-binding-linux-x64-gnu@npm:1.11.1":
  version: 1.11.1
  resolution: "@unrs/resolver-binding-linux-x64-gnu@npm:1.11.1"
  conditions: os=linux & cpu=x64 & libc=glibc
  languageName: node
  linkType: hard

"@unrs/resolver-binding-linux-x64-musl@npm:1.11.1":
  version: 1.11.1
  resolution: "@unrs/resolver-binding-linux-x64-musl@npm:1.11.1"
  conditions: os=linux & cpu=x64 & libc=musl
  languageName: node
  linkType: hard

"@unrs/resolver-binding-wasm32-wasi@npm:1.11.1":
  version: 1.11.1
  resolution: "@unrs/resolver-binding-wasm32-wasi@npm:1.11.1"
  dependencies:
    "@napi-rs/wasm-runtime": "npm:^0.2.11"
  conditions: cpu=wasm32
  languageName: node
  linkType: hard

"@unrs/resolver-binding-win32-arm64-msvc@npm:1.11.1":
  version: 1.11.1
  resolution: "@unrs/resolver-binding-win32-arm64-msvc@npm:1.11.1"
  conditions: os=win32 & cpu=arm64
  languageName: node
  linkType: hard

"@unrs/resolver-binding-win32-ia32-msvc@npm:1.11.1":
  version: 1.11.1
  resolution: "@unrs/resolver-binding-win32-ia32-msvc@npm:1.11.1"
  conditions: os=win32 & cpu=ia32
  languageName: node
  linkType: hard

"@unrs/resolver-binding-win32-x64-msvc@npm:1.11.1":
  version: 1.11.1
  resolution: "@unrs/resolver-binding-win32-x64-msvc@npm:1.11.1"
  conditions: os=win32 & cpu=x64
  languageName: node
  linkType: hard

"@vitejs/plugin-react-swc@npm:^4.0.1":
  version: 4.0.1
  resolution: "@vitejs/plugin-react-swc@npm:4.0.1"
  dependencies:
    "@rolldown/pluginutils": "npm:1.0.0-beta.32"
    "@swc/core": "npm:^1.13.2"
  peerDependencies:
    vite: ^4 || ^5 || ^6 || ^7
  checksum: 10/488f79a4e984883bd9cbe3f81adfb35805abac8e20496ef0361380c160394c8825b55186428738729cfed9b1956979b787a7b77dce7f911d9d4c83c4efc57553
  languageName: node
  linkType: hard

"@vitest-preview/dev-utils@npm:0.0.1":
  version: 0.0.1
  resolution: "@vitest-preview/dev-utils@npm:0.0.1"
  dependencies:
    open: "npm:^8.4.0"
  checksum: 10/41b04a914983069a81242fd538f2ed52d8d034c85d0f9aea90a76d90e313d04f02610516f9ace2cd30d888ddb623b8fc70b8399b400b507f03a4095753115dd7
  languageName: node
  linkType: hard

"@vitest/coverage-istanbul@npm:3.2.4":
  version: 3.2.4
  resolution: "@vitest/coverage-istanbul@npm:3.2.4"
  dependencies:
    "@istanbuljs/schema": "npm:^0.1.3"
    debug: "npm:^4.4.1"
    istanbul-lib-coverage: "npm:^3.2.2"
    istanbul-lib-instrument: "npm:^6.0.3"
    istanbul-lib-report: "npm:^3.0.1"
    istanbul-lib-source-maps: "npm:^5.0.6"
    istanbul-reports: "npm:^3.1.7"
    magicast: "npm:^0.3.5"
    test-exclude: "npm:^7.0.1"
    tinyrainbow: "npm:^2.0.0"
  peerDependencies:
    vitest: 3.2.4
  checksum: 10/c260db2c3d913bc13fc7c768b424f72d93043b5e187f93bc5efd513d8987658a8cea07294a71b6864e84cf9478c639d5c4b07e9f5767933c09e2bdfc6f9fd3aa
  languageName: node
  linkType: hard

"@vitest/coverage-v8@npm:3.2.4":
  version: 3.2.4
  resolution: "@vitest/coverage-v8@npm:3.2.4"
  dependencies:
    "@ampproject/remapping": "npm:^2.3.0"
    "@bcoe/v8-coverage": "npm:^1.0.2"
    ast-v8-to-istanbul: "npm:^0.3.3"
    debug: "npm:^4.4.1"
    istanbul-lib-coverage: "npm:^3.2.2"
    istanbul-lib-report: "npm:^3.0.1"
    istanbul-lib-source-maps: "npm:^5.0.6"
    istanbul-reports: "npm:^3.1.7"
    magic-string: "npm:^0.30.17"
    magicast: "npm:^0.3.5"
    std-env: "npm:^3.9.0"
    test-exclude: "npm:^7.0.1"
    tinyrainbow: "npm:^2.0.0"
  peerDependencies:
    "@vitest/browser": 3.2.4
    vitest: 3.2.4
  peerDependenciesMeta:
    "@vitest/browser":
      optional: true
  checksum: 10/5a5940c78eabbb36efafb9ecc50408785614768b3f74f5f88e6dd32db59a21d39e15e7cf52fae961cc2cd75e0390c8568cdb9aef35aa8593ccd057edce539ee4
  languageName: node
  linkType: hard

"@vitest/expect@npm:3.2.4":
  version: 3.2.4
  resolution: "@vitest/expect@npm:3.2.4"
  dependencies:
    "@types/chai": "npm:^5.2.2"
    "@vitest/spy": "npm:3.2.4"
    "@vitest/utils": "npm:3.2.4"
    chai: "npm:^5.2.0"
    tinyrainbow: "npm:^2.0.0"
  checksum: 10/dc69ce886c13714dfbbff78f2d2cb7eb536017e82301a73c42d573a9e9d2bf91005ac7abd9b977adf0a3bd431209f45a8ac2418029b68b0a377e092607c843ce
  languageName: node
  linkType: hard

"@vitest/mocker@npm:3.2.4":
  version: 3.2.4
  resolution: "@vitest/mocker@npm:3.2.4"
  dependencies:
    "@vitest/spy": "npm:3.2.4"
    estree-walker: "npm:^3.0.3"
    magic-string: "npm:^0.30.17"
  peerDependencies:
    msw: ^2.4.9
    vite: ^5.0.0 || ^6.0.0 || ^7.0.0-0
  peerDependenciesMeta:
    msw:
      optional: true
    vite:
      optional: true
  checksum: 10/5e92431b6ed9fc1679060e4caef3e4623f4750542a5d7cd944774f8217c4d231e273202e8aea00bab33260a5a9222ecb7005d80da0348c3c829bd37d123071a8
  languageName: node
  linkType: hard

"@vitest/pretty-format@npm:3.2.4, @vitest/pretty-format@npm:^3.2.4":
  version: 3.2.4
  resolution: "@vitest/pretty-format@npm:3.2.4"
  dependencies:
    tinyrainbow: "npm:^2.0.0"
  checksum: 10/8dd30cbf956e01fbab042fe651fb5175d9f0cd00b7b569a46cd98df89c4fec47dab12916201ad6e09a4f25f2a2ec8927a4bfdc61118593097f759c90b18a51d4
  languageName: node
  linkType: hard

"@vitest/runner@npm:3.2.4":
  version: 3.2.4
  resolution: "@vitest/runner@npm:3.2.4"
  dependencies:
    "@vitest/utils": "npm:3.2.4"
    pathe: "npm:^2.0.3"
    strip-literal: "npm:^3.0.0"
  checksum: 10/197bd55def519ef202f990b7c1618c212380831827c116240871033e4973decb780503c705ba9245a12bd8121f3ac4086ffcb3e302148b62d9bd77fd18dd1deb
  languageName: node
  linkType: hard

"@vitest/snapshot@npm:3.2.4":
  version: 3.2.4
  resolution: "@vitest/snapshot@npm:3.2.4"
  dependencies:
    "@vitest/pretty-format": "npm:3.2.4"
    magic-string: "npm:^0.30.17"
    pathe: "npm:^2.0.3"
  checksum: 10/acfb682491b9ca9345bf9fed02c2779dec43e0455a380c1966b0aad8dd81c79960902cf34621ab48fe80a0eaf8c61cc42dec186a1321dc3c9897ef2ebd5f1bc4
  languageName: node
  linkType: hard

"@vitest/spy@npm:3.2.4":
  version: 3.2.4
  resolution: "@vitest/spy@npm:3.2.4"
  dependencies:
    tinyspy: "npm:^4.0.3"
  checksum: 10/7d38c299f42a8c7e5e41652b203af98ca54e63df69c3b072d0e401d5a57fbbba3e39d8538ac1b3022c26718a6388d0bcc222bc2f07faab75942543b9247c007d
  languageName: node
  linkType: hard

"@vitest/ui@npm:3.2.4":
  version: 3.2.4
  resolution: "@vitest/ui@npm:3.2.4"
  dependencies:
    "@vitest/utils": "npm:3.2.4"
    fflate: "npm:^0.8.2"
    flatted: "npm:^3.3.3"
    pathe: "npm:^2.0.3"
    sirv: "npm:^3.0.1"
    tinyglobby: "npm:^0.2.14"
    tinyrainbow: "npm:^2.0.0"
  peerDependencies:
    vitest: 3.2.4
  checksum: 10/727ca0a1421fff894ff6d5891bab7fa70546735ac08c4a6b07d509950ecb93529a38dd58c208cf5919c55347103c79a1c65c9b1c859e9af4302cef4e1b81fe5e
  languageName: node
  linkType: hard

"@vitest/utils@npm:3.2.4":
  version: 3.2.4
  resolution: "@vitest/utils@npm:3.2.4"
  dependencies:
    "@vitest/pretty-format": "npm:3.2.4"
    loupe: "npm:^3.1.4"
    tinyrainbow: "npm:^2.0.0"
  checksum: 10/7f12ef63bd8ee13957744d1f336b0405f164ade4358bf9dfa531f75bbb58ffac02bf61aba65724311ddbc50b12ba54853a169e59c6b837c16086173b9a480710
  languageName: node
  linkType: hard

"@webgpu/types@npm:*":
  version: 0.1.64
  resolution: "@webgpu/types@npm:0.1.64"
  checksum: 10/172029dc907861c15a289f51a05cbc763c2814aef03fa9f331a09af1fc21bc47d50603257e6a68ce815a882b1b904baaf6980ee4b9882294dec95a30a1e6e965
  languageName: node
  linkType: hard

"abbrev@npm:^3.0.0":
  version: 3.0.1
  resolution: "abbrev@npm:3.0.1"
  checksum: 10/ebd2c149dda6f543b66ce3779ea612151bb3aa9d0824f169773ee9876f1ca5a4e0adbcccc7eed048c04da7998e1825e2aa76fcca92d9e67dea50ac2b0a58dc2e
  languageName: node
  linkType: hard

"abortcontroller-polyfill@npm:1.7.8":
  version: 1.7.8
  resolution: "abortcontroller-polyfill@npm:1.7.8"
  checksum: 10/8f3e35bd571fb636abf140660372b3e6e98c5b2ac6b881c5fcabd62b022a70cfb65edcc8102001770b83773d044c250d4ad2b914d70eb06ecfcf96a5c353ddb5
  languageName: node
  linkType: hard

"accepts@npm:~1.3.8":
  version: 1.3.8
  resolution: "accepts@npm:1.3.8"
  dependencies:
    mime-types: "npm:~2.1.34"
    negotiator: "npm:0.6.3"
  checksum: 10/67eaaa90e2917c58418e7a9b89392002d2b1ccd69bcca4799135d0c632f3b082f23f4ae4ddeedbced5aa59bcc7bdf4699c69ebed4593696c922462b7bc5744d6
  languageName: node
  linkType: hard

"acorn-jsx@npm:^5.3.2":
  version: 5.3.2
  resolution: "acorn-jsx@npm:5.3.2"
  peerDependencies:
    acorn: ^6.0.0 || ^7.0.0 || ^8.0.0
  checksum: 10/d4371eaef7995530b5b5ca4183ff6f062ca17901a6d3f673c9ac011b01ede37e7a1f7f61f8f5cfe709e88054757bb8f3277dc4061087cdf4f2a1f90ccbcdb977
  languageName: node
  linkType: hard

"acorn@npm:^8.14.0, acorn@npm:^8.15.0":
  version: 8.15.0
  resolution: "acorn@npm:8.15.0"
  bin:
    acorn: bin/acorn
  checksum: 10/77f2de5051a631cf1729c090e5759148459cdb76b5f5c70f890503d629cf5052357b0ce783c0f976dd8a93c5150f59f6d18df1def3f502396a20f81282482fa4
  languageName: node
  linkType: hard

"agent-base@npm:^7.1.0, agent-base@npm:^7.1.2":
  version: 7.1.4
  resolution: "agent-base@npm:7.1.4"
  checksum: 10/79bef167247789f955aaba113bae74bf64aa1e1acca4b1d6bb444bdf91d82c3e07e9451ef6a6e2e35e8f71a6f97ce33e3d855a5328eb9fad1bc3cc4cfd031ed8
  languageName: node
  linkType: hard

"ajv@npm:^6.12.4":
  version: 6.12.6
  resolution: "ajv@npm:6.12.6"
  dependencies:
    fast-deep-equal: "npm:^3.1.1"
    fast-json-stable-stringify: "npm:^2.0.0"
    json-schema-traverse: "npm:^0.4.1"
    uri-js: "npm:^4.2.2"
  checksum: 10/48d6ad21138d12eb4d16d878d630079a2bda25a04e745c07846a4ad768319533031e28872a9b3c5790fa1ec41aabdf2abed30a56e5a03ebc2cf92184b8ee306c
  languageName: node
  linkType: hard

"ansi-regex@npm:^5.0.1":
  version: 5.0.1
  resolution: "ansi-regex@npm:5.0.1"
  checksum: 10/2aa4bb54caf2d622f1afdad09441695af2a83aa3fe8b8afa581d205e57ed4261c183c4d3877cee25794443fde5876417d859c108078ab788d6af7e4fe52eb66b
  languageName: node
  linkType: hard

"ansi-regex@npm:^6.0.1":
  version: 6.2.0
  resolution: "ansi-regex@npm:6.2.0"
  checksum: 10/f1a540a85647187f21918a87ea3fc910adc6ecc2bfc180c22d9b01a04379dce3a6c1f2e5375ab78e8d7d589eb1aeb734f49171e262e90c4225f21b4415c08c8c
  languageName: node
  linkType: hard

"ansi-styles@npm:^4.0.0, ansi-styles@npm:^4.1.0":
  version: 4.3.0
  resolution: "ansi-styles@npm:4.3.0"
  dependencies:
    color-convert: "npm:^2.0.1"
  checksum: 10/b4494dfbfc7e4591b4711a396bd27e540f8153914123dccb4cdbbcb514015ada63a3809f362b9d8d4f6b17a706f1d7bea3c6f974b15fa5ae76b5b502070889ff
  languageName: node
  linkType: hard

"ansi-styles@npm:^5.0.0":
  version: 5.2.0
  resolution: "ansi-styles@npm:5.2.0"
  checksum: 10/d7f4e97ce0623aea6bc0d90dcd28881ee04cba06c570b97fd3391bd7a268eedfd9d5e2dd4fdcbdd82b8105df5faf6f24aaedc08eaf3da898e702db5948f63469
  languageName: node
  linkType: hard

"ansi-styles@npm:^6.1.0":
  version: 6.2.1
  resolution: "ansi-styles@npm:6.2.1"
  checksum: 10/70fdf883b704d17a5dfc9cde206e698c16bcd74e7f196ab821511651aee4f9f76c9514bdfa6ca3a27b5e49138b89cb222a28caf3afe4567570139577f991df32
  languageName: node
  linkType: hard

"ansis@npm:^4.1.0":
  version: 4.1.0
  resolution: "ansis@npm:4.1.0"
  checksum: 10/e2658367807edb461a4c772bdba50cef85c7b3e5f19d4d67d7a406e97b9ba222cfd4dc300fee1b05619207d4e17c809f32e97ac47429f8b4b1a6709dc6ac35ac
  languageName: node
  linkType: hard

"anymatch@npm:~3.1.2":
  version: 3.1.3
  resolution: "anymatch@npm:3.1.3"
  dependencies:
    normalize-path: "npm:^3.0.0"
    picomatch: "npm:^2.0.4"
  checksum: 10/3e044fd6d1d26545f235a9fe4d7a534e2029d8e59fa7fd9f2a6eb21230f6b5380ea1eaf55136e60cbf8e613544b3b766e7a6fa2102e2a3a117505466e3025dc2
  languageName: node
  linkType: hard

"argparse@npm:^2.0.1":
  version: 2.0.1
  resolution: "argparse@npm:2.0.1"
  checksum: 10/18640244e641a417ec75a9bd38b0b2b6b95af5199aa241b131d4b2fb206f334d7ecc600bd194861610a5579084978bfcbb02baa399dbe442d56d0ae5e60dbaef
  languageName: node
  linkType: hard

"aria-query@npm:5.1.3":
  version: 5.1.3
  resolution: "aria-query@npm:5.1.3"
  dependencies:
    deep-equal: "npm:^2.0.5"
  checksum: 10/e5da608a7c4954bfece2d879342b6c218b6b207e2d9e5af270b5e38ef8418f02d122afdc948b68e32649b849a38377785252059090d66fa8081da95d1609c0d2
  languageName: node
  linkType: hard

"aria-query@npm:^5.0.0, aria-query@npm:^5.3.2":
  version: 5.3.2
  resolution: "aria-query@npm:5.3.2"
  checksum: 10/b2fe9bc98bd401bc322ccb99717c1ae2aaf53ea0d468d6e7aebdc02fac736e4a99b46971ee05b783b08ade23c675b2d8b60e4a1222a95f6e27bc4d2a0bfdcc03
  languageName: node
  linkType: hard

"array-buffer-byte-length@npm:^1.0.0, array-buffer-byte-length@npm:^1.0.1, array-buffer-byte-length@npm:^1.0.2":
  version: 1.0.2
  resolution: "array-buffer-byte-length@npm:1.0.2"
  dependencies:
    call-bound: "npm:^1.0.3"
    is-array-buffer: "npm:^3.0.5"
  checksum: 10/0ae3786195c3211b423e5be8dd93357870e6fb66357d81da968c2c39ef43583ef6eece1f9cb1caccdae4806739c65dea832b44b8593414313cd76a89795fca63
  languageName: node
  linkType: hard

"array-flatten@npm:1.1.1":
  version: 1.1.1
  resolution: "array-flatten@npm:1.1.1"
  checksum: 10/********************************************************************************************************************************
  languageName: node
  linkType: hard

"array-includes@npm:^3.1.6, array-includes@npm:^3.1.8":
  version: 3.1.9
  resolution: "array-includes@npm:3.1.9"
  dependencies:
    call-bind: "npm:^1.0.8"
    call-bound: "npm:^1.0.4"
    define-properties: "npm:^1.2.1"
    es-abstract: "npm:^1.24.0"
    es-object-atoms: "npm:^1.1.1"
    get-intrinsic: "npm:^1.3.0"
    is-string: "npm:^1.1.1"
    math-intrinsics: "npm:^1.1.0"
  checksum: 10/8bfe9a58df74f326b4a76b04ee05c13d871759e888b4ee8f013145297cf5eb3c02cfa216067ebdaac5d74eb9763ac5cad77cdf2773b8ab475833701e032173aa
  languageName: node
  linkType: hard

"array-union@npm:^2.1.0":
  version: 2.1.0
  resolution: "array-union@npm:2.1.0"
  checksum: 10/5bee12395cba82da674931df6d0fea23c4aa4660cb3b338ced9f828782a65caa232573e6bf3968f23e0c5eb301764a382cef2f128b170a9dc59de0e36c39f98d
  languageName: node
  linkType: hard

"array.prototype.findlast@npm:^1.2.5":
  version: 1.2.5
  resolution: "array.prototype.findlast@npm:1.2.5"
  dependencies:
    call-bind: "npm:^1.0.7"
    define-properties: "npm:^1.2.1"
    es-abstract: "npm:^1.23.2"
    es-errors: "npm:^1.3.0"
    es-object-atoms: "npm:^1.0.0"
    es-shim-unscopables: "npm:^1.0.2"
  checksum: 10/7dffcc665aa965718ad6de7e17ac50df0c5e38798c0a5bf9340cf24feb8594df6ec6f3fcbe714c1577728a1b18b5704b15669474b27bceeca91ef06ce2a23c31
  languageName: node
  linkType: hard

"array.prototype.findlastindex@npm:^1.2.5":
  version: 1.2.6
  resolution: "array.prototype.findlastindex@npm:1.2.6"
  dependencies:
    call-bind: "npm:^1.0.8"
    call-bound: "npm:^1.0.4"
    define-properties: "npm:^1.2.1"
    es-abstract: "npm:^1.23.9"
    es-errors: "npm:^1.3.0"
    es-object-atoms: "npm:^1.1.1"
    es-shim-unscopables: "npm:^1.1.0"
  checksum: 10/5ddb6420e820bef6ddfdcc08ce780d0fd5e627e97457919c27e32359916de5a11ce12f7c55073555e503856618eaaa70845d6ca11dcba724766f38eb1c22f7a2
  languageName: node
  linkType: hard

"array.prototype.flat@npm:^1.3.1, array.prototype.flat@npm:^1.3.2":
  version: 1.3.3
  resolution: "array.prototype.flat@npm:1.3.3"
  dependencies:
    call-bind: "npm:^1.0.8"
    define-properties: "npm:^1.2.1"
    es-abstract: "npm:^1.23.5"
    es-shim-unscopables: "npm:^1.0.2"
  checksum: 10/f9b992fa0775d8f7c97abc91eb7f7b2f0ed8430dd9aeb9fdc2967ac4760cdd7fc2ef7ead6528fef40c7261e4d790e117808ce0d3e7e89e91514d4963a531cd01
  languageName: node
  linkType: hard

"array.prototype.flatmap@npm:^1.3.2, array.prototype.flatmap@npm:^1.3.3":
  version: 1.3.3
  resolution: "array.prototype.flatmap@npm:1.3.3"
  dependencies:
    call-bind: "npm:^1.0.8"
    define-properties: "npm:^1.2.1"
    es-abstract: "npm:^1.23.5"
    es-shim-unscopables: "npm:^1.0.2"
  checksum: 10/473534573aa4b37b1d80705d0ce642f5933cccf5617c9f3e8a56686e9815ba93d469138e86a1f25d2fe8af999c3d24f54d703ec1fc2db2e6778d46d0f4ac951e
  languageName: node
  linkType: hard

"array.prototype.tosorted@npm:^1.1.4":
  version: 1.1.4
  resolution: "array.prototype.tosorted@npm:1.1.4"
  dependencies:
    call-bind: "npm:^1.0.7"
    define-properties: "npm:^1.2.1"
    es-abstract: "npm:^1.23.3"
    es-errors: "npm:^1.3.0"
    es-shim-unscopables: "npm:^1.0.2"
  checksum: 10/874694e5d50e138894ff5b853e639c29b0aa42bbd355acda8e8e9cd337f1c80565f21edc15e8c727fa4c0877fd9d8783c575809e440cc4d2d19acaa048bf967d
  languageName: node
  linkType: hard

"arraybuffer.prototype.slice@npm:^1.0.4":
  version: 1.0.4
  resolution: "arraybuffer.prototype.slice@npm:1.0.4"
  dependencies:
    array-buffer-byte-length: "npm:^1.0.1"
    call-bind: "npm:^1.0.8"
    define-properties: "npm:^1.2.1"
    es-abstract: "npm:^1.23.5"
    es-errors: "npm:^1.3.0"
    get-intrinsic: "npm:^1.2.6"
    is-array-buffer: "npm:^3.0.4"
  checksum: 10/4821ebdfe7d699f910c7f09bc9fa996f09b96b80bccb4f5dd4b59deae582f6ad6e505ecef6376f8beac1eda06df2dbc89b70e82835d104d6fcabd33c1aed1ae9
  languageName: node
  linkType: hard

"assertion-error@npm:^2.0.1":
  version: 2.0.1
  resolution: "assertion-error@npm:2.0.1"
  checksum: 10/a0789dd882211b87116e81e2648ccb7f60340b34f19877dd020b39ebb4714e475eb943e14ba3e22201c221ef6645b7bfe10297e76b6ac95b48a9898c1211ce66
  languageName: node
  linkType: hard

"ast-types-flow@npm:^0.0.8":
  version: 0.0.8
  resolution: "ast-types-flow@npm:0.0.8"
  checksum: 10/85a1c24af4707871c27cfe456bd2ff7fcbe678f3d1c878ac968c9557735a171a17bdcc8c8f903ceab3fc3c49d5b3da2194e6ab0a6be7fec0e133fa028f21ba1b
  languageName: node
  linkType: hard

"ast-types@npm:^0.16.1":
  version: 0.16.1
  resolution: "ast-types@npm:0.16.1"
  dependencies:
    tslib: "npm:^2.0.1"
  checksum: 10/f569b475eb1c8cb93888cb6e7b7e36dc43fa19a77e4eb132cbff6e3eb1598ca60f850db6e60b070e5a0ee8c1559fca921dac0916e576f2f104e198793b0bdd8d
  languageName: node
  linkType: hard

"ast-v8-to-istanbul@npm:^0.3.3":
  version: 0.3.5
  resolution: "ast-v8-to-istanbul@npm:0.3.5"
  dependencies:
    "@jridgewell/trace-mapping": "npm:^0.3.30"
    estree-walker: "npm:^3.0.3"
    js-tokens: "npm:^9.0.1"
  checksum: 10/5ba88ab050ea34b823808bc29784278a01c8af6d86321ad9e5a6c527eceac613aed23bee3490255e33ad30a081888c3a3bf36497fd24d4d69d81913e0a4e3567
  languageName: node
  linkType: hard

"async-function@npm:^1.0.0":
  version: 1.0.0
  resolution: "async-function@npm:1.0.0"
  checksum: 10/1a09379937d846f0ce7614e75071c12826945d4e417db634156bf0e4673c495989302f52186dfa9767a1d9181794554717badd193ca2bbab046ef1da741d8efd
  languageName: node
  linkType: hard

"asynckit@npm:^0.4.0":
  version: 0.4.0
  resolution: "asynckit@npm:0.4.0"
  checksum: 10/3ce727cbc78f69d6a4722517a58ee926c8c21083633b1d3fdf66fd688f6c127a53a592141bd4866f9b63240a86e9d8e974b13919450bd17fa33c2d22c4558ad8
  languageName: node
  linkType: hard

"available-typed-arrays@npm:^1.0.7":
  version: 1.0.7
  resolution: "available-typed-arrays@npm:1.0.7"
  dependencies:
    possible-typed-array-names: "npm:^1.0.0"
  checksum: 10/6c9da3a66caddd83c875010a1ca8ef11eac02ba15fb592dc9418b2b5e7b77b645fa7729380a92d9835c2f05f2ca1b6251f39b993e0feb3f1517c74fa1af02cab
  languageName: node
  linkType: hard

"axe-core@npm:^4.10.0":
  version: 4.10.3
  resolution: "axe-core@npm:4.10.3"
  checksum: 10/9ff51ad0fd0fdec5c0247ea74e8ace5990b54c7f01f8fa3e5cd8ba98b0db24d8ebd7bab4a9bd4d75c28c4edcd1eac455b44c8c6c258c6a98f3d2f88bc60af4cc
  languageName: node
  linkType: hard

"axios@npm:^1.8.3":
  version: 1.11.0
  resolution: "axios@npm:1.11.0"
  dependencies:
    follow-redirects: "npm:^1.15.6"
    form-data: "npm:^4.0.4"
    proxy-from-env: "npm:^1.1.0"
  checksum: 10/232df4af7a4e4e07baa84621b9cc4b0c518a757b4eacc7f635c0eb3642cb98dff347326739f24b891b3b4481b7b838c79a3a0c4819c9fbc1fc40232431b9c5dc
  languageName: node
  linkType: hard

"axobject-query@npm:^4.1.0":
  version: 4.1.0
  resolution: "axobject-query@npm:4.1.0"
  checksum: 10/e275dea9b673f71170d914f2d2a18be5d57d8d29717b629e7fedd907dcc2ebdc7a37803ff975874810bd423f222f299c020d28fde40a146f537448bf6bfecb6e
  languageName: node
  linkType: hard

"babel-dead-code-elimination@npm:^1.0.10":
  version: 1.0.10
  resolution: "babel-dead-code-elimination@npm:1.0.10"
  dependencies:
    "@babel/core": "npm:^7.23.7"
    "@babel/parser": "npm:^7.23.6"
    "@babel/traverse": "npm:^7.23.7"
    "@babel/types": "npm:^7.23.6"
  checksum: 10/28a160b7a9e48e344d403cea4feaa5b486c104a8deca076c3c733cff85c0d1e176610120c7fe770700c34c782c6e09467bc073a4962d9e060ae12f1c340a0cdc
  languageName: node
  linkType: hard

"babel-plugin-polyfill-corejs2@npm:^0.4.14":
  version: 0.4.14
  resolution: "babel-plugin-polyfill-corejs2@npm:0.4.14"
  dependencies:
    "@babel/compat-data": "npm:^7.27.7"
    "@babel/helper-define-polyfill-provider": "npm:^0.6.5"
    semver: "npm:^6.3.1"
  peerDependencies:
    "@babel/core": ^7.4.0 || ^8.0.0-0 <8.0.0
  checksum: 10/8ec00a1b821ccbfcc432630da66e98bc417f5301f4ce665269d50d245a18ad3ce8a8af2a007f28e3defcd555bb8ce65f16b0d4b6d131bd788e2b97d8b8953332
  languageName: node
  linkType: hard

"babel-plugin-polyfill-corejs3@npm:^0.13.0":
  version: 0.13.0
  resolution: "babel-plugin-polyfill-corejs3@npm:0.13.0"
  dependencies:
    "@babel/helper-define-polyfill-provider": "npm:^0.6.5"
    core-js-compat: "npm:^3.43.0"
  peerDependencies:
    "@babel/core": ^7.4.0 || ^8.0.0-0 <8.0.0
  checksum: 10/aa36f9a09521404dd0569a4cbd5f88aa4b9abff59508749abde5d09d66c746012fb94ed1e6e2c8be3710939a2a4c6293ee3be889125d7611c93e5897d9e5babd
  languageName: node
  linkType: hard

"babel-plugin-polyfill-regenerator@npm:^0.6.5":
  version: 0.6.5
  resolution: "babel-plugin-polyfill-regenerator@npm:0.6.5"
  dependencies:
    "@babel/helper-define-polyfill-provider": "npm:^0.6.5"
  peerDependencies:
    "@babel/core": ^7.4.0 || ^8.0.0-0 <8.0.0
  checksum: 10/ed1932fa9a31e0752fd10ebf48ab9513a654987cab1182890839523cb898559d24ae0578fdc475d9f995390420e64eeaa4b0427045b56949dace3c725bc66dbb
  languageName: node
  linkType: hard

"balanced-match@npm:^1.0.0":
  version: 1.0.2
  resolution: "balanced-match@npm:1.0.2"
  checksum: 10/9706c088a283058a8a99e0bf91b0a2f75497f185980d9ffa8b304de1d9e58ebda7c72c07ebf01dadedaac5b2907b2c6f566f660d62bd336c3468e960403b9d65
  languageName: node
  linkType: hard

"binary-extensions@npm:^2.0.0":
  version: 2.3.0
  resolution: "binary-extensions@npm:2.3.0"
  checksum: 10/bcad01494e8a9283abf18c1b967af65ee79b0c6a9e6fcfafebfe91dbe6e0fc7272bafb73389e198b310516ae04f7ad17d79aacf6cb4c0d5d5202a7e2e52c7d98
  languageName: node
  linkType: hard

"body-parser@npm:1.20.3":
  version: 1.20.3
  resolution: "body-parser@npm:1.20.3"
  dependencies:
    bytes: "npm:3.1.2"
    content-type: "npm:~1.0.5"
    debug: "npm:2.6.9"
    depd: "npm:2.0.0"
    destroy: "npm:1.2.0"
    http-errors: "npm:2.0.0"
    iconv-lite: "npm:0.4.24"
    on-finished: "npm:2.4.1"
    qs: "npm:6.13.0"
    raw-body: "npm:2.5.2"
    type-is: "npm:~1.6.18"
    unpipe: "npm:1.0.0"
  checksum: 10/8723e3d7a672eb50854327453bed85ac48d045f4958e81e7d470c56bf111f835b97e5b73ae9f6393d0011cc9e252771f46fd281bbabc57d33d3986edf1e6aeca
  languageName: node
  linkType: hard

"boolbase@npm:^1.0.0":
  version: 1.0.0
  resolution: "boolbase@npm:1.0.0"
  checksum: 10/3e25c80ef626c3a3487c73dbfc70ac322ec830666c9ad915d11b701142fab25ec1e63eff2c450c74347acfd2de854ccde865cd79ef4db1683f7c7b046ea43bb0
  languageName: node
  linkType: hard

"brace-expansion@npm:^1.1.7":
  version: 1.1.12
  resolution: "brace-expansion@npm:1.1.12"
  dependencies:
    balanced-match: "npm:^1.0.0"
    concat-map: "npm:0.0.1"
  checksum: 10/12cb6d6310629e3048cadb003e1aca4d8c9bb5c67c3c321bafdd7e7a50155de081f78ea3e0ed92ecc75a9015e784f301efc8132383132f4f7904ad1ac529c562
  languageName: node
  linkType: hard

"brace-expansion@npm:^2.0.1":
  version: 2.0.2
  resolution: "brace-expansion@npm:2.0.2"
  dependencies:
    balanced-match: "npm:^1.0.0"
  checksum: 10/01dff195e3646bc4b0d27b63d9bab84d2ebc06121ff5013ad6e5356daa5a9d6b60fa26cf73c74797f2dc3fbec112af13578d51f75228c1112b26c790a87b0488
  languageName: node
  linkType: hard

"braces@npm:^3.0.3, braces@npm:~3.0.2":
  version: 3.0.3
  resolution: "braces@npm:3.0.3"
  dependencies:
    fill-range: "npm:^7.1.1"
  checksum: 10/fad11a0d4697a27162840b02b1fad249c1683cbc510cd5bf1a471f2f8085c046d41094308c577a50a03a579dd99d5a6b3724c4b5e8b14df2c4443844cfcda2c6
  languageName: node
  linkType: hard

"browserslist-config-single-spa@npm:^1.0.1":
  version: 1.0.1
  resolution: "browserslist-config-single-spa@npm:1.0.1"
  checksum: 10/ab942d3338bddec824e72bfede26ef94279eb61541f39cc09e5e7861f2560420b14355bcaf6fd40dd84768e9045ea734ba51288863abe7d102be80df349969c5
  languageName: node
  linkType: hard

"browserslist@npm:^4.24.0, browserslist@npm:^4.25.3":
  version: 4.25.4
  resolution: "browserslist@npm:4.25.4"
  dependencies:
    caniuse-lite: "npm:^1.0.30001737"
    electron-to-chromium: "npm:^1.5.211"
    node-releases: "npm:^2.0.19"
    update-browserslist-db: "npm:^1.1.3"
  bin:
    browserslist: cli.js
  checksum: 10/6ee84526263204f66b4a19967d93f82f2a662c0cd951386e3859e29c6a4c10c32f2acf41940251f44a5daede56dbec91348d6153a1afab1fc052ecdb01d4adbc
  languageName: node
  linkType: hard

"buffer-from@npm:^1.0.0":
  version: 1.1.2
  resolution: "buffer-from@npm:1.1.2"
  checksum: 10/0448524a562b37d4d7ed9efd91685a5b77a50672c556ea254ac9a6d30e3403a517d8981f10e565db24e8339413b43c97ca2951f10e399c6125a0d8911f5679bb
  languageName: node
  linkType: hard

"builtin-modules@npm:3.3.0":
  version: 3.3.0
  resolution: "builtin-modules@npm:3.3.0"
  checksum: 10/62e063ab40c0c1efccbfa9ffa31873e4f9d57408cb396a2649981a0ecbce56aabc93c28feaccbc5658c95aab2703ad1d11980e62ec2e5e72637404e1eb60f39e
  languageName: node
  linkType: hard

"bundle-require@npm:^4.0.1":
  version: 4.2.1
  resolution: "bundle-require@npm:4.2.1"
  dependencies:
    load-tsconfig: "npm:^0.2.3"
  peerDependencies:
    esbuild: ">=0.17"
  checksum: 10/e49cb6528373d4e086723bc37fb037e05e9cd529e1b3aa1c4da6c495c4725a0f74ae9cc461de35163d65dd3a6c41a0474c6e52b74b8ded4fe829c951d0784ec1
  languageName: node
  linkType: hard

"bytes@npm:3.1.2":
  version: 3.1.2
  resolution: "bytes@npm:3.1.2"
  checksum: 10/a10abf2ba70c784471d6b4f58778c0beeb2b5d405148e66affa91f23a9f13d07603d0a0354667310ae1d6dc141474ffd44e2a074be0f6e2254edb8fc21445388
  languageName: node
  linkType: hard

"cac@npm:^6.7.14":
  version: 6.7.14
  resolution: "cac@npm:6.7.14"
  checksum: 10/002769a0fbfc51c062acd2a59df465a2a947916b02ac50b56c69ec6c018ee99ac3e7f4dd7366334ea847f1ecacf4defaa61bcd2ac283db50156ce1f1d8c8ad42
  languageName: node
  linkType: hard

"cacache@npm:^19.0.1":
  version: 19.0.1
  resolution: "cacache@npm:19.0.1"
  dependencies:
    "@npmcli/fs": "npm:^4.0.0"
    fs-minipass: "npm:^3.0.0"
    glob: "npm:^10.2.2"
    lru-cache: "npm:^10.0.1"
    minipass: "npm:^7.0.3"
    minipass-collect: "npm:^2.0.1"
    minipass-flush: "npm:^1.0.5"
    minipass-pipeline: "npm:^1.2.4"
    p-map: "npm:^7.0.2"
    ssri: "npm:^12.0.0"
    tar: "npm:^7.4.3"
    unique-filename: "npm:^4.0.0"
  checksum: 10/ea026b27b13656330c2bbaa462a88181dcaa0435c1c2e705db89b31d9bdf7126049d6d0445ba746dca21454a0cfdf1d6f47fd39d34c8c8435296b30bc5738a13
  languageName: node
  linkType: hard

"call-bind-apply-helpers@npm:^1.0.0, call-bind-apply-helpers@npm:^1.0.1, call-bind-apply-helpers@npm:^1.0.2":
  version: 1.0.2
  resolution: "call-bind-apply-helpers@npm:1.0.2"
  dependencies:
    es-errors: "npm:^1.3.0"
    function-bind: "npm:^1.1.2"
  checksum: 10/00482c1f6aa7cfb30fb1dbeb13873edf81cfac7c29ed67a5957d60635a56b2a4a480f1016ddbdb3395cc37900d46037fb965043a51c5c789ffeab4fc535d18b5
  languageName: node
  linkType: hard

"call-bind@npm:^1.0.2, call-bind@npm:^1.0.5, call-bind@npm:^1.0.7, call-bind@npm:^1.0.8":
  version: 1.0.8
  resolution: "call-bind@npm:1.0.8"
  dependencies:
    call-bind-apply-helpers: "npm:^1.0.0"
    es-define-property: "npm:^1.0.0"
    get-intrinsic: "npm:^1.2.4"
    set-function-length: "npm:^1.2.2"
  checksum: 10/659b03c79bbfccf0cde3a79e7d52570724d7290209823e1ca5088f94b52192dc1836b82a324d0144612f816abb2f1734447438e38d9dafe0b3f82c2a1b9e3bce
  languageName: node
  linkType: hard

"call-bound@npm:^1.0.2, call-bound@npm:^1.0.3, call-bound@npm:^1.0.4":
  version: 1.0.4
  resolution: "call-bound@npm:1.0.4"
  dependencies:
    call-bind-apply-helpers: "npm:^1.0.2"
    get-intrinsic: "npm:^1.3.0"
  checksum: 10/ef2b96e126ec0e58a7ff694db43f4d0d44f80e641370c21549ed911fecbdbc2df3ebc9bddad918d6bbdefeafb60bb3337902006d5176d72bcd2da74820991af7
  languageName: node
  linkType: hard

"callsites@npm:^3.0.0":
  version: 3.1.0
  resolution: "callsites@npm:3.1.0"
  checksum: 10/072d17b6abb459c2ba96598918b55868af677154bec7e73d222ef95a8fdb9bbf7dae96a8421085cdad8cd190d86653b5b6dc55a4484f2e5b2e27d5e0c3fc15b3
  languageName: node
  linkType: hard

"camelcase@npm:^6.2.0":
  version: 6.3.0
  resolution: "camelcase@npm:6.3.0"
  checksum: 10/8c96818a9076434998511251dcb2761a94817ea17dbdc37f47ac080bd088fc62c7369429a19e2178b993497132c8cbcf5cc1f44ba963e76782ba469c0474938d
  languageName: node
  linkType: hard

"camelize@npm:^1.0.0":
  version: 1.0.1
  resolution: "camelize@npm:1.0.1"
  checksum: 10/0e147b4299ac6363c50050716aadfae42831257ec56ce54773ffd2a94a88abb2e2540c5ccc38345e8a39963105b76d86cb24477165a36b78c9958fb304513db3
  languageName: node
  linkType: hard

"caniuse-lite@npm:^1.0.30001737":
  version: 1.0.30001739
  resolution: "caniuse-lite@npm:1.0.30001739"
  checksum: 10/bdee0d0ba7b54dd619c3cd1a32d4e4aaeeda50625d24f020d6e148480b97e4cd5f7877e5eb41a25306583d494c206328a8eff300c752580baff4e57d78574ab5
  languageName: node
  linkType: hard

"chai@npm:^5.2.0":
  version: 5.3.3
  resolution: "chai@npm:5.3.3"
  dependencies:
    assertion-error: "npm:^2.0.1"
    check-error: "npm:^2.1.1"
    deep-eql: "npm:^5.0.1"
    loupe: "npm:^3.1.0"
    pathval: "npm:^2.0.0"
  checksum: 10/0d0ef63106083b05c7ba510697cd9991a02b8df5984a7d010ab4af10205c7a1f27d1c06bfa4679540894295ac4dcc22aa2a281e2e4cfe5133c1db379626689a2
  languageName: node
  linkType: hard

"chalk@npm:4.1.2, chalk@npm:^4.0.0, chalk@npm:^4.1.0":
  version: 4.1.2
  resolution: "chalk@npm:4.1.2"
  dependencies:
    ansi-styles: "npm:^4.1.0"
    supports-color: "npm:^7.1.0"
  checksum: 10/cb3f3e594913d63b1814d7ca7c9bafbf895f75fbf93b92991980610dfd7b48500af4e3a5d4e3a8f337990a96b168d7eb84ee55efdce965e2ee8efc20f8c8f139
  languageName: node
  linkType: hard

"check-error@npm:^2.1.1":
  version: 2.1.1
  resolution: "check-error@npm:2.1.1"
  checksum: 10/d785ed17b1d4a4796b6e75c765a9a290098cf52ff9728ce0756e8ffd4293d2e419dd30c67200aee34202463b474306913f2fcfaf1890641026d9fc6966fea27a
  languageName: node
  linkType: hard

"chokidar@npm:^3.5.3, chokidar@npm:^3.6.0":
  version: 3.6.0
  resolution: "chokidar@npm:3.6.0"
  dependencies:
    anymatch: "npm:~3.1.2"
    braces: "npm:~3.0.2"
    fsevents: "npm:~2.3.2"
    glob-parent: "npm:~5.1.2"
    is-binary-path: "npm:~2.1.0"
    is-glob: "npm:~4.0.1"
    normalize-path: "npm:~3.0.0"
    readdirp: "npm:~3.6.0"
  dependenciesMeta:
    fsevents:
      optional: true
  checksum: 10/c327fb07704443f8d15f7b4a7ce93b2f0bc0e6cea07ec28a7570aa22cd51fcf0379df589403976ea956c369f25aa82d84561947e227cd925902e1751371658df
  languageName: node
  linkType: hard

"chokidar@npm:^4.0.3":
  version: 4.0.3
  resolution: "chokidar@npm:4.0.3"
  dependencies:
    readdirp: "npm:^4.0.1"
  checksum: 10/bf2a575ea5596000e88f5db95461a9d59ad2047e939d5a4aac59dd472d126be8f1c1ff3c7654b477cf532d18f42a97279ef80ee847972fd2a25410bf00b80b59
  languageName: node
  linkType: hard

"chownr@npm:^3.0.0":
  version: 3.0.0
  resolution: "chownr@npm:3.0.0"
  checksum: 10/b63cb1f73d171d140a2ed8154ee6566c8ab775d3196b0e03a2a94b5f6a0ce7777ee5685ca56849403c8d17bd457a6540672f9a60696a6137c7a409097495b82c
  languageName: node
  linkType: hard

"clsx@npm:^2.1.1":
  version: 2.1.1
  resolution: "clsx@npm:2.1.1"
  checksum: 10/cdfb57fa6c7649bbff98d9028c2f0de2f91c86f551179541cf784b1cfdc1562dcb951955f46d54d930a3879931a980e32a46b598acaea274728dbe068deca919
  languageName: node
  linkType: hard

"color-convert@npm:^2.0.1":
  version: 2.0.1
  resolution: "color-convert@npm:2.0.1"
  dependencies:
    color-name: "npm:~1.1.4"
  checksum: 10/fa00c91b4332b294de06b443923246bccebe9fab1b253f7fe1772d37b06a2269b4039a85e309abe1fe11b267b11c08d1d0473fda3badd6167f57313af2887a64
  languageName: node
  linkType: hard

"color-name@npm:~1.1.4":
  version: 1.1.4
  resolution: "color-name@npm:1.1.4"
  checksum: 10/b0445859521eb4021cd0fb0cc1a75cecf67fceecae89b63f62b201cca8d345baf8b952c966862a9d9a2632987d4f6581f0ec8d957dfacece86f0a7919316f610
  languageName: node
  linkType: hard

"combined-stream@npm:^1.0.8":
  version: 1.0.8
  resolution: "combined-stream@npm:1.0.8"
  dependencies:
    delayed-stream: "npm:~1.0.0"
  checksum: 10/2e969e637d05d09fa50b02d74c83a1186f6914aae89e6653b62595cc75a221464f884f55f231b8f4df7a49537fba60bdc0427acd2bf324c09a1dbb84837e36e4
  languageName: node
  linkType: hard

"commander@npm:*":
  version: 14.0.0
  resolution: "commander@npm:14.0.0"
  checksum: 10/c05418bfc35a3e8b5c67bd9f75f5b773f386f9b85f83e70e7c926047f270929cb06cf13cd68f387dd6e7e23c6157de8171b28ba606abd3e6256028f1f789becf
  languageName: node
  linkType: hard

"commander@npm:^2.20.0":
  version: 2.20.3
  resolution: "commander@npm:2.20.3"
  checksum: 10/90c5b6898610cd075984c58c4f88418a4fb44af08c1b1415e9854c03171bec31b336b7f3e4cefe33de994b3f12b03c5e2d638da4316df83593b9e82554e7e95b
  languageName: node
  linkType: hard

"concat-map@npm:0.0.1":
  version: 0.0.1
  resolution: "concat-map@npm:0.0.1"
  checksum: 10/9680699c8e2b3af0ae22592cb764acaf973f292a7b71b8a06720233011853a58e256c89216a10cbe889727532fd77f8bcd49a760cedfde271b8e006c20e079f2
  languageName: node
  linkType: hard

"confusing-browser-globals@npm:^1.0.10":
  version: 1.0.11
  resolution: "confusing-browser-globals@npm:1.0.11"
  checksum: 10/3afc635abd37e566477f610e7978b15753f0e84025c25d49236f1f14d480117185516bdd40d2a2167e6bed8048641a9854964b9c067e3dcdfa6b5d0ad3c3a5ef
  languageName: node
  linkType: hard

"connect@npm:^3.7.0":
  version: 3.7.0
  resolution: "connect@npm:3.7.0"
  dependencies:
    debug: "npm:2.6.9"
    finalhandler: "npm:1.1.2"
    parseurl: "npm:~1.3.3"
    utils-merge: "npm:1.0.1"
  checksum: 10/f94818b198cc662092276ef6757dd825c59c8469c8064583525e7b81d39a3af86a01c7cb76107dfa0295dfc52b27a7ae1c40ea0e0a10189c3f8776cf08ce3a4e
  languageName: node
  linkType: hard

"content-disposition@npm:0.5.4":
  version: 0.5.4
  resolution: "content-disposition@npm:0.5.4"
  dependencies:
    safe-buffer: "npm:5.2.1"
  checksum: 10/b7f4ce176e324f19324be69b05bf6f6e411160ac94bc523b782248129eb1ef3be006f6cff431aaea5e337fe5d176ce8830b8c2a1b721626ead8933f0cbe78720
  languageName: node
  linkType: hard

"content-type@npm:~1.0.4, content-type@npm:~1.0.5":
  version: 1.0.5
  resolution: "content-type@npm:1.0.5"
  checksum: 10/585847d98dc7fb8035c02ae2cb76c7a9bd7b25f84c447e5ed55c45c2175e83617c8813871b4ee22f368126af6b2b167df655829007b21aa10302873ea9c62662
  languageName: node
  linkType: hard

"convert-source-map@npm:^2.0.0":
  version: 2.0.0
  resolution: "convert-source-map@npm:2.0.0"
  checksum: 10/c987be3ec061348cdb3c2bfb924bec86dea1eacad10550a85ca23edb0fe3556c3a61c7399114f3331ccb3499d7fd0285ab24566e5745929412983494c3926e15
  languageName: node
  linkType: hard

"cookie-es@npm:^1.2.2":
  version: 1.2.2
  resolution: "cookie-es@npm:1.2.2"
  checksum: 10/0fd742c11caa185928e450543f84df62d4b2c1fc7b5041196b57b7db04e1c6ac6585fb40e4f579a2819efefd2d6a9cbb4d17f71240d05f4dcd8f74ae81341a20
  languageName: node
  linkType: hard

"cookie-signature@npm:1.0.6":
  version: 1.0.6
  resolution: "cookie-signature@npm:1.0.6"
  checksum: 10/f4e1b0a98a27a0e6e66fd7ea4e4e9d8e038f624058371bf4499cfcd8f3980be9a121486995202ba3fca74fbed93a407d6d54d43a43f96fd28d0bd7a06761591a
  languageName: node
  linkType: hard

"cookie@npm:0.7.1":
  version: 0.7.1
  resolution: "cookie@npm:0.7.1"
  checksum: 10/aec6a6aa0781761bf55d60447d6be08861d381136a0fe94aa084fddd4f0300faa2b064df490c6798adfa1ebaef9e0af9b08a189c823e0811b8b313b3d9a03380
  languageName: node
  linkType: hard

"core-js-compat@npm:^3.43.0":
  version: 3.45.1
  resolution: "core-js-compat@npm:3.45.1"
  dependencies:
    browserslist: "npm:^4.25.3"
  checksum: 10/a6eb757ccf5091ee4cf7756c4f2ddefb506b049d89526e8150221e6d9150dc2685c34cbed42f4b15a27a92dd300fd56f75c9502cd57cfe928c1bd7a8ed961a42
  languageName: node
  linkType: hard

"core-js@npm:3.45.1":
  version: 3.45.1
  resolution: "core-js@npm:3.45.1"
  checksum: 10/b9dca79b1af8bb4f0d4af0752ea98d694fe157abaf55513fd4084df32dfd4398f0fc57898b32cdb643c1cecb87b9231c2a2ce535797c80ae328eac6d6078ee61
  languageName: node
  linkType: hard

"cosmiconfig@npm:^8.1.3":
  version: 8.3.6
  resolution: "cosmiconfig@npm:8.3.6"
  dependencies:
    import-fresh: "npm:^3.3.0"
    js-yaml: "npm:^4.1.0"
    parse-json: "npm:^5.2.0"
    path-type: "npm:^4.0.0"
  peerDependencies:
    typescript: ">=4.9.5"
  peerDependenciesMeta:
    typescript:
      optional: true
  checksum: 10/91d082baca0f33b1c085bf010f9ded4af43cbedacba8821da0fb5667184d0a848addc52c31fadd080007f904a555319c238cf5f4c03e6d58ece2e4876b2e73d6
  languageName: node
  linkType: hard

"cross-spawn@npm:^7.0.6":
  version: 7.0.6
  resolution: "cross-spawn@npm:7.0.6"
  dependencies:
    path-key: "npm:^3.1.0"
    shebang-command: "npm:^2.0.0"
    which: "npm:^2.0.1"
  checksum: 10/0d52657d7ae36eb130999dffff1168ec348687b48dd38e2ff59992ed916c88d328cf1d07ff4a4a10bc78de5e1c23f04b306d569e42f7a2293915c081e4dfee86
  languageName: node
  linkType: hard

"css-color-keywords@npm:^1.0.0":
  version: 1.0.0
  resolution: "css-color-keywords@npm:1.0.0"
  checksum: 10/8f125e3ad477bd03c77b533044bd9e8a6f7c0da52d49bbc0bbe38327b3829d6ba04d368ca49dd9ff3b667d2fc8f1698d891c198bbf8feade1a5501bf5a296408
  languageName: node
  linkType: hard

"css-select@npm:^4.2.1":
  version: 4.3.0
  resolution: "css-select@npm:4.3.0"
  dependencies:
    boolbase: "npm:^1.0.0"
    css-what: "npm:^6.0.1"
    domhandler: "npm:^4.3.1"
    domutils: "npm:^2.8.0"
    nth-check: "npm:^2.0.1"
  checksum: 10/8f7310c9af30ccaba8f72cb4a54d32232c53bf9ba05d019b693e16bfd7ba5df0affc1f4d74b1ee55923643d23b80a837eedcf60938c53356e479b04049ff9994
  languageName: node
  linkType: hard

"css-to-react-native@npm:3.2.0":
  version: 3.2.0
  resolution: "css-to-react-native@npm:3.2.0"
  dependencies:
    camelize: "npm:^1.0.0"
    css-color-keywords: "npm:^1.0.0"
    postcss-value-parser: "npm:^4.0.2"
  checksum: 10/62ef744254e333abc696efdc945ecf13ad6ba7b726d0a39c0405b2fcb86542aa2f3fe7b7b6770f67ae9679d98b159b4d66353107bf7d6144a445eafcf5fa250a
  languageName: node
  linkType: hard

"css-what@npm:^6.0.1":
  version: 6.2.2
  resolution: "css-what@npm:6.2.2"
  checksum: 10/3c5a53be94728089bd1716f915f7f96adde5dd8bf374610eb03982266f3d860bf1ebaf108cda30509d02ef748fe33eaa59aa75911e2c49ee05a85ef1f9fb5223
  languageName: node
  linkType: hard

"css.escape@npm:^1.5.1":
  version: 1.5.1
  resolution: "css.escape@npm:1.5.1"
  checksum: 10/f6d38088d870a961794a2580b2b2af1027731bb43261cfdce14f19238a88664b351cc8978abc20f06cc6bbde725699dec8deb6fe9816b139fc3f2af28719e774
  languageName: node
  linkType: hard

"cssstyle@npm:^4.2.1":
  version: 4.6.0
  resolution: "cssstyle@npm:4.6.0"
  dependencies:
    "@asamuzakjp/css-color": "npm:^3.2.0"
    rrweb-cssom: "npm:^0.8.0"
  checksum: 10/1cb25c9d66b87adb165f978b75cdeb6f225d7e31ba30a8934666046a0be037e4e7200d359bfa79d4f1a4aef1083ea09633b81bcdb36a2f2ac888e8c73ea3a289
  languageName: node
  linkType: hard

"csstype@npm:3.1.3, csstype@npm:^3.0.2, csstype@npm:^3.1.0":
  version: 3.1.3
  resolution: "csstype@npm:3.1.3"
  checksum: 10/f593cce41ff5ade23f44e77521e3a1bcc2c64107041e1bf6c3c32adc5187d0d60983292fda326154d20b01079e24931aa5b08e4467cc488b60bb1e7f6d478ade
  languageName: node
  linkType: hard

"damerau-levenshtein@npm:^1.0.8":
  version: 1.0.8
  resolution: "damerau-levenshtein@npm:1.0.8"
  checksum: 10/f4eba1c90170f96be25d95fa3857141b5f81e254f7e4d530da929217b19990ea9a0390fc53d3c1cafac9152fda78e722ea4894f765cf6216be413b5af1fbf821
  languageName: node
  linkType: hard

"data-urls@npm:^5.0.0":
  version: 5.0.0
  resolution: "data-urls@npm:5.0.0"
  dependencies:
    whatwg-mimetype: "npm:^4.0.0"
    whatwg-url: "npm:^14.0.0"
  checksum: 10/5c40568c31b02641a70204ff233bc4e42d33717485d074244a98661e5f2a1e80e38fe05a5755dfaf2ee549f2ab509d6a3af2a85f4b2ad2c984e5d176695eaf46
  languageName: node
  linkType: hard

"data-view-buffer@npm:^1.0.2":
  version: 1.0.2
  resolution: "data-view-buffer@npm:1.0.2"
  dependencies:
    call-bound: "npm:^1.0.3"
    es-errors: "npm:^1.3.0"
    is-data-view: "npm:^1.0.2"
  checksum: 10/c10b155a4e93999d3a215d08c23eea95f865e1f510b2e7748fcae1882b776df1afe8c99f483ace7fc0e5a3193ab08da138abebc9829d12003746c5a338c4d644
  languageName: node
  linkType: hard

"data-view-byte-length@npm:^1.0.2":
  version: 1.0.2
  resolution: "data-view-byte-length@npm:1.0.2"
  dependencies:
    call-bound: "npm:^1.0.3"
    es-errors: "npm:^1.3.0"
    is-data-view: "npm:^1.0.2"
  checksum: 10/2a47055fcf1ab3ec41b00b6f738c6461a841391a643c9ed9befec1117c1765b4d492661d97fb7cc899200c328949dca6ff189d2c6537d96d60e8a02dfe3c95f7
  languageName: node
  linkType: hard

"data-view-byte-offset@npm:^1.0.1":
  version: 1.0.1
  resolution: "data-view-byte-offset@npm:1.0.1"
  dependencies:
    call-bound: "npm:^1.0.2"
    es-errors: "npm:^1.3.0"
    is-data-view: "npm:^1.0.1"
  checksum: 10/fa3bdfa0968bea6711ee50375094b39f561bce3f15f9e558df59de9c25f0bdd4cddc002d9c1d70ac7772ebd36854a7e22d1761e7302a934e6f1c2263bcf44aa2
  languageName: node
  linkType: hard

"debug@npm:2.6.9":
  version: 2.6.9
  resolution: "debug@npm:2.6.9"
  dependencies:
    ms: "npm:2.0.0"
  checksum: 10/e07005f2b40e04f1bd14a3dd20520e9c4f25f60224cb006ce9d6781732c917964e9ec029fc7f1a151083cd929025ad5133814d4dc624a9aaf020effe4914ed14
  languageName: node
  linkType: hard

"debug@npm:4, debug@npm:^4.1.0, debug@npm:^4.1.1, debug@npm:^4.3.1, debug@npm:^4.3.2, debug@npm:^4.3.4, debug@npm:^4.4.0, debug@npm:^4.4.1":
  version: 4.4.1
  resolution: "debug@npm:4.4.1"
  dependencies:
    ms: "npm:^2.1.3"
  peerDependenciesMeta:
    supports-color:
      optional: true
  checksum: 10/8e2709b2144f03c7950f8804d01ccb3786373df01e406a0f66928e47001cf2d336cbed9ee137261d4f90d68d8679468c755e3548ed83ddacdc82b194d2468afe
  languageName: node
  linkType: hard

"debug@npm:^3.2.7":
  version: 3.2.7
  resolution: "debug@npm:3.2.7"
  dependencies:
    ms: "npm:^2.1.1"
  checksum: 10/d86fd7be2b85462297ea16f1934dc219335e802f629ca9a69b63ed8ed041dda492389bb2ee039217c02e5b54792b1c51aa96ae954cf28634d363a2360c7a1639
  languageName: node
  linkType: hard

"decimal.js@npm:^10.5.0":
  version: 10.6.0
  resolution: "decimal.js@npm:10.6.0"
  checksum: 10/c0d45842d47c311d11b38ce7ccc911121953d4df3ebb1465d92b31970eb4f6738a065426a06094af59bee4b0d64e42e7c8984abd57b6767c64ea90cf90bb4a69
  languageName: node
  linkType: hard

"deep-eql@npm:^5.0.1":
  version: 5.0.2
  resolution: "deep-eql@npm:5.0.2"
  checksum: 10/a529b81e2ef8821621d20a36959a0328873a3e49d393ad11f8efe8559f31239494c2eb889b80342808674c475802ba95b9d6c4c27641b9a029405104c1b59fcf
  languageName: node
  linkType: hard

"deep-equal@npm:^2.0.5":
  version: 2.2.3
  resolution: "deep-equal@npm:2.2.3"
  dependencies:
    array-buffer-byte-length: "npm:^1.0.0"
    call-bind: "npm:^1.0.5"
    es-get-iterator: "npm:^1.1.3"
    get-intrinsic: "npm:^1.2.2"
    is-arguments: "npm:^1.1.1"
    is-array-buffer: "npm:^3.0.2"
    is-date-object: "npm:^1.0.5"
    is-regex: "npm:^1.1.4"
    is-shared-array-buffer: "npm:^1.0.2"
    isarray: "npm:^2.0.5"
    object-is: "npm:^1.1.5"
    object-keys: "npm:^1.1.1"
    object.assign: "npm:^4.1.4"
    regexp.prototype.flags: "npm:^1.5.1"
    side-channel: "npm:^1.0.4"
    which-boxed-primitive: "npm:^1.0.2"
    which-collection: "npm:^1.0.1"
    which-typed-array: "npm:^1.1.13"
  checksum: 10/1ce49d0b71d0f14d8ef991a742665eccd488dfc9b3cada069d4d7a86291e591c92d2589c832811dea182b4015736b210acaaebce6184be356c1060d176f5a05f
  languageName: node
  linkType: hard

"deep-is@npm:^0.1.3":
  version: 0.1.4
  resolution: "deep-is@npm:0.1.4"
  checksum: 10/ec12d074aef5ae5e81fa470b9317c313142c9e8e2afe3f8efa124db309720db96d1d222b82b84c834e5f87e7a614b44a4684b6683583118b87c833b3be40d4d8
  languageName: node
  linkType: hard

"define-data-property@npm:^1.0.1, define-data-property@npm:^1.1.4":
  version: 1.1.4
  resolution: "define-data-property@npm:1.1.4"
  dependencies:
    es-define-property: "npm:^1.0.0"
    es-errors: "npm:^1.3.0"
    gopd: "npm:^1.0.1"
  checksum: 10/abdcb2505d80a53524ba871273e5da75e77e52af9e15b3aa65d8aad82b8a3a424dad7aee2cc0b71470ac7acf501e08defac362e8b6a73cdb4309f028061df4ae
  languageName: node
  linkType: hard

"define-lazy-prop@npm:^2.0.0":
  version: 2.0.0
  resolution: "define-lazy-prop@npm:2.0.0"
  checksum: 10/0115fdb065e0490918ba271d7339c42453d209d4cb619dfe635870d906731eff3e1ade8028bb461ea27ce8264ec5e22c6980612d332895977e89c1bbc80fcee2
  languageName: node
  linkType: hard

"define-properties@npm:^1.1.3, define-properties@npm:^1.2.1":
  version: 1.2.1
  resolution: "define-properties@npm:1.2.1"
  dependencies:
    define-data-property: "npm:^1.0.1"
    has-property-descriptors: "npm:^1.0.0"
    object-keys: "npm:^1.1.1"
  checksum: 10/b4ccd00597dd46cb2d4a379398f5b19fca84a16f3374e2249201992f36b30f6835949a9429669ee6b41b6e837205a163eadd745e472069e70dfc10f03e5fcc12
  languageName: node
  linkType: hard

"delayed-stream@npm:~1.0.0":
  version: 1.0.0
  resolution: "delayed-stream@npm:1.0.0"
  checksum: 10/46fe6e83e2cb1d85ba50bd52803c68be9bd953282fa7096f51fc29edd5d67ff84ff753c51966061e5ba7cb5e47ef6d36a91924eddb7f3f3483b1c560f77a0020
  languageName: node
  linkType: hard

"depd@npm:2.0.0":
  version: 2.0.0
  resolution: "depd@npm:2.0.0"
  checksum: 10/c0c8ff36079ce5ada64f46cc9d6fd47ebcf38241105b6e0c98f412e8ad91f084bcf906ff644cc3a4bd876ca27a62accb8b0fff72ea6ed1a414b89d8506f4a5ca
  languageName: node
  linkType: hard

"destroy@npm:1.2.0":
  version: 1.2.0
  resolution: "destroy@npm:1.2.0"
  checksum: 10/0acb300b7478a08b92d810ab229d5afe0d2f4399272045ab22affa0d99dbaf12637659411530a6fcd597a9bdac718fc94373a61a95b4651bbc7b83684a565e38
  languageName: node
  linkType: hard

"diff@npm:^8.0.2":
  version: 8.0.2
  resolution: "diff@npm:8.0.2"
  checksum: 10/82a2120d3418f97822e17a6044ccd4b99a91e26e145e8698353673d7146bd2d092bbebb79c112aae7badc7b9c526f9098cbe342f96174feb6beabdd2587b3c42
  languageName: node
  linkType: hard

"dir-glob@npm:^3.0.1":
  version: 3.0.1
  resolution: "dir-glob@npm:3.0.1"
  dependencies:
    path-type: "npm:^4.0.0"
  checksum: 10/fa05e18324510d7283f55862f3161c6759a3f2f8dbce491a2fc14c8324c498286c54282c1f0e933cb930da8419b30679389499b919122952a4f8592362ef4615
  languageName: node
  linkType: hard

"doctrine@npm:^2.1.0":
  version: 2.1.0
  resolution: "doctrine@npm:2.1.0"
  dependencies:
    esutils: "npm:^2.0.2"
  checksum: 10/555684f77e791b17173ea86e2eea45ef26c22219cb64670669c4f4bebd26dbc95cd90ec1f4159e9349a6bb9eb892ce4dde8cd0139e77bedd8bf4518238618474
  languageName: node
  linkType: hard

"dodogochi@workspace:.":
  version: 0.0.0-use.local
  resolution: "dodogochi@workspace:."
  dependencies:
    "@dodobrands/react-logger": "npm:6.5.1"
    "@dodopizza/eslint-plugin": "npm:4.1.4"
    "@dodopizza/frontend-scripts": "npm:3.5.1"
    "@dodopizza/ts-config": "npm:1.0.1"
    "@tanstack/eslint-plugin-query": "npm:5.86.0"
    "@tanstack/react-query": "npm:5.86.0"
    "@tanstack/react-query-devtools": "npm:5.86.0"
    "@tanstack/react-router": "npm:1.131.35"
    "@tanstack/react-router-devtools": "npm:1.131.35"
    "@tanstack/router-plugin": "npm:1.131.35"
    "@testing-library/dom": "npm:^9.0.0"
    "@testing-library/react": "npm:14.1.2"
    "@testing-library/user-event": "npm:14.5.1"
    "@types/node": "npm:22.13.10"
    "@types/react": "npm:^19"
    "@types/react-dom": "npm:^19"
    "@types/three": "npm:^0"
    eslint: "npm:9.24.0"
    react: "npm:19.1.1"
    react-dom: "npm:19.1.1"
    single-spa-react: "npm:6.0.2"
    styled-components: "npm:6.1.19"
    three: "npm:0.180.0"
    typescript: "npm:5.8.3"
  languageName: unknown
  linkType: soft

"dom-accessibility-api@npm:^0.5.9":
  version: 0.5.16
  resolution: "dom-accessibility-api@npm:0.5.16"
  checksum: 10/377b4a7f9eae0a5d72e1068c369c99e0e4ca17fdfd5219f3abd32a73a590749a267475a59d7b03a891f9b673c27429133a818c44b2e47e32fec024b34274e2ca
  languageName: node
  linkType: hard

"dom-accessibility-api@npm:^0.6.3":
  version: 0.6.3
  resolution: "dom-accessibility-api@npm:0.6.3"
  checksum: 10/83d3371f8226487fbad36e160d44f1d9017fb26d46faba6a06fcad15f34633fc827b8c3e99d49f71d5f3253d866e2131826866fd0a3c86626f8eccfc361881ff
  languageName: node
  linkType: hard

"dom-serializer@npm:^1.0.1":
  version: 1.4.1
  resolution: "dom-serializer@npm:1.4.1"
  dependencies:
    domelementtype: "npm:^2.0.1"
    domhandler: "npm:^4.2.0"
    entities: "npm:^2.0.0"
  checksum: 10/53b217bcfed4a0f90dd47f34f239b1c81fff53ffa39d164d722325817fdb554903b145c2d12c8421ce0df7d31c1b180caf7eacd3c86391dd925f803df8027dcc
  languageName: node
  linkType: hard

"domelementtype@npm:^2.0.1, domelementtype@npm:^2.2.0":
  version: 2.3.0
  resolution: "domelementtype@npm:2.3.0"
  checksum: 10/ee837a318ff702622f383409d1f5b25dd1024b692ef64d3096ff702e26339f8e345820f29a68bcdcea8cfee3531776b3382651232fbeae95612d6f0a75efb4f6
  languageName: node
  linkType: hard

"domhandler@npm:^4.2.0, domhandler@npm:^4.3.1":
  version: 4.3.1
  resolution: "domhandler@npm:4.3.1"
  dependencies:
    domelementtype: "npm:^2.2.0"
  checksum: 10/e0d2af7403997a3ca040a9ace4a233b75ebe321e0ef628b417e46d619d65d47781b2f2038b6c2ef6e56e73e66aec99caf6a12c7e687ecff18ef74af6dfbde5de
  languageName: node
  linkType: hard

"domutils@npm:^2.8.0":
  version: 2.8.0
  resolution: "domutils@npm:2.8.0"
  dependencies:
    dom-serializer: "npm:^1.0.1"
    domelementtype: "npm:^2.2.0"
    domhandler: "npm:^4.2.0"
  checksum: 10/1f316a03f00b09a8893d4a25d297d5cbffd02c564509dede28ef72d5ce38d93f6d61f1de88d439f31b14a1d9b42f587ed711b9e8b1b4d3bf6001399832bfc4e0
  languageName: node
  linkType: hard

"dot-case@npm:^3.0.4":
  version: 3.0.4
  resolution: "dot-case@npm:3.0.4"
  dependencies:
    no-case: "npm:^3.0.4"
    tslib: "npm:^2.0.3"
  checksum: 10/a65e3519414856df0228b9f645332f974f2bf5433370f544a681122eab59e66038fc3349b4be1cdc47152779dac71a5864f1ccda2f745e767c46e9c6543b1169
  languageName: node
  linkType: hard

"dunder-proto@npm:^1.0.0, dunder-proto@npm:^1.0.1":
  version: 1.0.1
  resolution: "dunder-proto@npm:1.0.1"
  dependencies:
    call-bind-apply-helpers: "npm:^1.0.1"
    es-errors: "npm:^1.3.0"
    gopd: "npm:^1.2.0"
  checksum: 10/5add88a3d68d42d6e6130a0cac450b7c2edbe73364bbd2fc334564418569bea97c6943a8fcd70e27130bf32afc236f30982fc4905039b703f23e9e0433c29934
  languageName: node
  linkType: hard

"eastasianwidth@npm:^0.2.0":
  version: 0.2.0
  resolution: "eastasianwidth@npm:0.2.0"
  checksum: 10/9b1d3e1baefeaf7d70799db8774149cef33b97183a6addceeba0cf6b85ba23ee2686f302f14482006df32df75d32b17c509c143a3689627929e4a8efaf483952
  languageName: node
  linkType: hard

"ee-first@npm:1.1.1":
  version: 1.1.1
  resolution: "ee-first@npm:1.1.1"
  checksum: 10/1b4cac778d64ce3b582a7e26b218afe07e207a0f9bfe13cc7395a6d307849cfe361e65033c3251e00c27dd060cab43014c2d6b2647676135e18b77d2d05b3f4f
  languageName: node
  linkType: hard

"electron-to-chromium@npm:^1.5.211":
  version: 1.5.214
  resolution: "electron-to-chromium@npm:1.5.214"
  checksum: 10/5553095d4e98a7396ada99641309e821dacc12f57482e9beb0c0b3d53329da23e7f86e4e0c3ca79b960fc6ebba8a0d0c79435616e431e4940653ef5d74f0755c
  languageName: node
  linkType: hard

"emoji-regex@npm:^8.0.0":
  version: 8.0.0
  resolution: "emoji-regex@npm:8.0.0"
  checksum: 10/c72d67a6821be15ec11997877c437491c313d924306b8da5d87d2a2bcc2cec9903cb5b04ee1a088460501d8e5b44f10df82fdc93c444101a7610b80c8b6938e1
  languageName: node
  linkType: hard

"emoji-regex@npm:^9.2.2":
  version: 9.2.2
  resolution: "emoji-regex@npm:9.2.2"
  checksum: 10/915acf859cea7131dac1b2b5c9c8e35c4849e325a1d114c30adb8cd615970f6dca0e27f64f3a4949d7d6ed86ecd79a1c5c63f02e697513cddd7b5835c90948b8
  languageName: node
  linkType: hard

"encodeurl@npm:~1.0.2":
  version: 1.0.2
  resolution: "encodeurl@npm:1.0.2"
  checksum: 10/e50e3d508cdd9c4565ba72d2012e65038e5d71bdc9198cb125beb6237b5b1ade6c0d343998da9e170fb2eae52c1bed37d4d6d98a46ea423a0cddbed5ac3f780c
  languageName: node
  linkType: hard

"encodeurl@npm:~2.0.0":
  version: 2.0.0
  resolution: "encodeurl@npm:2.0.0"
  checksum: 10/abf5cd51b78082cf8af7be6785813c33b6df2068ce5191a40ca8b1afe6a86f9230af9a9ce694a5ce4665955e5c1120871826df9c128a642e09c58d592e2807fe
  languageName: node
  linkType: hard

"encoding@npm:^0.1.13":
  version: 0.1.13
  resolution: "encoding@npm:0.1.13"
  dependencies:
    iconv-lite: "npm:^0.6.2"
  checksum: 10/bb98632f8ffa823996e508ce6a58ffcf5856330fde839ae42c9e1f436cc3b5cc651d4aeae72222916545428e54fd0f6aa8862fd8d25bdbcc4589f1e3f3715e7f
  languageName: node
  linkType: hard

"entities@npm:^2.0.0":
  version: 2.2.0
  resolution: "entities@npm:2.2.0"
  checksum: 10/2c765221ee324dbe25e1b8ca5d1bf2a4d39e750548f2e85cbf7ca1d167d709689ddf1796623e66666ae747364c11ed512c03b48c5bbe70968d30f2a4009509b7
  languageName: node
  linkType: hard

"entities@npm:^4.4.0":
  version: 4.5.0
  resolution: "entities@npm:4.5.0"
  checksum: 10/ede2a35c9bce1aeccd055a1b445d41c75a14a2bb1cd22e242f20cf04d236cdcd7f9c859eb83f76885327bfae0c25bf03303665ee1ce3d47c5927b98b0e3e3d48
  languageName: node
  linkType: hard

"entities@npm:^6.0.0":
  version: 6.0.1
  resolution: "entities@npm:6.0.1"
  checksum: 10/62af1307202884349d2867f0aac5c60d8b57102ea0b0e768b16246099512c28e239254ad772d6834e7e14cb1b6f153fc3d0c031934e3183b086c86d3838d874a
  languageName: node
  linkType: hard

"env-paths@npm:^2.2.0":
  version: 2.2.1
  resolution: "env-paths@npm:2.2.1"
  checksum: 10/65b5df55a8bab92229ab2b40dad3b387fad24613263d103a97f91c9fe43ceb21965cd3392b1ccb5d77088021e525c4e0481adb309625d0cb94ade1d1fb8dc17e
  languageName: node
  linkType: hard

"err-code@npm:^2.0.2":
  version: 2.0.3
  resolution: "err-code@npm:2.0.3"
  checksum: 10/1d20d825cdcce8d811bfbe86340f4755c02655a7feb2f13f8c880566d9d72a3f6c92c192a6867632e490d6da67b678271f46e01044996a6443e870331100dfdd
  languageName: node
  linkType: hard

"error-ex@npm:^1.3.1":
  version: 1.3.2
  resolution: "error-ex@npm:1.3.2"
  dependencies:
    is-arrayish: "npm:^0.2.1"
  checksum: 10/d547740aa29c34e753fb6fed2c5de81802438529c12b3673bd37b6bb1fe49b9b7abdc3c11e6062fe625d8a296b3cf769a80f878865e25e685f787763eede3ffb
  languageName: node
  linkType: hard

"es-abstract@npm:^1.17.5, es-abstract@npm:^1.23.2, es-abstract@npm:^1.23.3, es-abstract@npm:^1.23.5, es-abstract@npm:^1.23.6, es-abstract@npm:^1.23.9, es-abstract@npm:^1.24.0":
  version: 1.24.0
  resolution: "es-abstract@npm:1.24.0"
  dependencies:
    array-buffer-byte-length: "npm:^1.0.2"
    arraybuffer.prototype.slice: "npm:^1.0.4"
    available-typed-arrays: "npm:^1.0.7"
    call-bind: "npm:^1.0.8"
    call-bound: "npm:^1.0.4"
    data-view-buffer: "npm:^1.0.2"
    data-view-byte-length: "npm:^1.0.2"
    data-view-byte-offset: "npm:^1.0.1"
    es-define-property: "npm:^1.0.1"
    es-errors: "npm:^1.3.0"
    es-object-atoms: "npm:^1.1.1"
    es-set-tostringtag: "npm:^2.1.0"
    es-to-primitive: "npm:^1.3.0"
    function.prototype.name: "npm:^1.1.8"
    get-intrinsic: "npm:^1.3.0"
    get-proto: "npm:^1.0.1"
    get-symbol-description: "npm:^1.1.0"
    globalthis: "npm:^1.0.4"
    gopd: "npm:^1.2.0"
    has-property-descriptors: "npm:^1.0.2"
    has-proto: "npm:^1.2.0"
    has-symbols: "npm:^1.1.0"
    hasown: "npm:^2.0.2"
    internal-slot: "npm:^1.1.0"
    is-array-buffer: "npm:^3.0.5"
    is-callable: "npm:^1.2.7"
    is-data-view: "npm:^1.0.2"
    is-negative-zero: "npm:^2.0.3"
    is-regex: "npm:^1.2.1"
    is-set: "npm:^2.0.3"
    is-shared-array-buffer: "npm:^1.0.4"
    is-string: "npm:^1.1.1"
    is-typed-array: "npm:^1.1.15"
    is-weakref: "npm:^1.1.1"
    math-intrinsics: "npm:^1.1.0"
    object-inspect: "npm:^1.13.4"
    object-keys: "npm:^1.1.1"
    object.assign: "npm:^4.1.7"
    own-keys: "npm:^1.0.1"
    regexp.prototype.flags: "npm:^1.5.4"
    safe-array-concat: "npm:^1.1.3"
    safe-push-apply: "npm:^1.0.0"
    safe-regex-test: "npm:^1.1.0"
    set-proto: "npm:^1.0.0"
    stop-iteration-iterator: "npm:^1.1.0"
    string.prototype.trim: "npm:^1.2.10"
    string.prototype.trimend: "npm:^1.0.9"
    string.prototype.trimstart: "npm:^1.0.8"
    typed-array-buffer: "npm:^1.0.3"
    typed-array-byte-length: "npm:^1.0.3"
    typed-array-byte-offset: "npm:^1.0.4"
    typed-array-length: "npm:^1.0.7"
    unbox-primitive: "npm:^1.1.0"
    which-typed-array: "npm:^1.1.19"
  checksum: 10/64e07a886f7439cf5ccfc100f9716e6173e10af6071a50a5031afbdde474a3dbc9619d5965da54e55f8908746a9134a46be02af8c732d574b7b81ed3124e2daf
  languageName: node
  linkType: hard

"es-define-property@npm:^1.0.0, es-define-property@npm:^1.0.1":
  version: 1.0.1
  resolution: "es-define-property@npm:1.0.1"
  checksum: 10/f8dc9e660d90919f11084db0a893128f3592b781ce967e4fccfb8f3106cb83e400a4032c559184ec52ee1dbd4b01e7776c7cd0b3327b1961b1a4a7008920fe78
  languageName: node
  linkType: hard

"es-errors@npm:^1.3.0":
  version: 1.3.0
  resolution: "es-errors@npm:1.3.0"
  checksum: 10/96e65d640156f91b707517e8cdc454dd7d47c32833aa3e85d79f24f9eb7ea85f39b63e36216ef0114996581969b59fe609a94e30316b08f5f4df1d44134cf8d5
  languageName: node
  linkType: hard

"es-get-iterator@npm:^1.1.3":
  version: 1.1.3
  resolution: "es-get-iterator@npm:1.1.3"
  dependencies:
    call-bind: "npm:^1.0.2"
    get-intrinsic: "npm:^1.1.3"
    has-symbols: "npm:^1.0.3"
    is-arguments: "npm:^1.1.1"
    is-map: "npm:^2.0.2"
    is-set: "npm:^2.0.2"
    is-string: "npm:^1.0.7"
    isarray: "npm:^2.0.5"
    stop-iteration-iterator: "npm:^1.0.0"
  checksum: 10/bc2194befbe55725f9489098626479deee3c801eda7e83ce0dff2eb266a28dc808edb9b623ff01d31ebc1328f09d661333d86b601036692c2e3c1a6942319433
  languageName: node
  linkType: hard

"es-iterator-helpers@npm:^1.2.1":
  version: 1.2.1
  resolution: "es-iterator-helpers@npm:1.2.1"
  dependencies:
    call-bind: "npm:^1.0.8"
    call-bound: "npm:^1.0.3"
    define-properties: "npm:^1.2.1"
    es-abstract: "npm:^1.23.6"
    es-errors: "npm:^1.3.0"
    es-set-tostringtag: "npm:^2.0.3"
    function-bind: "npm:^1.1.2"
    get-intrinsic: "npm:^1.2.6"
    globalthis: "npm:^1.0.4"
    gopd: "npm:^1.2.0"
    has-property-descriptors: "npm:^1.0.2"
    has-proto: "npm:^1.2.0"
    has-symbols: "npm:^1.1.0"
    internal-slot: "npm:^1.1.0"
    iterator.prototype: "npm:^1.1.4"
    safe-array-concat: "npm:^1.1.3"
  checksum: 10/802e0e8427a05ff4a5b0c70c7fdaaeff37cdb81a28694aeb7bfb831c6ab340d8f3deeb67b96732ff9e9699ea240524d5ea8a9a6a335fcd15aa3983b27b06113f
  languageName: node
  linkType: hard

"es-module-lexer@npm:^1.7.0":
  version: 1.7.0
  resolution: "es-module-lexer@npm:1.7.0"
  checksum: 10/b6f3e576a3fed4d82b0d0ad4bbf6b3a5ad694d2e7ce8c4a069560da3db6399381eaba703616a182b16dde50ce998af64e07dcf49f2ae48153b9e07be3f107087
  languageName: node
  linkType: hard

"es-object-atoms@npm:^1.0.0, es-object-atoms@npm:^1.1.1":
  version: 1.1.1
  resolution: "es-object-atoms@npm:1.1.1"
  dependencies:
    es-errors: "npm:^1.3.0"
  checksum: 10/54fe77de288451dae51c37bfbfe3ec86732dc3778f98f3eb3bdb4bf48063b2c0b8f9c93542656986149d08aa5be3204286e2276053d19582b76753f1a2728867
  languageName: node
  linkType: hard

"es-set-tostringtag@npm:^2.0.3, es-set-tostringtag@npm:^2.1.0":
  version: 2.1.0
  resolution: "es-set-tostringtag@npm:2.1.0"
  dependencies:
    es-errors: "npm:^1.3.0"
    get-intrinsic: "npm:^1.2.6"
    has-tostringtag: "npm:^1.0.2"
    hasown: "npm:^2.0.2"
  checksum: 10/86814bf8afbcd8966653f731415888019d4bc4aca6b6c354132a7a75bb87566751e320369654a101d23a91c87a85c79b178bcf40332839bd347aff437c4fb65f
  languageName: node
  linkType: hard

"es-shim-unscopables@npm:^1.0.2, es-shim-unscopables@npm:^1.1.0":
  version: 1.1.0
  resolution: "es-shim-unscopables@npm:1.1.0"
  dependencies:
    hasown: "npm:^2.0.2"
  checksum: 10/c351f586c30bbabc62355be49564b2435468b52c3532b8a1663672e3d10dc300197e69c247869dd173e56d86423ab95fc0c10b0939cdae597094e0fdca078cba
  languageName: node
  linkType: hard

"es-to-primitive@npm:^1.3.0":
  version: 1.3.0
  resolution: "es-to-primitive@npm:1.3.0"
  dependencies:
    is-callable: "npm:^1.2.7"
    is-date-object: "npm:^1.0.5"
    is-symbol: "npm:^1.0.4"
  checksum: 10/17faf35c221aad59a16286cbf58ef6f080bf3c485dff202c490d074d8e74da07884e29b852c245d894eac84f73c58330ec956dfd6d02c0b449d75eb1012a3f9b
  languageName: node
  linkType: hard

"esbuild-android-64@npm:0.15.18":
  version: 0.15.18
  resolution: "esbuild-android-64@npm:0.15.18"
  conditions: os=android & cpu=x64
  languageName: node
  linkType: hard

"esbuild-android-arm64@npm:0.15.18":
  version: 0.15.18
  resolution: "esbuild-android-arm64@npm:0.15.18"
  conditions: os=android & cpu=arm64
  languageName: node
  linkType: hard

"esbuild-darwin-64@npm:0.15.18":
  version: 0.15.18
  resolution: "esbuild-darwin-64@npm:0.15.18"
  conditions: os=darwin & cpu=x64
  languageName: node
  linkType: hard

"esbuild-darwin-arm64@npm:0.15.18":
  version: 0.15.18
  resolution: "esbuild-darwin-arm64@npm:0.15.18"
  conditions: os=darwin & cpu=arm64
  languageName: node
  linkType: hard

"esbuild-freebsd-64@npm:0.15.18":
  version: 0.15.18
  resolution: "esbuild-freebsd-64@npm:0.15.18"
  conditions: os=freebsd & cpu=x64
  languageName: node
  linkType: hard

"esbuild-freebsd-arm64@npm:0.15.18":
  version: 0.15.18
  resolution: "esbuild-freebsd-arm64@npm:0.15.18"
  conditions: os=freebsd & cpu=arm64
  languageName: node
  linkType: hard

"esbuild-linux-32@npm:0.15.18":
  version: 0.15.18
  resolution: "esbuild-linux-32@npm:0.15.18"
  conditions: os=linux & cpu=ia32
  languageName: node
  linkType: hard

"esbuild-linux-64@npm:0.15.18":
  version: 0.15.18
  resolution: "esbuild-linux-64@npm:0.15.18"
  conditions: os=linux & cpu=x64
  languageName: node
  linkType: hard

"esbuild-linux-arm64@npm:0.15.18":
  version: 0.15.18
  resolution: "esbuild-linux-arm64@npm:0.15.18"
  conditions: os=linux & cpu=arm64
  languageName: node
  linkType: hard

"esbuild-linux-arm@npm:0.15.18":
  version: 0.15.18
  resolution: "esbuild-linux-arm@npm:0.15.18"
  conditions: os=linux & cpu=arm
  languageName: node
  linkType: hard

"esbuild-linux-mips64le@npm:0.15.18":
  version: 0.15.18
  resolution: "esbuild-linux-mips64le@npm:0.15.18"
  conditions: os=linux & cpu=mips64el
  languageName: node
  linkType: hard

"esbuild-linux-ppc64le@npm:0.15.18":
  version: 0.15.18
  resolution: "esbuild-linux-ppc64le@npm:0.15.18"
  conditions: os=linux & cpu=ppc64
  languageName: node
  linkType: hard

"esbuild-linux-riscv64@npm:0.15.18":
  version: 0.15.18
  resolution: "esbuild-linux-riscv64@npm:0.15.18"
  conditions: os=linux & cpu=riscv64
  languageName: node
  linkType: hard

"esbuild-linux-s390x@npm:0.15.18":
  version: 0.15.18
  resolution: "esbuild-linux-s390x@npm:0.15.18"
  conditions: os=linux & cpu=s390x
  languageName: node
  linkType: hard

"esbuild-netbsd-64@npm:0.15.18":
  version: 0.15.18
  resolution: "esbuild-netbsd-64@npm:0.15.18"
  conditions: os=netbsd & cpu=x64
  languageName: node
  linkType: hard

"esbuild-openbsd-64@npm:0.15.18":
  version: 0.15.18
  resolution: "esbuild-openbsd-64@npm:0.15.18"
  conditions: os=openbsd & cpu=x64
  languageName: node
  linkType: hard

"esbuild-sunos-64@npm:0.15.18":
  version: 0.15.18
  resolution: "esbuild-sunos-64@npm:0.15.18"
  conditions: os=sunos & cpu=x64
  languageName: node
  linkType: hard

"esbuild-windows-32@npm:0.15.18":
  version: 0.15.18
  resolution: "esbuild-windows-32@npm:0.15.18"
  conditions: os=win32 & cpu=ia32
  languageName: node
  linkType: hard

"esbuild-windows-64@npm:0.15.18":
  version: 0.15.18
  resolution: "esbuild-windows-64@npm:0.15.18"
  conditions: os=win32 & cpu=x64
  languageName: node
  linkType: hard

"esbuild-windows-arm64@npm:0.15.18":
  version: 0.15.18
  resolution: "esbuild-windows-arm64@npm:0.15.18"
  conditions: os=win32 & cpu=arm64
  languageName: node
  linkType: hard

"esbuild@npm:*, esbuild@npm:^0.25.0, esbuild@npm:~0.25.0":
  version: 0.25.9
  resolution: "esbuild@npm:0.25.9"
  dependencies:
    "@esbuild/aix-ppc64": "npm:0.25.9"
    "@esbuild/android-arm": "npm:0.25.9"
    "@esbuild/android-arm64": "npm:0.25.9"
    "@esbuild/android-x64": "npm:0.25.9"
    "@esbuild/darwin-arm64": "npm:0.25.9"
    "@esbuild/darwin-x64": "npm:0.25.9"
    "@esbuild/freebsd-arm64": "npm:0.25.9"
    "@esbuild/freebsd-x64": "npm:0.25.9"
    "@esbuild/linux-arm": "npm:0.25.9"
    "@esbuild/linux-arm64": "npm:0.25.9"
    "@esbuild/linux-ia32": "npm:0.25.9"
    "@esbuild/linux-loong64": "npm:0.25.9"
    "@esbuild/linux-mips64el": "npm:0.25.9"
    "@esbuild/linux-ppc64": "npm:0.25.9"
    "@esbuild/linux-riscv64": "npm:0.25.9"
    "@esbuild/linux-s390x": "npm:0.25.9"
    "@esbuild/linux-x64": "npm:0.25.9"
    "@esbuild/netbsd-arm64": "npm:0.25.9"
    "@esbuild/netbsd-x64": "npm:0.25.9"
    "@esbuild/openbsd-arm64": "npm:0.25.9"
    "@esbuild/openbsd-x64": "npm:0.25.9"
    "@esbuild/openharmony-arm64": "npm:0.25.9"
    "@esbuild/sunos-x64": "npm:0.25.9"
    "@esbuild/win32-arm64": "npm:0.25.9"
    "@esbuild/win32-ia32": "npm:0.25.9"
    "@esbuild/win32-x64": "npm:0.25.9"
  dependenciesMeta:
    "@esbuild/aix-ppc64":
      optional: true
    "@esbuild/android-arm":
      optional: true
    "@esbuild/android-arm64":
      optional: true
    "@esbuild/android-x64":
      optional: true
    "@esbuild/darwin-arm64":
      optional: true
    "@esbuild/darwin-x64":
      optional: true
    "@esbuild/freebsd-arm64":
      optional: true
    "@esbuild/freebsd-x64":
      optional: true
    "@esbuild/linux-arm":
      optional: true
    "@esbuild/linux-arm64":
      optional: true
    "@esbuild/linux-ia32":
      optional: true
    "@esbuild/linux-loong64":
      optional: true
    "@esbuild/linux-mips64el":
      optional: true
    "@esbuild/linux-ppc64":
      optional: true
    "@esbuild/linux-riscv64":
      optional: true
    "@esbuild/linux-s390x":
      optional: true
    "@esbuild/linux-x64":
      optional: true
    "@esbuild/netbsd-arm64":
      optional: true
    "@esbuild/netbsd-x64":
      optional: true
    "@esbuild/openbsd-arm64":
      optional: true
    "@esbuild/openbsd-x64":
      optional: true
    "@esbuild/openharmony-arm64":
      optional: true
    "@esbuild/sunos-x64":
      optional: true
    "@esbuild/win32-arm64":
      optional: true
    "@esbuild/win32-ia32":
      optional: true
    "@esbuild/win32-x64":
      optional: true
  bin:
    esbuild: bin/esbuild
  checksum: 10/fc174ae7f646ad413adb641c7e46f16be575e462ed209866b55d5954d382e5da839e3f3f89a8e42e2b71d48895cc636ba43523011249fe5ff9c63d8d39d3a364
  languageName: node
  linkType: hard

"esbuild@npm:^0.15.9":
  version: 0.15.18
  resolution: "esbuild@npm:0.15.18"
  dependencies:
    "@esbuild/android-arm": "npm:0.15.18"
    "@esbuild/linux-loong64": "npm:0.15.18"
    esbuild-android-64: "npm:0.15.18"
    esbuild-android-arm64: "npm:0.15.18"
    esbuild-darwin-64: "npm:0.15.18"
    esbuild-darwin-arm64: "npm:0.15.18"
    esbuild-freebsd-64: "npm:0.15.18"
    esbuild-freebsd-arm64: "npm:0.15.18"
    esbuild-linux-32: "npm:0.15.18"
    esbuild-linux-64: "npm:0.15.18"
    esbuild-linux-arm: "npm:0.15.18"
    esbuild-linux-arm64: "npm:0.15.18"
    esbuild-linux-mips64le: "npm:0.15.18"
    esbuild-linux-ppc64le: "npm:0.15.18"
    esbuild-linux-riscv64: "npm:0.15.18"
    esbuild-linux-s390x: "npm:0.15.18"
    esbuild-netbsd-64: "npm:0.15.18"
    esbuild-openbsd-64: "npm:0.15.18"
    esbuild-sunos-64: "npm:0.15.18"
    esbuild-windows-32: "npm:0.15.18"
    esbuild-windows-64: "npm:0.15.18"
    esbuild-windows-arm64: "npm:0.15.18"
  dependenciesMeta:
    "@esbuild/android-arm":
      optional: true
    "@esbuild/linux-loong64":
      optional: true
    esbuild-android-64:
      optional: true
    esbuild-android-arm64:
      optional: true
    esbuild-darwin-64:
      optional: true
    esbuild-darwin-arm64:
      optional: true
    esbuild-freebsd-64:
      optional: true
    esbuild-freebsd-arm64:
      optional: true
    esbuild-linux-32:
      optional: true
    esbuild-linux-64:
      optional: true
    esbuild-linux-arm:
      optional: true
    esbuild-linux-arm64:
      optional: true
    esbuild-linux-mips64le:
      optional: true
    esbuild-linux-ppc64le:
      optional: true
    esbuild-linux-riscv64:
      optional: true
    esbuild-linux-s390x:
      optional: true
    esbuild-netbsd-64:
      optional: true
    esbuild-openbsd-64:
      optional: true
    esbuild-sunos-64:
      optional: true
    esbuild-windows-32:
      optional: true
    esbuild-windows-64:
      optional: true
    esbuild-windows-arm64:
      optional: true
  bin:
    esbuild: bin/esbuild
  checksum: 10/8cf0b134b4f3d623b35f874ac97de7b7bd9d0184717f298a226d607dcfbcd029be438c1d0d60804944e8486604833de3c0b8ddd5eb3598a5ee70f8121ad50aee
  languageName: node
  linkType: hard

"escalade@npm:^3.2.0":
  version: 3.2.0
  resolution: "escalade@npm:3.2.0"
  checksum: 10/9d7169e3965b2f9ae46971afa392f6e5a25545ea30f2e2dd99c9b0a95a3f52b5653681a84f5b2911a413ddad2d7a93d3514165072f349b5ffc59c75a899970d6
  languageName: node
  linkType: hard

"escape-html@npm:~1.0.3":
  version: 1.0.3
  resolution: "escape-html@npm:1.0.3"
  checksum: 10/6213ca9ae00d0ab8bccb6d8d4e0a98e76237b2410302cf7df70aaa6591d509a2a37ce8998008cbecae8fc8ffaadf3fb0229535e6a145f3ce0b211d060decbb24
  languageName: node
  linkType: hard

"escape-string-regexp@npm:^4.0.0":
  version: 4.0.0
  resolution: "escape-string-regexp@npm:4.0.0"
  checksum: 10/98b48897d93060f2322108bf29db0feba7dd774be96cd069458d1453347b25ce8682ecc39859d4bca2203cc0ab19c237bcc71755eff49a0f8d90beadeeba5cc5
  languageName: node
  linkType: hard

"eslint-config-airbnb-base@npm:^15.0.0":
  version: 15.0.0
  resolution: "eslint-config-airbnb-base@npm:15.0.0"
  dependencies:
    confusing-browser-globals: "npm:^1.0.10"
    object.assign: "npm:^4.1.2"
    object.entries: "npm:^1.1.5"
    semver: "npm:^6.3.0"
  peerDependencies:
    eslint: ^7.32.0 || ^8.2.0
    eslint-plugin-import: ^2.25.2
  checksum: 10/daa68a1dcb7bff338747a952723b5fa9d159980ec3554c395a4b52a7f7d4f00a45e7b465420eb6d4d87a82cef6361e4cfd6dbb38c2f3f52f2140b6cf13654803
  languageName: node
  linkType: hard

"eslint-config-airbnb-typescript@npm:18.0.0":
  version: 18.0.0
  resolution: "eslint-config-airbnb-typescript@npm:18.0.0"
  dependencies:
    eslint-config-airbnb-base: "npm:^15.0.0"
  peerDependencies:
    "@typescript-eslint/eslint-plugin": ^7.0.0
    "@typescript-eslint/parser": ^7.0.0
    eslint: ^8.56.0
  checksum: 10/b913670baf3aa457aa1d514ea63813e76f2232a7efdb149ce96cecb10d836cadea6776a304529f1ae371d2e721479540461e89735bdde85a949e2bf62eb3187c
  languageName: node
  linkType: hard

"eslint-config-airbnb@npm:19.0.4":
  version: 19.0.4
  resolution: "eslint-config-airbnb@npm:19.0.4"
  dependencies:
    eslint-config-airbnb-base: "npm:^15.0.0"
    object.assign: "npm:^4.1.2"
    object.entries: "npm:^1.1.5"
  peerDependencies:
    eslint: ^7.32.0 || ^8.2.0
    eslint-plugin-import: ^2.25.3
    eslint-plugin-jsx-a11y: ^6.5.1
    eslint-plugin-react: ^7.28.0
    eslint-plugin-react-hooks: ^4.3.0
  checksum: 10/f2086523cfd20c42fd620c757281bd028aa8ce9dadc7293c5c23ea60947a2d3ca04404ede77b40f5a65250fe3c04502acafc4f2f6946819fe6c257d76d9644e5
  languageName: node
  linkType: hard

"eslint-config-prettier@npm:10.1.2":
  version: 10.1.2
  resolution: "eslint-config-prettier@npm:10.1.2"
  peerDependencies:
    eslint: ">=7.0.0"
  bin:
    eslint-config-prettier: bin/cli.js
  checksum: 10/7b096cbb75ff57cee933451e9c8bd2926688bc603a7d74c3d89b2bd57324cb0346c7e95ac24b17ef2dd2050bb870602c032368f11bf57c2962210418a99caf3f
  languageName: node
  linkType: hard

"eslint-import-resolver-node@npm:0.3.9, eslint-import-resolver-node@npm:^0.3.9":
  version: 0.3.9
  resolution: "eslint-import-resolver-node@npm:0.3.9"
  dependencies:
    debug: "npm:^3.2.7"
    is-core-module: "npm:^2.13.0"
    resolve: "npm:^1.22.4"
  checksum: 10/d52e08e1d96cf630957272e4f2644dcfb531e49dcfd1edd2e07e43369eb2ec7a7d4423d417beee613201206ff2efa4eb9a582b5825ee28802fc7c71fcd53ca83
  languageName: node
  linkType: hard

"eslint-import-resolver-typescript@npm:4.3.2":
  version: 4.3.2
  resolution: "eslint-import-resolver-typescript@npm:4.3.2"
  dependencies:
    debug: "npm:^4.4.0"
    get-tsconfig: "npm:^4.10.0"
    is-bun-module: "npm:^2.0.0"
    stable-hash: "npm:^0.0.5"
    tinyglobby: "npm:^0.2.12"
    unrs-resolver: "npm:^1.4.1"
  peerDependencies:
    eslint: "*"
    eslint-plugin-import: "*"
    eslint-plugin-import-x: "*"
  peerDependenciesMeta:
    eslint-plugin-import:
      optional: true
    eslint-plugin-import-x:
      optional: true
  checksum: 10/40f4d4a888b2e139943338e064ec080d6b1bb85426bcd7197e0a0d505bd34b08dfb4e0fc10620d773063325039212549c7913edbb6beed28e7ea1de62ad271d1
  languageName: node
  linkType: hard

"eslint-module-utils@npm:2.12.0":
  version: 2.12.0
  resolution: "eslint-module-utils@npm:2.12.0"
  dependencies:
    debug: "npm:^3.2.7"
  peerDependenciesMeta:
    eslint:
      optional: true
  checksum: 10/dd27791147eca17366afcb83f47d6825b6ce164abb256681e5de4ec1d7e87d8605641eb869298a0dbc70665e2446dbcc2f40d3e1631a9475dd64dd23d4ca5dee
  languageName: node
  linkType: hard

"eslint-module-utils@npm:^2.12.0":
  version: 2.12.1
  resolution: "eslint-module-utils@npm:2.12.1"
  dependencies:
    debug: "npm:^3.2.7"
  peerDependenciesMeta:
    eslint:
      optional: true
  checksum: 10/bd25d6610ec3abaa50e8f1beb0119541562bbb8dd02c035c7e887976fe1e0c5dd8175f4607ca8d86d1146df24d52a071bd3d1dd329f6902bd58df805a8ca16d3
  languageName: node
  linkType: hard

"eslint-plugin-boundaries@npm:5.0.1":
  version: 5.0.1
  resolution: "eslint-plugin-boundaries@npm:5.0.1"
  dependencies:
    chalk: "npm:4.1.2"
    eslint-import-resolver-node: "npm:0.3.9"
    eslint-module-utils: "npm:2.12.0"
    micromatch: "npm:4.0.8"
  peerDependencies:
    eslint: ">=6.0.0"
  checksum: 10/fe94d152e6eef02b4769d3f9ba4cde1067e2c1da58c78072fe7cce2282f71ba2ee7084bde1a4ed3e0abf04d74b9f6bd2a61615aedde71682e982dd3734bd7006
  languageName: node
  linkType: hard

"eslint-plugin-i18next@npm:6.1.1":
  version: 6.1.1
  resolution: "eslint-plugin-i18next@npm:6.1.1"
  dependencies:
    lodash: "npm:^4.17.21"
    requireindex: "npm:~1.1.0"
  checksum: 10/132e559985b267e2ad4ec76f3d744cc556428999bb52cbb9aa776c5c095592e5a9be5d8251c5b35bb707df1965e4920a48f72c172a6e10fc26d0b9e19a5602e6
  languageName: node
  linkType: hard

"eslint-plugin-import@npm:2.31.0":
  version: 2.31.0
  resolution: "eslint-plugin-import@npm:2.31.0"
  dependencies:
    "@rtsao/scc": "npm:^1.1.0"
    array-includes: "npm:^3.1.8"
    array.prototype.findlastindex: "npm:^1.2.5"
    array.prototype.flat: "npm:^1.3.2"
    array.prototype.flatmap: "npm:^1.3.2"
    debug: "npm:^3.2.7"
    doctrine: "npm:^2.1.0"
    eslint-import-resolver-node: "npm:^0.3.9"
    eslint-module-utils: "npm:^2.12.0"
    hasown: "npm:^2.0.2"
    is-core-module: "npm:^2.15.1"
    is-glob: "npm:^4.0.3"
    minimatch: "npm:^3.1.2"
    object.fromentries: "npm:^2.0.8"
    object.groupby: "npm:^1.0.3"
    object.values: "npm:^1.2.0"
    semver: "npm:^6.3.1"
    string.prototype.trimend: "npm:^1.0.8"
    tsconfig-paths: "npm:^3.15.0"
  peerDependencies:
    eslint: ^2 || ^3 || ^4 || ^5 || ^6 || ^7.2.0 || ^8 || ^9
  checksum: 10/6b76bd009ac2db0615d9019699d18e2a51a86cb8c1d0855a35fb1b418be23b40239e6debdc6e8c92c59f1468ed0ea8d7b85c817117a113d5cc225be8a02ad31c
  languageName: node
  linkType: hard

"eslint-plugin-jest@npm:28.11.0":
  version: 28.11.0
  resolution: "eslint-plugin-jest@npm:28.11.0"
  dependencies:
    "@typescript-eslint/utils": "npm:^6.0.0 || ^7.0.0 || ^8.0.0"
  peerDependencies:
    "@typescript-eslint/eslint-plugin": ^6.0.0 || ^7.0.0 || ^8.0.0
    eslint: ^7.0.0 || ^8.0.0 || ^9.0.0
    jest: "*"
  peerDependenciesMeta:
    "@typescript-eslint/eslint-plugin":
      optional: true
    jest:
      optional: true
  checksum: 10/7f3896ec2dc03110688bb9f359a7aa1ba1a6d9a60ffbc3642361c4aaf55afcba9ce36b6609b20b1507028c2170ffe29b0f3e9cc9b7fe12fdd233740a2f9ce0a1
  languageName: node
  linkType: hard

"eslint-plugin-json@npm:4.0.1":
  version: 4.0.1
  resolution: "eslint-plugin-json@npm:4.0.1"
  dependencies:
    lodash: "npm:^4.17.21"
    vscode-json-languageservice: "npm:^4.1.6"
  checksum: 10/d891eb5326cca9af5d4a2ebdef5e04761d9a2fca56841d533f755dd475539818d9f43cc7f1e19952276fca8493ea58b4e143ee68f75b2971e2ea8e5e244e0929
  languageName: node
  linkType: hard

"eslint-plugin-jsx-a11y@npm:6.10.2":
  version: 6.10.2
  resolution: "eslint-plugin-jsx-a11y@npm:6.10.2"
  dependencies:
    aria-query: "npm:^5.3.2"
    array-includes: "npm:^3.1.8"
    array.prototype.flatmap: "npm:^1.3.2"
    ast-types-flow: "npm:^0.0.8"
    axe-core: "npm:^4.10.0"
    axobject-query: "npm:^4.1.0"
    damerau-levenshtein: "npm:^1.0.8"
    emoji-regex: "npm:^9.2.2"
    hasown: "npm:^2.0.2"
    jsx-ast-utils: "npm:^3.3.5"
    language-tags: "npm:^1.0.9"
    minimatch: "npm:^3.1.2"
    object.fromentries: "npm:^2.0.8"
    safe-regex-test: "npm:^1.0.3"
    string.prototype.includes: "npm:^2.0.1"
  peerDependencies:
    eslint: ^3 || ^4 || ^5 || ^6 || ^7 || ^8 || ^9
  checksum: 10/388550798548d911e2286d530a29153ca00434a06fcfc0e31e0dda46a5e7960005e532fb29ce1ccbf1e394a3af3e5cf70c47ca43778861eacc5e3ed799adb79c
  languageName: node
  linkType: hard

"eslint-plugin-optimize-regex@npm:1.2.1":
  version: 1.2.1
  resolution: "eslint-plugin-optimize-regex@npm:1.2.1"
  dependencies:
    regexp-tree: "npm:^0.1.21"
  checksum: 10/239af996f4187c6ecc91f805782b36db96d8d325d49a921d8aac18cb1007c318b2d25429732dca1306b6d45d3a654faf2321216cdd053f6c666d4de64bb5f252
  languageName: node
  linkType: hard

"eslint-plugin-prefer-arrow-functions@npm:3.6.2":
  version: 3.6.2
  resolution: "eslint-plugin-prefer-arrow-functions@npm:3.6.2"
  dependencies:
    "@typescript-eslint/types": "npm:8.19.1"
    "@typescript-eslint/utils": "npm:8.19.1"
  peerDependencies:
    eslint: ">=9.17.0"
  checksum: 10/8a1dbc133f389deb7688419dd9ce59aaac2bc6519528b0ffd496c7098a8733311e9ac72a3f5eb87ded3ccaa07c0f61d6af54643812a155d9fb328f7cdc111fe0
  languageName: node
  linkType: hard

"eslint-plugin-prettier@npm:5.2.6":
  version: 5.2.6
  resolution: "eslint-plugin-prettier@npm:5.2.6"
  dependencies:
    prettier-linter-helpers: "npm:^1.0.0"
    synckit: "npm:^0.11.0"
  peerDependencies:
    "@types/eslint": ">=8.0.0"
    eslint: ">=8.0.0"
    eslint-config-prettier: ">= 7.0.0 <10.0.0 || >=10.1.0"
    prettier: ">=3.0.0"
  peerDependenciesMeta:
    "@types/eslint":
      optional: true
    eslint-config-prettier:
      optional: true
  checksum: 10/8f82a3c6bbf2db358476e745501349c8f3d5f0976f15c4af2a07dd62bb70291d29500ad09a354bb33e645c98a378d35544a92e9758aeb65530b1ec6e2dc8b8f9
  languageName: node
  linkType: hard

"eslint-plugin-promise@npm:7.2.1":
  version: 7.2.1
  resolution: "eslint-plugin-promise@npm:7.2.1"
  dependencies:
    "@eslint-community/eslint-utils": "npm:^4.4.0"
  peerDependencies:
    eslint: ^7.0.0 || ^8.0.0 || ^9.0.0
  checksum: 10/e7447159d52dbc0fdaacfad18571906bb783f9f41f497e73f9b0351e9cc79497f9a9053fbef8141d0c027c16c768a1ef7f8cd4709a4a5cbb14636e862a1ccb34
  languageName: node
  linkType: hard

"eslint-plugin-react-hooks@npm:5.2.0":
  version: 5.2.0
  resolution: "eslint-plugin-react-hooks@npm:5.2.0"
  peerDependencies:
    eslint: ^3.0.0 || ^4.0.0 || ^5.0.0 || ^6.0.0 || ^7.0.0 || ^8.0.0-0 || ^9.0.0
  checksum: 10/ebb79e9cf69ae06e3a7876536653c5e556b5fd8cd9dc49577f10a6e728360e7b6f5ce91f4339b33e93b26e3bb23805418f8b5e75db80baddd617b1dffe73bed1
  languageName: node
  linkType: hard

"eslint-plugin-react@npm:7.37.5":
  version: 7.37.5
  resolution: "eslint-plugin-react@npm:7.37.5"
  dependencies:
    array-includes: "npm:^3.1.8"
    array.prototype.findlast: "npm:^1.2.5"
    array.prototype.flatmap: "npm:^1.3.3"
    array.prototype.tosorted: "npm:^1.1.4"
    doctrine: "npm:^2.1.0"
    es-iterator-helpers: "npm:^1.2.1"
    estraverse: "npm:^5.3.0"
    hasown: "npm:^2.0.2"
    jsx-ast-utils: "npm:^2.4.1 || ^3.0.0"
    minimatch: "npm:^3.1.2"
    object.entries: "npm:^1.1.9"
    object.fromentries: "npm:^2.0.8"
    object.values: "npm:^1.2.1"
    prop-types: "npm:^15.8.1"
    resolve: "npm:^2.0.0-next.5"
    semver: "npm:^6.3.1"
    string.prototype.matchall: "npm:^4.0.12"
    string.prototype.repeat: "npm:^1.0.0"
  peerDependencies:
    eslint: ^3 || ^4 || ^5 || ^6 || ^7 || ^8 || ^9.7
  checksum: 10/ee1bd4e0ec64f29109d5a625bb703d179c82e0159c86c3f1b52fc1209d2994625a137dae303c333fb308a2e38315e44066d5204998177e31974382f9fda25d5c
  languageName: node
  linkType: hard

"eslint-plugin-simple-import-sort@npm:12.1.1":
  version: 12.1.1
  resolution: "eslint-plugin-simple-import-sort@npm:12.1.1"
  peerDependencies:
    eslint: ">=5.0.0"
  checksum: 10/2a690cea9243fbefa70345687bca8952f5e185fa459b7a8db687a908cc31082435cfee236c619d5245548fa5f89a2f2c4f8499f80512e048d2bedc60e3662d5a
  languageName: node
  linkType: hard

"eslint-plugin-sonarjs@npm:3.0.2":
  version: 3.0.2
  resolution: "eslint-plugin-sonarjs@npm:3.0.2"
  dependencies:
    "@eslint-community/regexpp": "npm:4.12.1"
    builtin-modules: "npm:3.3.0"
    bytes: "npm:3.1.2"
    functional-red-black-tree: "npm:1.0.1"
    jsx-ast-utils: "npm:3.3.5"
    minimatch: "npm:9.0.5"
    scslre: "npm:0.3.0"
    semver: "npm:7.7.1"
    typescript: "npm:^5"
  peerDependencies:
    eslint: ^8.0.0 || ^9.0.0
  checksum: 10/971ed06ff2a7f24c561f9be212f89764cec9e279f6d18157df69f7f49fff56b5f7259ea00cbfb16ad261d1ed0f1eb179dd46ba760f78f95cc2c95bff37737974
  languageName: node
  linkType: hard

"eslint-plugin-storybook@npm:0.12.0":
  version: 0.12.0
  resolution: "eslint-plugin-storybook@npm:0.12.0"
  dependencies:
    "@storybook/csf": "npm:^0.1.11"
    "@typescript-eslint/utils": "npm:^8.8.1"
    ts-dedent: "npm:^2.2.0"
  peerDependencies:
    eslint: ">=8"
  checksum: 10/278ea59565e30b74ee1d57f0a8f704906eaf40973b13999ec2c44872bb90c7505dfb12777b264940e2b480e81ace85c0532af69666e76a783b8ffa898a1d49ad
  languageName: node
  linkType: hard

"eslint-plugin-styled-components-varname@npm:1.0.1":
  version: 1.0.1
  resolution: "eslint-plugin-styled-components-varname@npm:1.0.1"
  dependencies:
    requireindex: "npm:~1.1.0"
  checksum: 10/2a4cae45d83c2c7c1e7256575038f580b7fc6fb1f5b2a1069d0ba89d653a98f70669d21fff2fad6198ea276839af84303600acb9d8c59b216f4e88cc647c51f3
  languageName: node
  linkType: hard

"eslint-plugin-testing-library@npm:7.1.1":
  version: 7.1.1
  resolution: "eslint-plugin-testing-library@npm:7.1.1"
  dependencies:
    "@typescript-eslint/scope-manager": "npm:^8.15.0"
    "@typescript-eslint/utils": "npm:^8.15.0"
  peerDependencies:
    eslint: ^8.57.0 || ^9.0.0
  checksum: 10/48a7a7f93afd16f9cf9cccaf7a1e7ba2e2ea9072d598558ce758d396c7a4d6a71e49b4ec654feef67350141f4f2737d7460c07dbfaed4eb60a09d1c7ceb11558
  languageName: node
  linkType: hard

"eslint-plugin-vitest@npm:0.5.4":
  version: 0.5.4
  resolution: "eslint-plugin-vitest@npm:0.5.4"
  dependencies:
    "@typescript-eslint/utils": "npm:^7.7.1"
  peerDependencies:
    eslint: ^8.57.0 || ^9.0.0
    vitest: "*"
  peerDependenciesMeta:
    "@typescript-eslint/eslint-plugin":
      optional: true
    vitest:
      optional: true
  checksum: 10/a81eda0b6fff5f05afa9e4e2deb114562e8a53e224293a0dd3f524c01a240a1f8b6c7284d15862c5b740adc6816a2f23e5b96fc65d95c0abd24a5ef171215589
  languageName: node
  linkType: hard

"eslint-scope@npm:^8.3.0":
  version: 8.4.0
  resolution: "eslint-scope@npm:8.4.0"
  dependencies:
    esrecurse: "npm:^4.3.0"
    estraverse: "npm:^5.2.0"
  checksum: 10/e8e611701f65375e034c62123946e628894f0b54aa8cb11abe224816389abe5cd74cf16b62b72baa36504f22d1a958b9b8b0169b82397fe2e7997674c0d09b06
  languageName: node
  linkType: hard

"eslint-visitor-keys@npm:^3.4.3":
  version: 3.4.3
  resolution: "eslint-visitor-keys@npm:3.4.3"
  checksum: 10/3f357c554a9ea794b094a09bd4187e5eacd1bc0d0653c3adeb87962c548e6a1ab8f982b86963ae1337f5d976004146536dcee5d0e2806665b193fbfbf1a9231b
  languageName: node
  linkType: hard

"eslint-visitor-keys@npm:^4.2.0, eslint-visitor-keys@npm:^4.2.1":
  version: 4.2.1
  resolution: "eslint-visitor-keys@npm:4.2.1"
  checksum: 10/3ee00fc6a7002d4b0ffd9dc99e13a6a7882c557329e6c25ab254220d71e5c9c4f89dca4695352949ea678eb1f3ba912a18ef8aac0a7fe094196fd92f441bfce2
  languageName: node
  linkType: hard

"eslint@npm:9.24.0":
  version: 9.24.0
  resolution: "eslint@npm:9.24.0"
  dependencies:
    "@eslint-community/eslint-utils": "npm:^4.2.0"
    "@eslint-community/regexpp": "npm:^4.12.1"
    "@eslint/config-array": "npm:^0.20.0"
    "@eslint/config-helpers": "npm:^0.2.0"
    "@eslint/core": "npm:^0.12.0"
    "@eslint/eslintrc": "npm:^3.3.1"
    "@eslint/js": "npm:9.24.0"
    "@eslint/plugin-kit": "npm:^0.2.7"
    "@humanfs/node": "npm:^0.16.6"
    "@humanwhocodes/module-importer": "npm:^1.0.1"
    "@humanwhocodes/retry": "npm:^0.4.2"
    "@types/estree": "npm:^1.0.6"
    "@types/json-schema": "npm:^7.0.15"
    ajv: "npm:^6.12.4"
    chalk: "npm:^4.0.0"
    cross-spawn: "npm:^7.0.6"
    debug: "npm:^4.3.2"
    escape-string-regexp: "npm:^4.0.0"
    eslint-scope: "npm:^8.3.0"
    eslint-visitor-keys: "npm:^4.2.0"
    espree: "npm:^10.3.0"
    esquery: "npm:^1.5.0"
    esutils: "npm:^2.0.2"
    fast-deep-equal: "npm:^3.1.3"
    file-entry-cache: "npm:^8.0.0"
    find-up: "npm:^5.0.0"
    glob-parent: "npm:^6.0.2"
    ignore: "npm:^5.2.0"
    imurmurhash: "npm:^0.1.4"
    is-glob: "npm:^4.0.0"
    json-stable-stringify-without-jsonify: "npm:^1.0.1"
    lodash.merge: "npm:^4.6.2"
    minimatch: "npm:^3.1.2"
    natural-compare: "npm:^1.4.0"
    optionator: "npm:^0.9.3"
  peerDependencies:
    jiti: "*"
  peerDependenciesMeta:
    jiti:
      optional: true
  bin:
    eslint: bin/eslint.js
  checksum: 10/05810e135c1f429be451a4be92283c0be204010bb0ea71edfeae1d25ff917cbc5a229144ee55853a085088c7e4092e59a28c0dae87a865ef9600ad4438861d4a
  languageName: node
  linkType: hard

"espree@npm:^10.0.1, espree@npm:^10.3.0":
  version: 10.4.0
  resolution: "espree@npm:10.4.0"
  dependencies:
    acorn: "npm:^8.15.0"
    acorn-jsx: "npm:^5.3.2"
    eslint-visitor-keys: "npm:^4.2.1"
  checksum: 10/9b355b32dbd1cc9f57121d5ee3be258fab87ebeb7c83fc6c02e5af1a74fc8c5ba79fe8c663e69ea112c3e84a1b95e6a2067ac4443ee7813bb85ac7581acb8bf9
  languageName: node
  linkType: hard

"esprima@npm:~4.0.0":
  version: 4.0.1
  resolution: "esprima@npm:4.0.1"
  bin:
    esparse: ./bin/esparse.js
    esvalidate: ./bin/esvalidate.js
  checksum: 10/f1d3c622ad992421362294f7acf866aa9409fbad4eb2e8fa230bd33944ce371d32279667b242d8b8907ec2b6ad7353a717f3c0e60e748873a34a7905174bc0eb
  languageName: node
  linkType: hard

"esquery@npm:^1.5.0":
  version: 1.6.0
  resolution: "esquery@npm:1.6.0"
  dependencies:
    estraverse: "npm:^5.1.0"
  checksum: 10/c587fb8ec9ed83f2b1bc97cf2f6854cc30bf784a79d62ba08c6e358bf22280d69aee12827521cf38e69ae9761d23fb7fde593ce315610f85655c139d99b05e5a
  languageName: node
  linkType: hard

"esrecurse@npm:^4.3.0":
  version: 4.3.0
  resolution: "esrecurse@npm:4.3.0"
  dependencies:
    estraverse: "npm:^5.2.0"
  checksum: 10/44ffcd89e714ea6b30143e7f119b104fc4d75e77ee913f34d59076b40ef2d21967f84e019f84e1fd0465b42cdbf725db449f232b5e47f29df29ed76194db8e16
  languageName: node
  linkType: hard

"estraverse@npm:^5.1.0, estraverse@npm:^5.2.0, estraverse@npm:^5.3.0":
  version: 5.3.0
  resolution: "estraverse@npm:5.3.0"
  checksum: 10/37cbe6e9a68014d34dbdc039f90d0baf72436809d02edffcc06ba3c2a12eb298048f877511353b130153e532aac8d68ba78430c0dd2f44806ebc7c014b01585e
  languageName: node
  linkType: hard

"estree-walker@npm:^2.0.2":
  version: 2.0.2
  resolution: "estree-walker@npm:2.0.2"
  checksum: 10/b02109c5d46bc2ed47de4990eef770f7457b1159a229f0999a09224d2b85ffeed2d7679cffcff90aeb4448e94b0168feb5265b209cdec29aad50a3d6e93d21e2
  languageName: node
  linkType: hard

"estree-walker@npm:^3.0.3":
  version: 3.0.3
  resolution: "estree-walker@npm:3.0.3"
  dependencies:
    "@types/estree": "npm:^1.0.0"
  checksum: 10/a65728d5727b71de172c5df323385755a16c0fdab8234dc756c3854cfee343261ddfbb72a809a5660fac8c75d960bb3e21aa898c2d7e9b19bb298482ca58a3af
  languageName: node
  linkType: hard

"esutils@npm:^2.0.2":
  version: 2.0.3
  resolution: "esutils@npm:2.0.3"
  checksum: 10/b23acd24791db11d8f65be5ea58fd9a6ce2df5120ae2da65c16cfc5331ff59d5ac4ef50af66cd4bde238881503ec839928a0135b99a036a9cdfa22d17fd56cdb
  languageName: node
  linkType: hard

"etag@npm:~1.8.1":
  version: 1.8.1
  resolution: "etag@npm:1.8.1"
  checksum: 10/571aeb3dbe0f2bbd4e4fadbdb44f325fc75335cd5f6f6b6a091e6a06a9f25ed5392f0863c5442acb0646787446e816f13cbfc6edce5b07658541dff573cab1ff
  languageName: node
  linkType: hard

"expect-type@npm:^1.2.1":
  version: 1.2.2
  resolution: "expect-type@npm:1.2.2"
  checksum: 10/1703e6e47b575f79d801d87f24c639f4d0af71b327a822e6922d0ccb7eb3f6559abb240b8bd43bab6a477903de4cc322908e194d05132c18f52a217115e8e870
  languageName: node
  linkType: hard

"exponential-backoff@npm:^3.1.1":
  version: 3.1.2
  resolution: "exponential-backoff@npm:3.1.2"
  checksum: 10/ca2f01f1aa4dafd3f3917bd531ab5be08c6f5f4b2389d2e974f903de3cbeb50b9633374353516b6afd70905775e33aba11afab1232d3acf0aa2963b98a611c51
  languageName: node
  linkType: hard

"express@npm:^4.18.2":
  version: 4.21.2
  resolution: "express@npm:4.21.2"
  dependencies:
    accepts: "npm:~1.3.8"
    array-flatten: "npm:1.1.1"
    body-parser: "npm:1.20.3"
    content-disposition: "npm:0.5.4"
    content-type: "npm:~1.0.4"
    cookie: "npm:0.7.1"
    cookie-signature: "npm:1.0.6"
    debug: "npm:2.6.9"
    depd: "npm:2.0.0"
    encodeurl: "npm:~2.0.0"
    escape-html: "npm:~1.0.3"
    etag: "npm:~1.8.1"
    finalhandler: "npm:1.3.1"
    fresh: "npm:0.5.2"
    http-errors: "npm:2.0.0"
    merge-descriptors: "npm:1.0.3"
    methods: "npm:~1.1.2"
    on-finished: "npm:2.4.1"
    parseurl: "npm:~1.3.3"
    path-to-regexp: "npm:0.1.12"
    proxy-addr: "npm:~2.0.7"
    qs: "npm:6.13.0"
    range-parser: "npm:~1.2.1"
    safe-buffer: "npm:5.2.1"
    send: "npm:0.19.0"
    serve-static: "npm:1.16.2"
    setprototypeof: "npm:1.2.0"
    statuses: "npm:2.0.1"
    type-is: "npm:~1.6.18"
    utils-merge: "npm:1.0.1"
    vary: "npm:~1.1.2"
  checksum: 10/34571c442fc8c9f2c4b442d2faa10ea1175cf8559237fc6a278f5ce6254a8ffdbeb9a15d99f77c1a9f2926ab183e3b7ba560e3261f1ad4149799e3412ab66bd1
  languageName: node
  linkType: hard

"fast-deep-equal@npm:^3.1.1, fast-deep-equal@npm:^3.1.3":
  version: 3.1.3
  resolution: "fast-deep-equal@npm:3.1.3"
  checksum: 10/e21a9d8d84f53493b6aa15efc9cfd53dd5b714a1f23f67fb5dc8f574af80df889b3bce25dc081887c6d25457cce704e636395333abad896ccdec03abaf1f3f9d
  languageName: node
  linkType: hard

"fast-diff@npm:^1.1.2":
  version: 1.3.0
  resolution: "fast-diff@npm:1.3.0"
  checksum: 10/9e57415bc69cd6efcc720b3b8fe9fdaf42dcfc06f86f0f45378b1fa512598a8aac48aa3928c8751d58e2f01bb4ba4f07e4f3d9bc0d57586d45f1bd1e872c6cde
  languageName: node
  linkType: hard

"fast-glob@npm:^3.2.12, fast-glob@npm:^3.2.9, fast-glob@npm:^3.3.2":
  version: 3.3.3
  resolution: "fast-glob@npm:3.3.3"
  dependencies:
    "@nodelib/fs.stat": "npm:^2.0.2"
    "@nodelib/fs.walk": "npm:^1.2.3"
    glob-parent: "npm:^5.1.2"
    merge2: "npm:^1.3.0"
    micromatch: "npm:^4.0.8"
  checksum: 10/dcc6432b269762dd47381d8b8358bf964d8f4f60286ac6aa41c01ade70bda459ff2001b516690b96d5365f68a49242966112b5d5cc9cd82395fa8f9d017c90ad
  languageName: node
  linkType: hard

"fast-json-stable-stringify@npm:^2.0.0":
  version: 2.1.0
  resolution: "fast-json-stable-stringify@npm:2.1.0"
  checksum: 10/2c20055c1fa43c922428f16ca8bb29f2807de63e5c851f665f7ac9790176c01c3b40335257736b299764a8d383388dabc73c8083b8e1bc3d99f0a941444ec60e
  languageName: node
  linkType: hard

"fast-levenshtein@npm:^2.0.6":
  version: 2.0.6
  resolution: "fast-levenshtein@npm:2.0.6"
  checksum: 10/eb7e220ecf2bab5159d157350b81d01f75726a4382f5a9266f42b9150c4523b9795f7f5d9fbbbeaeac09a441b2369f05ee02db48ea938584205530fe5693cfe1
  languageName: node
  linkType: hard

"fastq@npm:^1.6.0":
  version: 1.19.1
  resolution: "fastq@npm:1.19.1"
  dependencies:
    reusify: "npm:^1.0.4"
  checksum: 10/75679dc226316341c4f2a6b618571f51eac96779906faecd8921b984e844d6ae42fabb2df69b1071327d398d5716693ea9c9c8941f64ac9e89ec2032ce59d730
  languageName: node
  linkType: hard

"fdir@npm:^6.4.4, fdir@npm:^6.5.0":
  version: 6.5.0
  resolution: "fdir@npm:6.5.0"
  peerDependencies:
    picomatch: ^3 || ^4
  peerDependenciesMeta:
    picomatch:
      optional: true
  checksum: 10/14ca1c9f0a0e8f4f2e9bf4e8551065a164a09545dae548c12a18d238b72e51e5a7b39bd8e5494b56463a0877672d0a6c1ef62c6fa0677db1b0c847773be939b1
  languageName: node
  linkType: hard

"fflate@npm:^0.8.2, fflate@npm:~0.8.2":
  version: 0.8.2
  resolution: "fflate@npm:0.8.2"
  checksum: 10/********************************************************************************************************************************
  languageName: node
  linkType: hard

"file-entry-cache@npm:^8.0.0":
  version: 8.0.0
  resolution: "file-entry-cache@npm:8.0.0"
  dependencies:
    flat-cache: "npm:^4.0.0"
  checksum: 10/********************************************************************************************************************************
  languageName: node
  linkType: hard

"fill-range@npm:^7.1.1":
  version: 7.1.1
  resolution: "fill-range@npm:7.1.1"
  dependencies:
    to-regex-range: "npm:^5.0.1"
  checksum: 10/a7095cb39e5bc32fada2aa7c7249d3f6b01bd1ce461a61b0adabacccabd9198500c6fb1f68a7c851a657e273fce2233ba869638897f3d7ed2e87a2d89b4436ea
  languageName: node
  linkType: hard

"finalhandler@npm:1.1.2":
  version: 1.1.2
  resolution: "finalhandler@npm:1.1.2"
  dependencies:
    debug: "npm:2.6.9"
    encodeurl: "npm:~1.0.2"
    escape-html: "npm:~1.0.3"
    on-finished: "npm:~2.3.0"
    parseurl: "npm:~1.3.3"
    statuses: "npm:~1.5.0"
    unpipe: "npm:~1.0.0"
  checksum: 10/351e99a889abf149eb3edb24568586469feeb3019f5eafb9b31e632a5ad886f12a5595a221508245e6a37da69ae866c9fb411eb541a844238e2c900f63ac1576
  languageName: node
  linkType: hard

"finalhandler@npm:1.3.1":
  version: 1.3.1
  resolution: "finalhandler@npm:1.3.1"
  dependencies:
    debug: "npm:2.6.9"
    encodeurl: "npm:~2.0.0"
    escape-html: "npm:~1.0.3"
    on-finished: "npm:2.4.1"
    parseurl: "npm:~1.3.3"
    statuses: "npm:2.0.1"
    unpipe: "npm:~1.0.0"
  checksum: 10/4babe72969b7373b5842bc9f75c3a641a4d0f8eb53af6b89fa714d4460ce03fb92b28de751d12ba415e96e7e02870c436d67412120555e2b382640535697305b
  languageName: node
  linkType: hard

"find-up@npm:^5.0.0":
  version: 5.0.0
  resolution: "find-up@npm:5.0.0"
  dependencies:
    locate-path: "npm:^6.0.0"
    path-exists: "npm:^4.0.0"
  checksum: 10/07955e357348f34660bde7920783204ff5a26ac2cafcaa28bace494027158a97b9f56faaf2d89a6106211a8174db650dd9f503f9c0d526b1202d5554a00b9095
  languageName: node
  linkType: hard

"flat-cache@npm:^4.0.0":
  version: 4.0.1
  resolution: "flat-cache@npm:4.0.1"
  dependencies:
    flatted: "npm:^3.2.9"
    keyv: "npm:^4.5.4"
  checksum: 10/58ce851d9045fffc7871ce2bd718bc485ad7e777bf748c054904b87c351ff1080c2c11da00788d78738bfb51b71e4d5ea12d13b98eb36e3358851ffe495b62dc
  languageName: node
  linkType: hard

"flatted@npm:^3.2.9, flatted@npm:^3.3.3":
  version: 3.3.3
  resolution: "flatted@npm:3.3.3"
  checksum: 10/********************************************************************************************************************************
  languageName: node
  linkType: hard

"follow-redirects@npm:^1.15.6":
  version: 1.15.11
  resolution: "follow-redirects@npm:1.15.11"
  peerDependenciesMeta:
    debug:
      optional: true
  checksum: 10/07372fd74b98c78cf4d417d68d41fdaa0be4dcacafffb9e67b1e3cf090bc4771515e65020651528faab238f10f9b9c0d9707d6c1574a6c0387c5de1042cde9ba
  languageName: node
  linkType: hard

"for-each@npm:^0.3.3, for-each@npm:^0.3.5":
  version: 0.3.5
  resolution: "for-each@npm:0.3.5"
  dependencies:
    is-callable: "npm:^1.2.7"
  checksum: 10/330cc2439f85c94f4609de3ee1d32c5693ae15cdd7fe3d112c4fd9efd4ce7143f2c64ef6c2c9e0cfdb0058437f33ef05b5bdae5b98fcc903fb2143fbaf0fea0f
  languageName: node
  linkType: hard

"foreground-child@npm:^3.1.0":
  version: 3.3.1
  resolution: "foreground-child@npm:3.3.1"
  dependencies:
    cross-spawn: "npm:^7.0.6"
    signal-exit: "npm:^4.0.1"
  checksum: 10/427b33f997a98073c0424e5c07169264a62cda806d8d2ded159b5b903fdfc8f0a1457e06b5fc35506497acb3f1e353f025edee796300209ac6231e80edece835
  languageName: node
  linkType: hard

"form-data@npm:^4.0.4":
  version: 4.0.4
  resolution: "form-data@npm:4.0.4"
  dependencies:
    asynckit: "npm:^0.4.0"
    combined-stream: "npm:^1.0.8"
    es-set-tostringtag: "npm:^2.1.0"
    hasown: "npm:^2.0.2"
    mime-types: "npm:^2.1.12"
  checksum: 10/a4b62e21932f48702bc468cc26fb276d186e6b07b557e3dd7cc455872bdbb82db7db066844a64ad3cf40eaf3a753c830538183570462d3649fdfd705601cbcfb
  languageName: node
  linkType: hard

"forwarded@npm:0.2.0":
  version: 0.2.0
  resolution: "forwarded@npm:0.2.0"
  checksum: 10/29ba9fd347117144e97cbb8852baae5e8b2acb7d1b591ef85695ed96f5b933b1804a7fac4a15dd09ca7ac7d0cdc104410e8102aae2dd3faa570a797ba07adb81
  languageName: node
  linkType: hard

"fresh@npm:0.5.2":
  version: 0.5.2
  resolution: "fresh@npm:0.5.2"
  checksum: 10/64c88e489b5d08e2f29664eb3c79c705ff9a8eb15d3e597198ef76546d4ade295897a44abb0abd2700e7ef784b2e3cbf1161e4fbf16f59129193fd1030d16da1
  languageName: node
  linkType: hard

"fs-minipass@npm:^3.0.0":
  version: 3.0.3
  resolution: "fs-minipass@npm:3.0.3"
  dependencies:
    minipass: "npm:^7.0.3"
  checksum: 10/af143246cf6884fe26fa281621d45cfe111d34b30535a475bfa38dafe343dadb466c047a924ffc7d6b7b18265df4110224ce3803806dbb07173bf2087b648d7f
  languageName: node
  linkType: hard

"fsevents@npm:~2.3.2, fsevents@npm:~2.3.3":
  version: 2.3.3
  resolution: "fsevents@npm:2.3.3"
  dependencies:
    node-gyp: "npm:latest"
  checksum: 10/4c1ade961ded57cdbfbb5cac5106ec17bc8bccd62e16343c569a0ceeca83b9dfef87550b4dc5cbb89642da412b20c5071f304c8c464b80415446e8e155a038c0
  conditions: os=darwin
  languageName: node
  linkType: hard

"fsevents@patch:fsevents@npm%3A~2.3.2#optional!builtin<compat/fsevents>, fsevents@patch:fsevents@npm%3A~2.3.3#optional!builtin<compat/fsevents>":
  version: 2.3.3
  resolution: "fsevents@patch:fsevents@npm%3A2.3.3#optional!builtin<compat/fsevents>::version=2.3.3&hash=df0bf1"
  dependencies:
    node-gyp: "npm:latest"
  conditions: os=darwin
  languageName: node
  linkType: hard

"function-bind@npm:^1.1.2":
  version: 1.1.2
  resolution: "function-bind@npm:1.1.2"
  checksum: 10/185e20d20f10c8d661d59aac0f3b63b31132d492e1b11fcc2a93cb2c47257ebaee7407c38513efd2b35cafdf972d9beb2ea4593c1e0f3bf8f2744836928d7454
  languageName: node
  linkType: hard

"function.prototype.name@npm:^1.1.6, function.prototype.name@npm:^1.1.8":
  version: 1.1.8
  resolution: "function.prototype.name@npm:1.1.8"
  dependencies:
    call-bind: "npm:^1.0.8"
    call-bound: "npm:^1.0.3"
    define-properties: "npm:^1.2.1"
    functions-have-names: "npm:^1.2.3"
    hasown: "npm:^2.0.2"
    is-callable: "npm:^1.2.7"
  checksum: 10/25b9e5bea936732a6f0c0c08db58cc0d609ac1ed458c6a07ead46b32e7b9bf3fe5887796c3f83d35994efbc4fdde81c08ac64135b2c399b8f2113968d44082bc
  languageName: node
  linkType: hard

"functional-red-black-tree@npm:1.0.1":
  version: 1.0.1
  resolution: "functional-red-black-tree@npm:1.0.1"
  checksum: 10/debe73e92204341d1fa5f89614e44284d3add26dee660722978d8c50829170f87d1c74768f68c251d215ae461c11db7bac13101c77f4146ff051da75466f7a12
  languageName: node
  linkType: hard

"functions-have-names@npm:^1.2.3":
  version: 1.2.3
  resolution: "functions-have-names@npm:1.2.3"
  checksum: 10/0ddfd3ed1066a55984aaecebf5419fbd9344a5c38dd120ffb0739fac4496758dcf371297440528b115e4367fc46e3abc86a2cc0ff44612181b175ae967a11a05
  languageName: node
  linkType: hard

"gensync@npm:^1.0.0-beta.2":
  version: 1.0.0-beta.2
  resolution: "gensync@npm:1.0.0-beta.2"
  checksum: 10/17d8333460204fbf1f9160d067e1e77f908a5447febb49424b8ab043026049835c9ef3974445c57dbd39161f4d2b04356d7de12b2eecaa27a7a7ea7d871cbedd
  languageName: node
  linkType: hard

"get-intrinsic@npm:^1.1.3, get-intrinsic@npm:^1.2.2, get-intrinsic@npm:^1.2.4, get-intrinsic@npm:^1.2.5, get-intrinsic@npm:^1.2.6, get-intrinsic@npm:^1.2.7, get-intrinsic@npm:^1.3.0":
  version: 1.3.0
  resolution: "get-intrinsic@npm:1.3.0"
  dependencies:
    call-bind-apply-helpers: "npm:^1.0.2"
    es-define-property: "npm:^1.0.1"
    es-errors: "npm:^1.3.0"
    es-object-atoms: "npm:^1.1.1"
    function-bind: "npm:^1.1.2"
    get-proto: "npm:^1.0.1"
    gopd: "npm:^1.2.0"
    has-symbols: "npm:^1.1.0"
    hasown: "npm:^2.0.2"
    math-intrinsics: "npm:^1.1.0"
  checksum: 10/6e9dd920ff054147b6f44cb98104330e87caafae051b6d37b13384a45ba15e71af33c3baeac7cb630a0aaa23142718dcf25b45cfdd86c184c5dcb4e56d953a10
  languageName: node
  linkType: hard

"get-proto@npm:^1.0.0, get-proto@npm:^1.0.1":
  version: 1.0.1
  resolution: "get-proto@npm:1.0.1"
  dependencies:
    dunder-proto: "npm:^1.0.1"
    es-object-atoms: "npm:^1.0.0"
  checksum: 10/4fc96afdb58ced9a67558698b91433e6b037aaa6f1493af77498d7c85b141382cf223c0e5946f334fb328ee85dfe6edd06d218eaf09556f4bc4ec6005d7f5f7b
  languageName: node
  linkType: hard

"get-symbol-description@npm:^1.1.0":
  version: 1.1.0
  resolution: "get-symbol-description@npm:1.1.0"
  dependencies:
    call-bound: "npm:^1.0.3"
    es-errors: "npm:^1.3.0"
    get-intrinsic: "npm:^1.2.6"
  checksum: 10/a353e3a9595a74720b40fb5bae3ba4a4f826e186e83814d93375182384265676f59e49998b9cdfac4a2225ce95a3d32a68f502a2c5619303987f1c183ab80494
  languageName: node
  linkType: hard

"get-tsconfig@npm:^4.10.0, get-tsconfig@npm:^4.7.5":
  version: 4.10.1
  resolution: "get-tsconfig@npm:4.10.1"
  dependencies:
    resolve-pkg-maps: "npm:^1.0.0"
  checksum: 10/04d63f47fdecaefbd1f73ec02949be4ec4db7d6d9fbc8d4e81f9a4bb1c6f876e48943712f2f9236643d3e4d61d9a7b06da08564d08b034631ebe3f5605bef237
  languageName: node
  linkType: hard

"glob-parent@npm:^5.1.2, glob-parent@npm:~5.1.2":
  version: 5.1.2
  resolution: "glob-parent@npm:5.1.2"
  dependencies:
    is-glob: "npm:^4.0.1"
  checksum: 10/32cd106ce8c0d83731966d31517adb766d02c3812de49c30cfe0675c7c0ae6630c11214c54a5ae67aca882cf738d27fd7768f21aa19118b9245950554be07247
  languageName: node
  linkType: hard

"glob-parent@npm:^6.0.2":
  version: 6.0.2
  resolution: "glob-parent@npm:6.0.2"
  dependencies:
    is-glob: "npm:^4.0.3"
  checksum: 10/c13ee97978bef4f55106b71e66428eb1512e71a7466ba49025fc2aec59a5bfb0954d5abd58fc5ee6c9b076eef4e1f6d3375c2e964b88466ca390da4419a786a8
  languageName: node
  linkType: hard

"glob@npm:^10.2.2, glob@npm:^10.4.1":
  version: 10.4.5
  resolution: "glob@npm:10.4.5"
  dependencies:
    foreground-child: "npm:^3.1.0"
    jackspeak: "npm:^3.1.2"
    minimatch: "npm:^9.0.4"
    minipass: "npm:^7.1.2"
    package-json-from-dist: "npm:^1.0.0"
    path-scurry: "npm:^1.11.1"
  bin:
    glob: dist/esm/bin.mjs
  checksum: 10/698dfe11828b7efd0514cd11e573eaed26b2dff611f0400907281ce3eab0c1e56143ef9b35adc7c77ecc71fba74717b510c7c223d34ca8a98ec81777b293d4ac
  languageName: node
  linkType: hard

"globals@npm:^14.0.0":
  version: 14.0.0
  resolution: "globals@npm:14.0.0"
  checksum: 10/03939c8af95c6df5014b137cac83aa909090c3a3985caef06ee9a5a669790877af8698ab38007e4c0186873adc14c0b13764acc754b16a754c216cc56aa5f021
  languageName: node
  linkType: hard

"globalthis@npm:^1.0.4":
  version: 1.0.4
  resolution: "globalthis@npm:1.0.4"
  dependencies:
    define-properties: "npm:^1.2.1"
    gopd: "npm:^1.0.1"
  checksum: 10/1f1fd078fb2f7296306ef9dd51019491044ccf17a59ed49d375b576ca108ff37e47f3d29aead7add40763574a992f16a5367dd1e2173b8634ef18556ab719ac4
  languageName: node
  linkType: hard

"globby@npm:^11.1.0":
  version: 11.1.0
  resolution: "globby@npm:11.1.0"
  dependencies:
    array-union: "npm:^2.1.0"
    dir-glob: "npm:^3.0.1"
    fast-glob: "npm:^3.2.9"
    ignore: "npm:^5.2.0"
    merge2: "npm:^1.4.1"
    slash: "npm:^3.0.0"
  checksum: 10/288e95e310227bbe037076ea81b7c2598ccbc3122d87abc6dab39e1eec309aa14f0e366a98cdc45237ffcfcbad3db597778c0068217dcb1950fef6249104e1b1
  languageName: node
  linkType: hard

"globrex@npm:^0.1.2":
  version: 0.1.2
  resolution: "globrex@npm:0.1.2"
  checksum: 10/81ce62ee6f800d823d6b7da7687f841676d60ee8f51f934ddd862e4057316d26665c4edc0358d4340a923ac00a514f8b67c787e28fe693aae16350f4e60d55e9
  languageName: node
  linkType: hard

"goober@npm:^2.1.16":
  version: 2.1.16
  resolution: "goober@npm:2.1.16"
  peerDependencies:
    csstype: ^3.0.10
  checksum: 10/27b57b1aef751a5b287c75cac2a3ad5f540c577fc0c89882d11d88ed6f541dbf2c1792b181a8555220da8fcf618813381fba1506d80da7831ba6e95a4f3d9ed2
  languageName: node
  linkType: hard

"gopd@npm:^1.0.1, gopd@npm:^1.2.0":
  version: 1.2.0
  resolution: "gopd@npm:1.2.0"
  checksum: 10/94e296d69f92dc1c0768fcfeecfb3855582ab59a7c75e969d5f96ce50c3d201fd86d5a2857c22565764d5bb8a816c7b1e58f133ec318cd56274da36c5e3fb1a1
  languageName: node
  linkType: hard

"graceful-fs@npm:^4.2.6":
  version: 4.2.11
  resolution: "graceful-fs@npm:4.2.11"
  checksum: 10/bf152d0ed1dc159239db1ba1f74fdbc40cb02f626770dcd5815c427ce0688c2635a06ed69af364396da4636d0408fcf7d4afdf7881724c3307e46aff30ca49e2
  languageName: node
  linkType: hard

"graphemer@npm:^1.4.0":
  version: 1.4.0
  resolution: "graphemer@npm:1.4.0"
  checksum: 10/6dd60dba97007b21e3a829fab3f771803cc1292977fe610e240ea72afd67e5690ac9eeaafc4a99710e78962e5936ab5a460787c2a1180f1cb0ccfac37d29f897
  languageName: node
  linkType: hard

"has-bigints@npm:^1.0.2":
  version: 1.1.0
  resolution: "has-bigints@npm:1.1.0"
  checksum: 10/90fb1b24d40d2472bcd1c8bd9dd479037ec240215869bdbff97b2be83acef57d28f7e96bdd003a21bed218d058b49097f4acc8821c05b1629cc5d48dd7bfcccd
  languageName: node
  linkType: hard

"has-flag@npm:^4.0.0":
  version: 4.0.0
  resolution: "has-flag@npm:4.0.0"
  checksum: 10/261a1357037ead75e338156b1f9452c016a37dcd3283a972a30d9e4a87441ba372c8b81f818cd0fbcd9c0354b4ae7e18b9e1afa1971164aef6d18c2b6095a8ad
  languageName: node
  linkType: hard

"has-property-descriptors@npm:^1.0.0, has-property-descriptors@npm:^1.0.2":
  version: 1.0.2
  resolution: "has-property-descriptors@npm:1.0.2"
  dependencies:
    es-define-property: "npm:^1.0.0"
  checksum: 10/2d8c9ab8cebb572e3362f7d06139a4592105983d4317e68f7adba320fe6ddfc8874581e0971e899e633fd5f72e262830edce36d5a0bc863dad17ad20572484b2
  languageName: node
  linkType: hard

"has-proto@npm:^1.2.0":
  version: 1.2.0
  resolution: "has-proto@npm:1.2.0"
  dependencies:
    dunder-proto: "npm:^1.0.0"
  checksum: 10/7eaed07728eaa28b77fadccabce53f30de467ff186a766872669a833ac2e87d8922b76a22cc58339d7e0277aefe98d6d00762113b27a97cdf65adcf958970935
  languageName: node
  linkType: hard

"has-symbols@npm:^1.0.3, has-symbols@npm:^1.1.0":
  version: 1.1.0
  resolution: "has-symbols@npm:1.1.0"
  checksum: 10/959385c98696ebbca51e7534e0dc723ada325efa3475350951363cce216d27373e0259b63edb599f72eb94d6cde8577b4b2375f080b303947e560f85692834fa
  languageName: node
  linkType: hard

"has-tostringtag@npm:^1.0.2":
  version: 1.0.2
  resolution: "has-tostringtag@npm:1.0.2"
  dependencies:
    has-symbols: "npm:^1.0.3"
  checksum: 10/c74c5f5ceee3c8a5b8bc37719840dc3749f5b0306d818974141dda2471a1a2ca6c8e46b9d6ac222c5345df7a901c9b6f350b1e6d62763fec877e26609a401bfe
  languageName: node
  linkType: hard

"hasown@npm:^2.0.2":
  version: 2.0.2
  resolution: "hasown@npm:2.0.2"
  dependencies:
    function-bind: "npm:^1.1.2"
  checksum: 10/7898a9c1788b2862cf0f9c345a6bec77ba4a0c0983c7f19d610c382343d4f98fa260686b225dfb1f88393a66679d2ec58ee310c1d6868c081eda7918f32cc70a
  languageName: node
  linkType: hard

"he@npm:1.2.0":
  version: 1.2.0
  resolution: "he@npm:1.2.0"
  bin:
    he: bin/he
  checksum: 10/d09b2243da4e23f53336e8de3093e5c43d2c39f8d0d18817abfa32ce3e9355391b2edb4bb5edc376aea5d4b0b59d6a0482aab4c52bc02ef95751e4b818e847f1
  languageName: node
  linkType: hard

"html-encoding-sniffer@npm:^4.0.0":
  version: 4.0.0
  resolution: "html-encoding-sniffer@npm:4.0.0"
  dependencies:
    whatwg-encoding: "npm:^3.1.1"
  checksum: 10/e86efd493293a5671b8239bd099d42128433bb3c7b0fdc7819282ef8e118a21f5dead0ad6f358e024a4e5c84f17ebb7a9b36075220fac0a6222b207248bede6f
  languageName: node
  linkType: hard

"html-escaper@npm:^2.0.0":
  version: 2.0.2
  resolution: "html-escaper@npm:2.0.2"
  checksum: 10/034d74029dcca544a34fb6135e98d427acd73019796ffc17383eaa3ec2fe1c0471dcbbc8f8ed39e46e86d43ccd753a160631615e4048285e313569609b66d5b7
  languageName: node
  linkType: hard

"http-cache-semantics@npm:^4.1.1":
  version: 4.2.0
  resolution: "http-cache-semantics@npm:4.2.0"
  checksum: 10/4efd2dfcfeea9d5e88c84af450b9980be8a43c2c8179508b1c57c7b4421c855f3e8efe92fa53e0b3f4a43c85824ada930eabbc306d1b3beab750b6dcc5187693
  languageName: node
  linkType: hard

"http-errors@npm:2.0.0":
  version: 2.0.0
  resolution: "http-errors@npm:2.0.0"
  dependencies:
    depd: "npm:2.0.0"
    inherits: "npm:2.0.4"
    setprototypeof: "npm:1.2.0"
    statuses: "npm:2.0.1"
    toidentifier: "npm:1.0.1"
  checksum: 10/0e7f76ee8ff8a33e58a3281a469815b893c41357378f408be8f6d4aa7d1efafb0da064625518e7078381b6a92325949b119dc38fcb30bdbc4e3a35f78c44c439
  languageName: node
  linkType: hard

"http-proxy-agent@npm:^7.0.0, http-proxy-agent@npm:^7.0.2":
  version: 7.0.2
  resolution: "http-proxy-agent@npm:7.0.2"
  dependencies:
    agent-base: "npm:^7.1.0"
    debug: "npm:^4.3.4"
  checksum: 10/d062acfa0cb82beeb558f1043c6ba770ea892b5fb7b28654dbc70ea2aeea55226dd34c02a294f6c1ca179a5aa483c4ea641846821b182edbd9cc5d89b54c6848
  languageName: node
  linkType: hard

"https-proxy-agent@npm:^7.0.1, https-proxy-agent@npm:^7.0.6":
  version: 7.0.6
  resolution: "https-proxy-agent@npm:7.0.6"
  dependencies:
    agent-base: "npm:^7.1.2"
    debug: "npm:4"
  checksum: 10/784b628cbd55b25542a9d85033bdfd03d4eda630fb8b3c9477959367f3be95dc476ed2ecbb9836c359c7c698027fc7b45723a302324433590f45d6c1706e8c13
  languageName: node
  linkType: hard

"iconv-lite@npm:0.4.24":
  version: 0.4.24
  resolution: "iconv-lite@npm:0.4.24"
  dependencies:
    safer-buffer: "npm:>= 2.1.2 < 3"
  checksum: 10/6d3a2dac6e5d1fb126d25645c25c3a1209f70cceecc68b8ef51ae0da3cdc078c151fade7524a30b12a3094926336831fca09c666ef55b37e2c69638b5d6bd2e3
  languageName: node
  linkType: hard

"iconv-lite@npm:0.6.3, iconv-lite@npm:^0.6.2":
  version: 0.6.3
  resolution: "iconv-lite@npm:0.6.3"
  dependencies:
    safer-buffer: "npm:>= 2.1.2 < 3.0.0"
  checksum: 10/24e3292dd3dadaa81d065c6f8c41b274a47098150d444b96e5f53b4638a9a71482921ea6a91a1f59bb71d9796de25e04afd05919fa64c360347ba65d3766f10f
  languageName: node
  linkType: hard

"ignore@npm:^5.2.0":
  version: 5.3.2
  resolution: "ignore@npm:5.3.2"
  checksum: 10/cceb6a457000f8f6a50e1196429750d782afce5680dd878aa4221bd79972d68b3a55b4b1458fc682be978f4d3c6a249046aa0880637367216444ab7b014cfc98
  languageName: node
  linkType: hard

"ignore@npm:^7.0.0":
  version: 7.0.5
  resolution: "ignore@npm:7.0.5"
  checksum: 10/f134b96a4de0af419196f52c529d5c6120c4456ff8a6b5a14ceaaa399f883e15d58d2ce651c9b69b9388491d4669dda47285d307e827de9304a53a1824801bc6
  languageName: node
  linkType: hard

"import-fresh@npm:^3.2.1, import-fresh@npm:^3.3.0":
  version: 3.3.1
  resolution: "import-fresh@npm:3.3.1"
  dependencies:
    parent-module: "npm:^1.0.0"
    resolve-from: "npm:^4.0.0"
  checksum: 10/a06b19461b4879cc654d46f8a6244eb55eb053437afd4cbb6613cad6be203811849ed3e4ea038783092879487299fda24af932b86bdfff67c9055ba3612b8c87
  languageName: node
  linkType: hard

"imurmurhash@npm:^0.1.4":
  version: 0.1.4
  resolution: "imurmurhash@npm:0.1.4"
  checksum: 10/2d30b157a91fe1c1d7c6f653cbf263f039be6c5bfa959245a16d4ee191fc0f2af86c08545b6e6beeb041c56b574d2d5b9f95343d378ab49c0f37394d541e7fc8
  languageName: node
  linkType: hard

"indent-string@npm:^4.0.0":
  version: 4.0.0
  resolution: "indent-string@npm:4.0.0"
  checksum: 10/cd3f5cbc9ca2d624c6a1f53f12e6b341659aba0e2d3254ae2b4464aaea8b4294cdb09616abbc59458f980531f2429784ed6a420d48d245bcad0811980c9efae9
  languageName: node
  linkType: hard

"inherits@npm:2.0.4":
  version: 2.0.4
  resolution: "inherits@npm:2.0.4"
  checksum: 10/cd45e923bee15186c07fa4c89db0aace24824c482fb887b528304694b2aa6ff8a898da8657046a5dcf3e46cd6db6c61629551f9215f208d7c3f157cf9b290521
  languageName: node
  linkType: hard

"internal-slot@npm:^1.1.0":
  version: 1.1.0
  resolution: "internal-slot@npm:1.1.0"
  dependencies:
    es-errors: "npm:^1.3.0"
    hasown: "npm:^2.0.2"
    side-channel: "npm:^1.1.0"
  checksum: 10/1d5219273a3dab61b165eddf358815eefc463207db33c20fcfca54717da02e3f492003757721f972fd0bf21e4b426cab389c5427b99ceea4b8b670dc88ee6d4a
  languageName: node
  linkType: hard

"ip-address@npm:^10.0.1":
  version: 10.0.1
  resolution: "ip-address@npm:10.0.1"
  checksum: 10/09731acda32cd8e14c46830c137e7e5940f47b36d63ffb87c737331270287d631cf25aa95570907a67d3f919fdb25f4470c404eda21e62f22e0a55927f4dd0fb
  languageName: node
  linkType: hard

"ipaddr.js@npm:1.9.1":
  version: 1.9.1
  resolution: "ipaddr.js@npm:1.9.1"
  checksum: 10/864d0cced0c0832700e9621913a6429ccdc67f37c1bd78fb8c6789fff35c9d167cb329134acad2290497a53336813ab4798d2794fd675d5eb33b5fdf0982b9ca
  languageName: node
  linkType: hard

"is-arguments@npm:^1.1.1":
  version: 1.2.0
  resolution: "is-arguments@npm:1.2.0"
  dependencies:
    call-bound: "npm:^1.0.2"
    has-tostringtag: "npm:^1.0.2"
  checksum: 10/471a8ef631b8ee8829c43a8ab05c081700c0e25180c73d19f3bf819c1a8448c426a9e8e601f278973eca68966384b16ceb78b8c63af795b099cd199ea5afc457
  languageName: node
  linkType: hard

"is-array-buffer@npm:^3.0.2, is-array-buffer@npm:^3.0.4, is-array-buffer@npm:^3.0.5":
  version: 3.0.5
  resolution: "is-array-buffer@npm:3.0.5"
  dependencies:
    call-bind: "npm:^1.0.8"
    call-bound: "npm:^1.0.3"
    get-intrinsic: "npm:^1.2.6"
  checksum: 10/ef1095c55b963cd0dcf6f88a113e44a0aeca91e30d767c475e7d746d28d1195b10c5076b94491a7a0cd85020ca6a4923070021d74651d093dc909e9932cf689b
  languageName: node
  linkType: hard

"is-arrayish@npm:^0.2.1":
  version: 0.2.1
  resolution: "is-arrayish@npm:0.2.1"
  checksum: 10/73ced84fa35e59e2c57da2d01e12cd01479f381d7f122ce41dcbb713f09dbfc651315832cd2bf8accba7681a69e4d6f1e03941d94dd10040d415086360e7005e
  languageName: node
  linkType: hard

"is-async-function@npm:^2.0.0":
  version: 2.1.1
  resolution: "is-async-function@npm:2.1.1"
  dependencies:
    async-function: "npm:^1.0.0"
    call-bound: "npm:^1.0.3"
    get-proto: "npm:^1.0.1"
    has-tostringtag: "npm:^1.0.2"
    safe-regex-test: "npm:^1.1.0"
  checksum: 10/7c2ac7efdf671e03265e74a043bcb1c0a32e226bc2a42dfc5ec8644667df668bbe14b91c08e6c1414f392f8cf86cd1d489b3af97756e2c7a49dd1ba63fd40ca6
  languageName: node
  linkType: hard

"is-bigint@npm:^1.1.0":
  version: 1.1.0
  resolution: "is-bigint@npm:1.1.0"
  dependencies:
    has-bigints: "npm:^1.0.2"
  checksum: 10/10cf327310d712fe227cfaa32d8b11814c214392b6ac18c827f157e1e85363cf9c8e2a22df526689bd5d25e53b58cc110894787afb54e138e7c504174dba15fd
  languageName: node
  linkType: hard

"is-binary-path@npm:~2.1.0":
  version: 2.1.0
  resolution: "is-binary-path@npm:2.1.0"
  dependencies:
    binary-extensions: "npm:^2.0.0"
  checksum: 10/078e51b4f956c2c5fd2b26bb2672c3ccf7e1faff38e0ebdba45612265f4e3d9fc3127a1fa8370bbf09eab61339203c3d3b7af5662cbf8be4030f8fac37745b0e
  languageName: node
  linkType: hard

"is-boolean-object@npm:^1.2.1":
  version: 1.2.2
  resolution: "is-boolean-object@npm:1.2.2"
  dependencies:
    call-bound: "npm:^1.0.3"
    has-tostringtag: "npm:^1.0.2"
  checksum: 10/051fa95fdb99d7fbf653165a7e6b2cba5d2eb62f7ffa81e793a790f3fb5366c91c1b7b6af6820aa2937dd86c73aa3ca9d9ca98f500988457b1c59692c52ba911
  languageName: node
  linkType: hard

"is-bun-module@npm:^2.0.0":
  version: 2.0.0
  resolution: "is-bun-module@npm:2.0.0"
  dependencies:
    semver: "npm:^7.7.1"
  checksum: 10/cded5a1a58368b847872d08617975d620ad94426d76a932f3e08d55b4574d199e0a62a4fb024fa2dc444200b71719eb0bffc5d3d1e1cc82e29b293bb8d66a990
  languageName: node
  linkType: hard

"is-callable@npm:^1.2.7":
  version: 1.2.7
  resolution: "is-callable@npm:1.2.7"
  checksum: 10/48a9297fb92c99e9df48706241a189da362bff3003354aea4048bd5f7b2eb0d823cd16d0a383cece3d76166ba16d85d9659165ac6fcce1ac12e6c649d66dbdb9
  languageName: node
  linkType: hard

"is-core-module@npm:^2.13.0, is-core-module@npm:^2.15.1, is-core-module@npm:^2.16.0":
  version: 2.16.1
  resolution: "is-core-module@npm:2.16.1"
  dependencies:
    hasown: "npm:^2.0.2"
  checksum: 10/452b2c2fb7f889cbbf7e54609ef92cf6c24637c568acc7e63d166812a0fb365ae8a504c333a29add8bdb1686704068caa7f4e4b639b650dde4f00a038b8941fb
  languageName: node
  linkType: hard

"is-data-view@npm:^1.0.1, is-data-view@npm:^1.0.2":
  version: 1.0.2
  resolution: "is-data-view@npm:1.0.2"
  dependencies:
    call-bound: "npm:^1.0.2"
    get-intrinsic: "npm:^1.2.6"
    is-typed-array: "npm:^1.1.13"
  checksum: 10/357e9a48fa38f369fd6c4c3b632a3ab2b8adca14997db2e4b3fe94c4cd0a709af48e0fb61b02c64a90c0dd542fd489d49c2d03157b05ae6c07f5e4dec9e730a8
  languageName: node
  linkType: hard

"is-date-object@npm:^1.0.5, is-date-object@npm:^1.1.0":
  version: 1.1.0
  resolution: "is-date-object@npm:1.1.0"
  dependencies:
    call-bound: "npm:^1.0.2"
    has-tostringtag: "npm:^1.0.2"
  checksum: 10/3a811b2c3176fb31abee1d23d3dc78b6c65fd9c07d591fcb67553cab9e7f272728c3dd077d2d738b53f9a2103255b0a6e8dfc9568a7805c56a78b2563e8d1dec
  languageName: node
  linkType: hard

"is-docker@npm:^2.0.0, is-docker@npm:^2.1.1":
  version: 2.2.1
  resolution: "is-docker@npm:2.2.1"
  bin:
    is-docker: cli.js
  checksum: 10/3fef7ddbf0be25958e8991ad941901bf5922ab2753c46980b60b05c1bf9c9c2402d35e6dc32e4380b980ef5e1970a5d9d5e5aa2e02d77727c3b6b5e918474c56
  languageName: node
  linkType: hard

"is-extglob@npm:^2.1.1":
  version: 2.1.1
  resolution: "is-extglob@npm:2.1.1"
  checksum: 10/df033653d06d0eb567461e58a7a8c9f940bd8c22274b94bf7671ab36df5719791aae15eef6d83bbb5e23283967f2f984b8914559d4449efda578c775c4be6f85
  languageName: node
  linkType: hard

"is-finalizationregistry@npm:^1.1.0":
  version: 1.1.1
  resolution: "is-finalizationregistry@npm:1.1.1"
  dependencies:
    call-bound: "npm:^1.0.3"
  checksum: 10/0bfb145e9a1ba852ddde423b0926d2169ae5fe9e37882cde9e8f69031281a986308df4d982283e152396e88b86562ed2256cbaa5e6390fb840a4c25ab54b8a80
  languageName: node
  linkType: hard

"is-fullwidth-code-point@npm:^3.0.0":
  version: 3.0.0
  resolution: "is-fullwidth-code-point@npm:3.0.0"
  checksum: 10/44a30c29457c7fb8f00297bce733f0a64cd22eca270f83e58c105e0d015e45c019491a4ab2faef91ab51d4738c670daff901c799f6a700e27f7314029e99e348
  languageName: node
  linkType: hard

"is-generator-function@npm:^1.0.10":
  version: 1.1.0
  resolution: "is-generator-function@npm:1.1.0"
  dependencies:
    call-bound: "npm:^1.0.3"
    get-proto: "npm:^1.0.0"
    has-tostringtag: "npm:^1.0.2"
    safe-regex-test: "npm:^1.1.0"
  checksum: 10/5906ff51a856a5fbc6b90a90fce32040b0a6870da905f98818f1350f9acadfc9884f7c3dec833fce04b83dd883937b86a190b6593ede82e8b1af8b6c4ecf7cbd
  languageName: node
  linkType: hard

"is-glob@npm:^4.0.0, is-glob@npm:^4.0.1, is-glob@npm:^4.0.3, is-glob@npm:~4.0.1":
  version: 4.0.3
  resolution: "is-glob@npm:4.0.3"
  dependencies:
    is-extglob: "npm:^2.1.1"
  checksum: 10/3ed74f2b0cdf4f401f38edb0442ddfde3092d79d7d35c9919c86641efdbcbb32e45aa3c0f70ce5eecc946896cd5a0f26e4188b9f2b881876f7cb6c505b82da11
  languageName: node
  linkType: hard

"is-map@npm:^2.0.2, is-map@npm:^2.0.3":
  version: 2.0.3
  resolution: "is-map@npm:2.0.3"
  checksum: 10/8de7b41715b08bcb0e5edb0fb9384b80d2d5bcd10e142188f33247d19ff078abaf8e9b6f858e2302d8d05376a26a55cd23a3c9f8ab93292b02fcd2cc9e4e92bb
  languageName: node
  linkType: hard

"is-negative-zero@npm:^2.0.3":
  version: 2.0.3
  resolution: "is-negative-zero@npm:2.0.3"
  checksum: 10/8fe5cffd8d4fb2ec7b49d657e1691889778d037494c6f40f4d1a524cadd658b4b53ad7b6b73a59bcb4b143ae9a3d15829af864b2c0f9d65ac1e678c4c80f17e5
  languageName: node
  linkType: hard

"is-number-object@npm:^1.1.1":
  version: 1.1.1
  resolution: "is-number-object@npm:1.1.1"
  dependencies:
    call-bound: "npm:^1.0.3"
    has-tostringtag: "npm:^1.0.2"
  checksum: 10/a5922fb8779ab1ea3b8a9c144522b3d0bea5d9f8f23f7a72470e61e1e4df47714e28e0154ac011998b709cce260c3c9447ad3cd24a96c2f2a0abfdb2cbdc76c8
  languageName: node
  linkType: hard

"is-number@npm:^7.0.0":
  version: 7.0.0
  resolution: "is-number@npm:7.0.0"
  checksum: 10/6a6c3383f68afa1e05b286af866017c78f1226d43ac8cb064e115ff9ed85eb33f5c4f7216c96a71e4dfea289ef52c5da3aef5bbfade8ffe47a0465d70c0c8e86
  languageName: node
  linkType: hard

"is-potential-custom-element-name@npm:^1.0.1":
  version: 1.0.1
  resolution: "is-potential-custom-element-name@npm:1.0.1"
  checksum: 10/ced7bbbb6433a5b684af581872afe0e1767e2d1146b2207ca0068a648fb5cab9d898495d1ac0583524faaf24ca98176a7d9876363097c2d14fee6dd324f3a1ab
  languageName: node
  linkType: hard

"is-regex@npm:^1.1.4, is-regex@npm:^1.2.1":
  version: 1.2.1
  resolution: "is-regex@npm:1.2.1"
  dependencies:
    call-bound: "npm:^1.0.2"
    gopd: "npm:^1.2.0"
    has-tostringtag: "npm:^1.0.2"
    hasown: "npm:^2.0.2"
  checksum: 10/c42b7efc5868a5c9a4d8e6d3e9816e8815c611b09535c00fead18a1138455c5cb5e1887f0023a467ad3f9c419d62ba4dc3d9ba8bafe55053914d6d6454a945d2
  languageName: node
  linkType: hard

"is-set@npm:^2.0.2, is-set@npm:^2.0.3":
  version: 2.0.3
  resolution: "is-set@npm:2.0.3"
  checksum: 10/5685df33f0a4a6098a98c72d94d67cad81b2bc72f1fb2091f3d9283c4a1c582123cd709145b02a9745f0ce6b41e3e43f1c944496d1d74d4ea43358be61308669
  languageName: node
  linkType: hard

"is-shared-array-buffer@npm:^1.0.2, is-shared-array-buffer@npm:^1.0.4":
  version: 1.0.4
  resolution: "is-shared-array-buffer@npm:1.0.4"
  dependencies:
    call-bound: "npm:^1.0.3"
  checksum: 10/0380d7c60cc692856871526ffcd38a8133818a2ee42d47bb8008248a0cd2121d8c8b5f66b6da3cac24bc5784553cacb6faaf678f66bc88c6615b42af2825230e
  languageName: node
  linkType: hard

"is-string@npm:^1.0.7, is-string@npm:^1.1.1":
  version: 1.1.1
  resolution: "is-string@npm:1.1.1"
  dependencies:
    call-bound: "npm:^1.0.3"
    has-tostringtag: "npm:^1.0.2"
  checksum: 10/5277cb9e225a7cc8a368a72623b44a99f2cfa139659c6b203553540681ad4276bfc078420767aad0e73eef5f0bd07d4abf39a35d37ec216917879d11cebc1f8b
  languageName: node
  linkType: hard

"is-symbol@npm:^1.0.4, is-symbol@npm:^1.1.1":
  version: 1.1.1
  resolution: "is-symbol@npm:1.1.1"
  dependencies:
    call-bound: "npm:^1.0.2"
    has-symbols: "npm:^1.1.0"
    safe-regex-test: "npm:^1.1.0"
  checksum: 10/db495c0d8cd0a7a66b4f4ef7fccee3ab5bd954cb63396e8ac4d32efe0e9b12fdfceb851d6c501216a71f4f21e5ff20fc2ee845a3d52d455e021c466ac5eb2db2
  languageName: node
  linkType: hard

"is-typed-array@npm:^1.1.13, is-typed-array@npm:^1.1.14, is-typed-array@npm:^1.1.15":
  version: 1.1.15
  resolution: "is-typed-array@npm:1.1.15"
  dependencies:
    which-typed-array: "npm:^1.1.16"
  checksum: 10/e8cf60b9ea85667097a6ad68c209c9722cfe8c8edf04d6218366469e51944c5cc25bae45ffb845c23f811d262e4314d3b0168748eb16711aa34d12724cdf0735
  languageName: node
  linkType: hard

"is-weakmap@npm:^2.0.2":
  version: 2.0.2
  resolution: "is-weakmap@npm:2.0.2"
  checksum: 10/a7b7e23206c542dcf2fa0abc483142731788771527e90e7e24f658c0833a0d91948a4f7b30d78f7a65255a48512e41a0288b778ba7fc396137515c12e201fd11
  languageName: node
  linkType: hard

"is-weakref@npm:^1.0.2, is-weakref@npm:^1.1.1":
  version: 1.1.1
  resolution: "is-weakref@npm:1.1.1"
  dependencies:
    call-bound: "npm:^1.0.3"
  checksum: 10/543506fd8259038b371bb083aac25b16cb4fd8b12fc58053aa3d45ac28dfd001cd5c6dffbba7aeea4213c74732d46b6cb2cfb5b412eed11f2db524f3f97d09a0
  languageName: node
  linkType: hard

"is-weakset@npm:^2.0.3":
  version: 2.0.4
  resolution: "is-weakset@npm:2.0.4"
  dependencies:
    call-bound: "npm:^1.0.3"
    get-intrinsic: "npm:^1.2.6"
  checksum: 10/1d5e1d0179beeed3661125a6faa2e59bfb48afda06fc70db807f178aa0ebebc3758fb6358d76b3d528090d5ef85148c345dcfbf90839592fe293e3e5e82f2134
  languageName: node
  linkType: hard

"is-wsl@npm:^2.2.0":
  version: 2.2.0
  resolution: "is-wsl@npm:2.2.0"
  dependencies:
    is-docker: "npm:^2.0.0"
  checksum: 10/20849846ae414997d290b75e16868e5261e86ff5047f104027026fd61d8b5a9b0b3ade16239f35e1a067b3c7cc02f70183cb661010ed16f4b6c7c93dad1b19d8
  languageName: node
  linkType: hard

"isarray@npm:^2.0.5":
  version: 2.0.5
  resolution: "isarray@npm:2.0.5"
  checksum: 10/1d8bc7911e13bb9f105b1b3e0b396c787a9e63046af0b8fe0ab1414488ab06b2b099b87a2d8a9e31d21c9a6fad773c7fc8b257c4880f2d957274479d28ca3414
  languageName: node
  linkType: hard

"isbot@npm:^5.1.22":
  version: 5.1.30
  resolution: "isbot@npm:5.1.30"
  checksum: 10/989a61e66398c1249e3a38ea4e6655ba39cc060ae2744093cd0a9cffa8fd1f49fc79ae7eb47e5e820dc163e17c1273d5bf5ee0682e9083f8839f3c2196397e3e
  languageName: node
  linkType: hard

"isexe@npm:^2.0.0":
  version: 2.0.0
  resolution: "isexe@npm:2.0.0"
  checksum: 10/7c9f715c03aff08f35e98b1fadae1b9267b38f0615d501824f9743f3aab99ef10e303ce7db3f186763a0b70a19de5791ebfc854ff884d5a8c4d92211f642ec92
  languageName: node
  linkType: hard

"isexe@npm:^3.1.1":
  version: 3.1.1
  resolution: "isexe@npm:3.1.1"
  checksum: 10/7fe1931ee4e88eb5aa524cd3ceb8c882537bc3a81b02e438b240e47012eef49c86904d0f0e593ea7c3a9996d18d0f1f3be8d3eaa92333977b0c3a9d353d5563e
  languageName: node
  linkType: hard

"isomorphic-fetch@npm:3.0.0":
  version: 3.0.0
  resolution: "isomorphic-fetch@npm:3.0.0"
  dependencies:
    node-fetch: "npm:^2.6.1"
    whatwg-fetch: "npm:^3.4.1"
  checksum: 10/568fe0307528c63405c44dd3873b7b6c96c0d19ff795cb15846e728b6823bdbc68cc8c97ac23324509661316f12f551e43dac2929bc7030b8bc4d6aa1158b857
  languageName: node
  linkType: hard

"istanbul-lib-coverage@npm:^3.0.0, istanbul-lib-coverage@npm:^3.2.0, istanbul-lib-coverage@npm:^3.2.2":
  version: 3.2.2
  resolution: "istanbul-lib-coverage@npm:3.2.2"
  checksum: 10/40bbdd1e937dfd8c830fa286d0f665e81b7a78bdabcd4565f6d5667c99828bda3db7fb7ac6b96a3e2e8a2461ddbc5452d9f8bc7d00cb00075fa6a3e99f5b6a81
  languageName: node
  linkType: hard

"istanbul-lib-instrument@npm:^6.0.3":
  version: 6.0.3
  resolution: "istanbul-lib-instrument@npm:6.0.3"
  dependencies:
    "@babel/core": "npm:^7.23.9"
    "@babel/parser": "npm:^7.23.9"
    "@istanbuljs/schema": "npm:^0.1.3"
    istanbul-lib-coverage: "npm:^3.2.0"
    semver: "npm:^7.5.4"
  checksum: 10/aa5271c0008dfa71b6ecc9ba1e801bf77b49dc05524e8c30d58aaf5b9505e0cd12f25f93165464d4266a518c5c75284ecb598fbd89fec081ae77d2c9d3327695
  languageName: node
  linkType: hard

"istanbul-lib-report@npm:^3.0.0, istanbul-lib-report@npm:^3.0.1":
  version: 3.0.1
  resolution: "istanbul-lib-report@npm:3.0.1"
  dependencies:
    istanbul-lib-coverage: "npm:^3.0.0"
    make-dir: "npm:^4.0.0"
    supports-color: "npm:^7.1.0"
  checksum: 10/86a83421ca1cf2109a9f6d193c06c31ef04a45e72a74579b11060b1e7bb9b6337a4e6f04abfb8857e2d569c271273c65e855ee429376a0d7c91ad91db42accd1
  languageName: node
  linkType: hard

"istanbul-lib-source-maps@npm:^5.0.6":
  version: 5.0.6
  resolution: "istanbul-lib-source-maps@npm:5.0.6"
  dependencies:
    "@jridgewell/trace-mapping": "npm:^0.3.23"
    debug: "npm:^4.1.1"
    istanbul-lib-coverage: "npm:^3.0.0"
  checksum: 10/569dd0a392ee3464b1fe1accbaef5cc26de3479eacb5b91d8c67ebb7b425d39fd02247d85649c3a0e9c29b600809fa60b5af5a281a75a89c01f385b1e24823a2
  languageName: node
  linkType: hard

"istanbul-reports@npm:^3.1.7":
  version: 3.2.0
  resolution: "istanbul-reports@npm:3.2.0"
  dependencies:
    html-escaper: "npm:^2.0.0"
    istanbul-lib-report: "npm:^3.0.0"
  checksum: 10/6773a1d5c7d47eeec75b317144fe2a3b1da84a44b6282bebdc856e09667865e58c9b025b75b3d87f5bc62939126cbba4c871ee84254537d934ba5da5d4c4ec4e
  languageName: node
  linkType: hard

"iterator.prototype@npm:^1.1.4":
  version: 1.1.5
  resolution: "iterator.prototype@npm:1.1.5"
  dependencies:
    define-data-property: "npm:^1.1.4"
    es-object-atoms: "npm:^1.0.0"
    get-intrinsic: "npm:^1.2.6"
    get-proto: "npm:^1.0.0"
    has-symbols: "npm:^1.1.0"
    set-function-name: "npm:^2.0.2"
  checksum: 10/352bcf333f42189e65cc8cb2dcb94a5c47cf0a9110ce12aba788d405a980b5f5f3a06c79bf915377e1d480647169babd842ded0d898bed181bf6686e8e6823f6
  languageName: node
  linkType: hard

"jackspeak@npm:^3.1.2":
  version: 3.4.3
  resolution: "jackspeak@npm:3.4.3"
  dependencies:
    "@isaacs/cliui": "npm:^8.0.2"
    "@pkgjs/parseargs": "npm:^0.11.0"
  dependenciesMeta:
    "@pkgjs/parseargs":
      optional: true
  checksum: 10/96f8786eaab98e4bf5b2a5d6d9588ea46c4d06bbc4f2eb861fdd7b6b182b16f71d8a70e79820f335d52653b16d4843b29dd9cdcf38ae80406756db9199497cf3
  languageName: node
  linkType: hard

"js-tokens@npm:^3.0.0 || ^4.0.0, js-tokens@npm:^4.0.0":
  version: 4.0.0
  resolution: "js-tokens@npm:4.0.0"
  checksum: 10/af37d0d913fb56aec6dc0074c163cc71cd23c0b8aad5c2350747b6721d37ba118af35abdd8b33c47ec2800de07dedb16a527ca9c530ee004093e04958bd0cbf2
  languageName: node
  linkType: hard

"js-tokens@npm:^9.0.1":
  version: 9.0.1
  resolution: "js-tokens@npm:9.0.1"
  checksum: 10/3288ba73bb2023adf59501979fb4890feb6669cc167b13771b226814fde96a1583de3989249880e3f4d674040d1815685db9a9880db9153307480d39dc760365
  languageName: node
  linkType: hard

"js-yaml@npm:^4.1.0":
  version: 4.1.0
  resolution: "js-yaml@npm:4.1.0"
  dependencies:
    argparse: "npm:^2.0.1"
  bin:
    js-yaml: bin/js-yaml.js
  checksum: 10/c138a34a3fd0d08ebaf71273ad4465569a483b8a639e0b118ff65698d257c2791d3199e3f303631f2cb98213fa7b5f5d6a4621fd0fff819421b990d30d967140
  languageName: node
  linkType: hard

"jsdom@npm:26.1.0":
  version: 26.1.0
  resolution: "jsdom@npm:26.1.0"
  dependencies:
    cssstyle: "npm:^4.2.1"
    data-urls: "npm:^5.0.0"
    decimal.js: "npm:^10.5.0"
    html-encoding-sniffer: "npm:^4.0.0"
    http-proxy-agent: "npm:^7.0.2"
    https-proxy-agent: "npm:^7.0.6"
    is-potential-custom-element-name: "npm:^1.0.1"
    nwsapi: "npm:^2.2.16"
    parse5: "npm:^7.2.1"
    rrweb-cssom: "npm:^0.8.0"
    saxes: "npm:^6.0.0"
    symbol-tree: "npm:^3.2.4"
    tough-cookie: "npm:^5.1.1"
    w3c-xmlserializer: "npm:^5.0.0"
    webidl-conversions: "npm:^7.0.0"
    whatwg-encoding: "npm:^3.1.1"
    whatwg-mimetype: "npm:^4.0.0"
    whatwg-url: "npm:^14.1.1"
    ws: "npm:^8.18.0"
    xml-name-validator: "npm:^5.0.0"
  peerDependencies:
    canvas: ^3.0.0
  peerDependenciesMeta:
    canvas:
      optional: true
  checksum: 10/39d78c4889cac20826393400dce1faed1666e9244fe0c8342a8f08c315375878e6be7fcfe339a33d6ff1a083bfe9e71b16d56ecf4d9a87db2da8c795925ea8c1
  languageName: node
  linkType: hard

"jsesc@npm:^3.0.2":
  version: 3.1.0
  resolution: "jsesc@npm:3.1.0"
  bin:
    jsesc: bin/jsesc
  checksum: 10/20bd37a142eca5d1794f354db8f1c9aeb54d85e1f5c247b371de05d23a9751ecd7bd3a9c4fc5298ea6fa09a100dafb4190fa5c98c6610b75952c3487f3ce7967
  languageName: node
  linkType: hard

"jsesc@npm:~3.0.2":
  version: 3.0.2
  resolution: "jsesc@npm:3.0.2"
  bin:
    jsesc: bin/jsesc
  checksum: 10/8e5a7de6b70a8bd71f9cb0b5a7ade6a73ae6ab55e697c74cc997cede97417a3a65ed86c36f7dd6125fe49766e8386c845023d9e213916ca92c9dfdd56e2babf3
  languageName: node
  linkType: hard

"json-buffer@npm:3.0.1":
  version: 3.0.1
  resolution: "json-buffer@npm:3.0.1"
  checksum: 10/82876154521b7b68ba71c4f969b91572d1beabadd87bd3a6b236f85fbc7dc4695089191ed60bb59f9340993c51b33d479f45b6ba9f3548beb519705281c32c3c
  languageName: node
  linkType: hard

"json-parse-even-better-errors@npm:^2.3.0":
  version: 2.3.1
  resolution: "json-parse-even-better-errors@npm:2.3.1"
  checksum: 10/5f3a99009ed5f2a5a67d06e2f298cc97bc86d462034173308156f15b43a6e850be8511dc204b9b94566305da2947f7d90289657237d210351a39059ff9d666cf
  languageName: node
  linkType: hard

"json-schema-traverse@npm:^0.4.1":
  version: 0.4.1
  resolution: "json-schema-traverse@npm:0.4.1"
  checksum: 10/7486074d3ba247769fda17d5181b345c9fb7d12e0da98b22d1d71a5db9698d8b4bd900a3ec1a4ffdd60846fc2556274a5c894d0c48795f14cb03aeae7b55260b
  languageName: node
  linkType: hard

"json-stable-stringify-without-jsonify@npm:^1.0.1":
  version: 1.0.1
  resolution: "json-stable-stringify-without-jsonify@npm:1.0.1"
  checksum: 10/12786c2e2f22c27439e6db0532ba321f1d0617c27ad8cb1c352a0e9249a50182fd1ba8b52a18899291604b0c32eafa8afd09e51203f19109a0537f68db2b652d
  languageName: node
  linkType: hard

"json5@npm:^1.0.2":
  version: 1.0.2
  resolution: "json5@npm:1.0.2"
  dependencies:
    minimist: "npm:^1.2.0"
  bin:
    json5: lib/cli.js
  checksum: 10/a78d812dbbd5642c4f637dd130954acfd231b074965871c3e28a5bbd571f099d623ecf9161f1960c4ddf68e0cc98dee8bebfdb94a71ad4551f85a1afc94b63f6
  languageName: node
  linkType: hard

"json5@npm:^2.2.3":
  version: 2.2.3
  resolution: "json5@npm:2.2.3"
  bin:
    json5: lib/cli.js
  checksum: 10/1db67b853ff0de3534085d630691d3247de53a2ed1390ba0ddff681ea43e9b3e30ecbdb65c5e9aab49435e44059c23dbd6fee8ee619419ba37465bb0dd7135da
  languageName: node
  linkType: hard

"jsonc-parser@npm:^3.0.0":
  version: 3.3.1
  resolution: "jsonc-parser@npm:3.3.1"
  checksum: 10/9b0dc391f20b47378f843ef1e877e73ec652a5bdc3c5fa1f36af0f119a55091d147a86c1ee86a232296f55c929bba174538c2bf0312610e0817a22de131cc3f4
  languageName: node
  linkType: hard

"jsx-ast-utils@npm:3.3.5, jsx-ast-utils@npm:^2.4.1 || ^3.0.0, jsx-ast-utils@npm:^3.3.5":
  version: 3.3.5
  resolution: "jsx-ast-utils@npm:3.3.5"
  dependencies:
    array-includes: "npm:^3.1.6"
    array.prototype.flat: "npm:^1.3.1"
    object.assign: "npm:^4.1.4"
    object.values: "npm:^1.1.6"
  checksum: 10/b61d44613687dfe4cc8ad4b4fbf3711bf26c60b8d5ed1f494d723e0808415c59b24a7c0ed8ab10736a40ff84eef38cbbfb68b395e05d31117b44ffc59d31edfc
  languageName: node
  linkType: hard

"keyv@npm:^4.5.4":
  version: 4.5.4
  resolution: "keyv@npm:4.5.4"
  dependencies:
    json-buffer: "npm:3.0.1"
  checksum: 10/167eb6ef64cc84b6fa0780ee50c9de456b422a1e18802209234f7c2cf7eae648c7741f32e50d7e24ccb22b24c13154070b01563d642755b156c357431a191e75
  languageName: node
  linkType: hard

"language-subtag-registry@npm:^0.3.20":
  version: 0.3.23
  resolution: "language-subtag-registry@npm:0.3.23"
  checksum: 10/fe13ed74ab9f862db8e5747b98cc9aa08d52a19f85b5cdb4975cd364c8539bd2da3380e4560d2dbbd728ec33dff8a4b4421fcb2e5b1b1bdaa21d16f91a54d0d4
  languageName: node
  linkType: hard

"language-tags@npm:^1.0.9":
  version: 1.0.9
  resolution: "language-tags@npm:1.0.9"
  dependencies:
    language-subtag-registry: "npm:^0.3.20"
  checksum: 10/d3a7c14b694e67f519153d6df6cb200681648d38d623c3bfa9d6a66a5ec5493628acb88e9df5aceef3cf1902ab263a205e7d59ee4cf1d6bb67e707b83538bd6d
  languageName: node
  linkType: hard

"levn@npm:^0.4.1":
  version: 0.4.1
  resolution: "levn@npm:0.4.1"
  dependencies:
    prelude-ls: "npm:^1.2.1"
    type-check: "npm:~0.4.0"
  checksum: 10/2e4720ff79f21ae08d42374b0a5c2f664c5be8b6c8f565bb4e1315c96ed3a8acaa9de788ffed82d7f2378cf36958573de07ef92336cb5255ed74d08b8318c9ee
  languageName: node
  linkType: hard

"lines-and-columns@npm:^1.1.6":
  version: 1.2.4
  resolution: "lines-and-columns@npm:1.2.4"
  checksum: 10/0c37f9f7fa212b38912b7145e1cd16a5f3cd34d782441c3e6ca653485d326f58b3caccda66efce1c5812bde4961bbde3374fae4b0d11bf1226152337f3894aa5
  languageName: node
  linkType: hard

"load-tsconfig@npm:^0.2.3":
  version: 0.2.5
  resolution: "load-tsconfig@npm:0.2.5"
  checksum: 10/b3176f6f0c86dbdbbc7e337440a803b0b4407c55e2e1cfc53bd3db68e0211448f36428a6075ecf5e286db5d1bf791da756fc0ac4d2447717140fb6a5218ecfb4
  languageName: node
  linkType: hard

"locate-path@npm:^6.0.0":
  version: 6.0.0
  resolution: "locate-path@npm:6.0.0"
  dependencies:
    p-locate: "npm:^5.0.0"
  checksum: 10/72eb661788a0368c099a184c59d2fee760b3831c9c1c33955e8a19ae4a21b4116e53fa736dc086cdeb9fce9f7cc508f2f92d2d3aae516f133e16a2bb59a39f5a
  languageName: node
  linkType: hard

"lodash.debounce@npm:^4.0.8":
  version: 4.0.8
  resolution: "lodash.debounce@npm:4.0.8"
  checksum: 10/cd0b2819786e6e80cb9f5cda26b1a8fc073daaf04e48d4cb462fa4663ec9adb3a5387aa22d7129e48eed1afa05b482e2a6b79bfc99b86886364449500cbb00fd
  languageName: node
  linkType: hard

"lodash.merge@npm:^4.6.2":
  version: 4.6.2
  resolution: "lodash.merge@npm:4.6.2"
  checksum: 10/d0ea2dd0097e6201be083865d50c3fb54fbfbdb247d9cc5950e086c991f448b7ab0cdab0d57eacccb43473d3f2acd21e134db39f22dac2d6c9ba6bf26978e3d6
  languageName: node
  linkType: hard

"lodash@npm:^4.17.21":
  version: 4.17.21
  resolution: "lodash@npm:4.17.21"
  checksum: 10/c08619c038846ea6ac754abd6dd29d2568aa705feb69339e836dfa8d8b09abbb2f859371e86863eda41848221f9af43714491467b5b0299122431e202bb0c532
  languageName: node
  linkType: hard

"loose-envify@npm:^1.4.0":
  version: 1.4.0
  resolution: "loose-envify@npm:1.4.0"
  dependencies:
    js-tokens: "npm:^3.0.0 || ^4.0.0"
  bin:
    loose-envify: cli.js
  checksum: 10/6517e24e0cad87ec9888f500c5b5947032cdfe6ef65e1c1936a0c48a524b81e65542c9c3edc91c97d5bddc806ee2a985dbc79be89215d613b1de5db6d1cfe6f4
  languageName: node
  linkType: hard

"loupe@npm:^3.1.0, loupe@npm:^3.1.4":
  version: 3.2.1
  resolution: "loupe@npm:3.2.1"
  checksum: 10/a4d78ec758aaa04e0e35d5cd1c15e970beb9cdbfd3d0f34f98b9bcda489f896a7190b3b6cc40b7a6dcb8e97e82e96eafaae10096aaa469804acdba6f7c2bde5f
  languageName: node
  linkType: hard

"lower-case@npm:^2.0.2":
  version: 2.0.2
  resolution: "lower-case@npm:2.0.2"
  dependencies:
    tslib: "npm:^2.0.3"
  checksum: 10/83a0a5f159ad7614bee8bf976b96275f3954335a84fad2696927f609ddae902802c4f3312d86668722e668bef41400254807e1d3a7f2e8c3eede79691aa1f010
  languageName: node
  linkType: hard

"lru-cache@npm:^10.0.1, lru-cache@npm:^10.2.0, lru-cache@npm:^10.4.3":
  version: 10.4.3
  resolution: "lru-cache@npm:10.4.3"
  checksum: 10/e6e90267360476720fa8e83cc168aa2bf0311f3f2eea20a6ba78b90a885ae72071d9db132f40fda4129c803e7dcec3a6b6a6fbb44ca90b081630b810b5d6a41a
  languageName: node
  linkType: hard

"lru-cache@npm:^5.1.1":
  version: 5.1.1
  resolution: "lru-cache@npm:5.1.1"
  dependencies:
    yallist: "npm:^3.0.2"
  checksum: 10/951d2673dcc64a7fb888bf3d13bc2fdf923faca97d89cdb405ba3dfff77e2b26e5798d405e78fcd7094c9e7b8b4dab2ddc5a4f8a11928af24a207b7c738ca3f8
  languageName: node
  linkType: hard

"lz-string@npm:^1.5.0":
  version: 1.5.0
  resolution: "lz-string@npm:1.5.0"
  bin:
    lz-string: bin/bin.js
  checksum: 10/e86f0280e99a8d8cd4eef24d8601ddae15ce54e43ac9990dfcb79e1e081c255ad24424a30d78d2ad8e51a8ce82a66a930047fed4b4aa38c6f0b392ff9300edfc
  languageName: node
  linkType: hard

"magic-string@npm:0.30.17":
  version: 0.30.17
  resolution: "magic-string@npm:0.30.17"
  dependencies:
    "@jridgewell/sourcemap-codec": "npm:^1.5.0"
  checksum: 10/2f71af2b0afd78c2e9012a29b066d2c8ba45a9cd0c8070f7fd72de982fb1c403b4e3afdb1dae00691d56885ede66b772ef6bedf765e02e3a7066208fe2fec4aa
  languageName: node
  linkType: hard

"magic-string@npm:^0.30.17, magic-string@npm:~0.30.11":
  version: 0.30.18
  resolution: "magic-string@npm:0.30.18"
  dependencies:
    "@jridgewell/sourcemap-codec": "npm:^1.5.5"
  checksum: 10/dd180f174cfe066f501dc700ec58815eae3cbde402308f3326574bad09a5ff9c9c5b6fc71502e9d1c663254614fe21679c6360cd39aaeedd2cee1b40e14669f2
  languageName: node
  linkType: hard

"magicast@npm:^0.3.5":
  version: 0.3.5
  resolution: "magicast@npm:0.3.5"
  dependencies:
    "@babel/parser": "npm:^7.25.4"
    "@babel/types": "npm:^7.25.4"
    source-map-js: "npm:^1.2.0"
  checksum: 10/3a2dba6b0bdde957797361d09c7931ebdc1b30231705360eeb40ed458d28e1c3112841c3ed4e1b87ceb28f741e333c7673cd961193aa9fdb4f4946b202e6205a
  languageName: node
  linkType: hard

"make-dir@npm:^4.0.0":
  version: 4.0.0
  resolution: "make-dir@npm:4.0.0"
  dependencies:
    semver: "npm:^7.5.3"
  checksum: 10/bf0731a2dd3aab4db6f3de1585cea0b746bb73eb5a02e3d8d72757e376e64e6ada190b1eddcde5b2f24a81b688a9897efd5018737d05e02e2a671dda9cff8a8a
  languageName: node
  linkType: hard

"make-fetch-happen@npm:^14.0.3":
  version: 14.0.3
  resolution: "make-fetch-happen@npm:14.0.3"
  dependencies:
    "@npmcli/agent": "npm:^3.0.0"
    cacache: "npm:^19.0.1"
    http-cache-semantics: "npm:^4.1.1"
    minipass: "npm:^7.0.2"
    minipass-fetch: "npm:^4.0.0"
    minipass-flush: "npm:^1.0.5"
    minipass-pipeline: "npm:^1.2.4"
    negotiator: "npm:^1.0.0"
    proc-log: "npm:^5.0.0"
    promise-retry: "npm:^2.0.1"
    ssri: "npm:^12.0.0"
  checksum: 10/fce0385840b6d86b735053dfe941edc2dd6468fda80fe74da1eeff10cbd82a75760f406194f2bc2fa85b99545b2bc1f84c08ddf994b21830775ba2d1a87e8bdf
  languageName: node
  linkType: hard

"math-intrinsics@npm:^1.1.0":
  version: 1.1.0
  resolution: "math-intrinsics@npm:1.1.0"
  checksum: 10/11df2eda46d092a6035479632e1ec865b8134bdfc4bd9e571a656f4191525404f13a283a515938c3a8de934dbfd9c09674d9da9fa831e6eb7e22b50b197d2edd
  languageName: node
  linkType: hard

"media-typer@npm:0.3.0":
  version: 0.3.0
  resolution: "media-typer@npm:0.3.0"
  checksum: 10/38e0984db39139604756903a01397e29e17dcb04207bb3e081412ce725ab17338ecc47220c1b186b6bbe79a658aad1b0d41142884f5a481f36290cdefbe6aa46
  languageName: node
  linkType: hard

"merge-descriptors@npm:1.0.3":
  version: 1.0.3
  resolution: "merge-descriptors@npm:1.0.3"
  checksum: 10/52117adbe0313d5defa771c9993fe081e2d2df9b840597e966aadafde04ae8d0e3da46bac7ca4efc37d4d2b839436582659cd49c6a43eacb3fe3050896a105d1
  languageName: node
  linkType: hard

"merge2@npm:^1.3.0, merge2@npm:^1.4.1":
  version: 1.4.1
  resolution: "merge2@npm:1.4.1"
  checksum: 10/7268db63ed5169466540b6fb947aec313200bcf6d40c5ab722c22e242f651994619bcd85601602972d3c85bd2cc45a358a4c61937e9f11a061919a1da569b0c2
  languageName: node
  linkType: hard

"meshoptimizer@npm:~0.22.0":
  version: 0.22.0
  resolution: "meshoptimizer@npm:0.22.0"
  checksum: 10/6def62fe70181f69a2ff2088cfb8aa8137b5cd6a1a952100957f819b2dba42c1cc8afcb7de41e1e1619c8146ef0612f5f71e1ceab1f9b224567ff285af0080e8
  languageName: node
  linkType: hard

"methods@npm:~1.1.2":
  version: 1.1.2
  resolution: "methods@npm:1.1.2"
  checksum: 10/a385dd974faa34b5dd021b2bbf78c722881bf6f003bfe6d391d7da3ea1ed625d1ff10ddd13c57531f628b3e785be38d3eed10ad03cebd90b76932413df9a1820
  languageName: node
  linkType: hard

"micromatch@npm:4.0.8, micromatch@npm:^4.0.8":
  version: 4.0.8
  resolution: "micromatch@npm:4.0.8"
  dependencies:
    braces: "npm:^3.0.3"
    picomatch: "npm:^2.3.1"
  checksum: 10/6bf2a01672e7965eb9941d1f02044fad2bd12486b5553dc1116ff24c09a8723157601dc992e74c911d896175918448762df3b3fd0a6b61037dd1a9766ddfbf58
  languageName: node
  linkType: hard

"mime-db@npm:1.52.0":
  version: 1.52.0
  resolution: "mime-db@npm:1.52.0"
  checksum: 10/54bb60bf39e6f8689f6622784e668a3d7f8bed6b0d886f5c3c446cb3284be28b30bf707ed05d0fe44a036f8469976b2629bbea182684977b084de9da274694d7
  languageName: node
  linkType: hard

"mime-types@npm:^2.1.12, mime-types@npm:~2.1.24, mime-types@npm:~2.1.34":
  version: 2.1.35
  resolution: "mime-types@npm:2.1.35"
  dependencies:
    mime-db: "npm:1.52.0"
  checksum: 10/89aa9651b67644035de2784a6e665fc685d79aba61857e02b9c8758da874a754aed4a9aced9265f5ed1171fd934331e5516b84a7f0218031b6fa0270eca1e51a
  languageName: node
  linkType: hard

"mime@npm:1.6.0":
  version: 1.6.0
  resolution: "mime@npm:1.6.0"
  bin:
    mime: cli.js
  checksum: 10/b7d98bb1e006c0e63e2c91b590fe1163b872abf8f7ef224d53dd31499c2197278a6d3d0864c45239b1a93d22feaf6f9477e9fc847eef945838150b8c02d03170
  languageName: node
  linkType: hard

"min-indent@npm:^1.0.0":
  version: 1.0.1
  resolution: "min-indent@npm:1.0.1"
  checksum: 10/bfc6dd03c5eaf623a4963ebd94d087f6f4bbbfd8c41329a7f09706b0cb66969c4ddd336abeb587bc44bc6f08e13bf90f0b374f9d71f9f01e04adc2cd6f083ef1
  languageName: node
  linkType: hard

"minimatch@npm:9.0.5, minimatch@npm:^9.0.4":
  version: 9.0.5
  resolution: "minimatch@npm:9.0.5"
  dependencies:
    brace-expansion: "npm:^2.0.1"
  checksum: 10/dd6a8927b063aca6d910b119e1f2df6d2ce7d36eab91de83167dd136bb85e1ebff97b0d3de1cb08bd1f7e018ca170b4962479fefab5b2a69e2ae12cb2edc8348
  languageName: node
  linkType: hard

"minimatch@npm:^3.1.2":
  version: 3.1.2
  resolution: "minimatch@npm:3.1.2"
  dependencies:
    brace-expansion: "npm:^1.1.7"
  checksum: 10/e0b25b04cd4ec6732830344e5739b13f8690f8a012d73445a4a19fbc623f5dd481ef7a5827fde25954cd6026fede7574cc54dc4643c99d6c6b653d6203f94634
  languageName: node
  linkType: hard

"minimist@npm:1.2.8, minimist@npm:^1.2.0, minimist@npm:^1.2.6":
  version: 1.2.8
  resolution: "minimist@npm:1.2.8"
  checksum: 10/908491b6cc15a6c440ba5b22780a0ba89b9810e1aea684e253e43c4e3b8d56ec1dcdd7ea96dde119c29df59c936cde16062159eae4225c691e19c70b432b6e6f
  languageName: node
  linkType: hard

"minipass-collect@npm:^2.0.1":
  version: 2.0.1
  resolution: "minipass-collect@npm:2.0.1"
  dependencies:
    minipass: "npm:^7.0.3"
  checksum: 10/b251bceea62090f67a6cced7a446a36f4cd61ee2d5cea9aee7fff79ba8030e416327a1c5aa2908dc22629d06214b46d88fdab8c51ac76bacbf5703851b5ad342
  languageName: node
  linkType: hard

"minipass-fetch@npm:^4.0.0":
  version: 4.0.1
  resolution: "minipass-fetch@npm:4.0.1"
  dependencies:
    encoding: "npm:^0.1.13"
    minipass: "npm:^7.0.3"
    minipass-sized: "npm:^1.0.3"
    minizlib: "npm:^3.0.1"
  dependenciesMeta:
    encoding:
      optional: true
  checksum: 10/7ddfebdbb87d9866e7b5f7eead5a9e3d9d507992af932a11d275551f60006cf7d9178e66d586dbb910894f3e3458d27c0ddf93c76e94d49d0a54a541ddc1263d
  languageName: node
  linkType: hard

"minipass-flush@npm:^1.0.5":
  version: 1.0.5
  resolution: "minipass-flush@npm:1.0.5"
  dependencies:
    minipass: "npm:^3.0.0"
  checksum: 10/56269a0b22bad756a08a94b1ffc36b7c9c5de0735a4dd1ab2b06c066d795cfd1f0ac44a0fcae13eece5589b908ecddc867f04c745c7009be0b566421ea0944cf
  languageName: node
  linkType: hard

"minipass-pipeline@npm:^1.2.4":
  version: 1.2.4
  resolution: "minipass-pipeline@npm:1.2.4"
  dependencies:
    minipass: "npm:^3.0.0"
  checksum: 10/b14240dac0d29823c3d5911c286069e36d0b81173d7bdf07a7e4a91ecdef92cdff4baaf31ea3746f1c61e0957f652e641223970870e2353593f382112257971b
  languageName: node
  linkType: hard

"minipass-sized@npm:^1.0.3":
  version: 1.0.3
  resolution: "minipass-sized@npm:1.0.3"
  dependencies:
    minipass: "npm:^3.0.0"
  checksum: 10/40982d8d836a52b0f37049a0a7e5d0f089637298e6d9b45df9c115d4f0520682a78258905e5c8b180fb41b593b0a82cc1361d2c74b45f7ada66334f84d1ecfdd
  languageName: node
  linkType: hard

"minipass@npm:^3.0.0":
  version: 3.3.6
  resolution: "minipass@npm:3.3.6"
  dependencies:
    yallist: "npm:^4.0.0"
  checksum: 10/a5c6ef069f70d9a524d3428af39f2b117ff8cd84172e19b754e7264a33df460873e6eb3d6e55758531580970de50ae950c496256bb4ad3691a2974cddff189f0
  languageName: node
  linkType: hard

"minipass@npm:^5.0.0 || ^6.0.2 || ^7.0.0, minipass@npm:^7.0.2, minipass@npm:^7.0.3, minipass@npm:^7.0.4, minipass@npm:^7.1.2":
  version: 7.1.2
  resolution: "minipass@npm:7.1.2"
  checksum: 10/c25f0ee8196d8e6036661104bacd743785b2599a21de5c516b32b3fa2b83113ac89a2358465bc04956baab37ffb956ae43be679b2262bf7be15fce467ccd7950
  languageName: node
  linkType: hard

"minizlib@npm:^3.0.1":
  version: 3.0.2
  resolution: "minizlib@npm:3.0.2"
  dependencies:
    minipass: "npm:^7.1.2"
  checksum: 10/c075bed1594f68dcc8c35122333520112daefd4d070e5d0a228bd4cf5580e9eed3981b96c0ae1d62488e204e80fd27b2b9d0068ca9a5ef3993e9565faf63ca41
  languageName: node
  linkType: hard

"mkdirp@npm:^3.0.1":
  version: 3.0.1
  resolution: "mkdirp@npm:3.0.1"
  bin:
    mkdirp: dist/cjs/src/bin.js
  checksum: 10/16fd79c28645759505914561e249b9a1f5fe3362279ad95487a4501e4467abeb714fd35b95307326b8fd03f3c7719065ef11a6f97b7285d7888306d1bd2232ba
  languageName: node
  linkType: hard

"mockjs@npm:1.1.0":
  version: 1.1.0
  resolution: "mockjs@npm:1.1.0"
  dependencies:
    commander: "npm:*"
  bin:
    random: bin/random
  checksum: 10/44e7114e49c0244b16199e6bcef97cf177990ab4b7cd905bf74919fb311a6174fec9dbde97c8d9b781fee3c9474f771582ad5c1ccbcf7b2fc35771bfd735499b
  languageName: node
  linkType: hard

"mrmime@npm:^2.0.0":
  version: 2.0.1
  resolution: "mrmime@npm:2.0.1"
  checksum: 10/1f966e2c05b7264209c4149ae50e8e830908eb64dd903535196f6ad72681fa109b794007288a3c2814f7a1ecf9ca192769909c0c374d974d604a8de5fc095d4a
  languageName: node
  linkType: hard

"ms@npm:2.0.0":
  version: 2.0.0
  resolution: "ms@npm:2.0.0"
  checksum: 10/0e6a22b8b746d2e0b65a430519934fefd41b6db0682e3477c10f60c76e947c4c0ad06f63ffdf1d78d335f83edee8c0aa928aa66a36c7cd95b69b26f468d527f4
  languageName: node
  linkType: hard

"ms@npm:2.1.3, ms@npm:^2.1.1, ms@npm:^2.1.3":
  version: 2.1.3
  resolution: "ms@npm:2.1.3"
  checksum: 10/aa92de608021b242401676e35cfa5aa42dd70cbdc082b916da7fb925c542173e36bce97ea3e804923fe92c0ad991434e4a38327e15a1b5b5f945d66df615ae6d
  languageName: node
  linkType: hard

"nanoid@npm:^3.3.11, nanoid@npm:^3.3.7, nanoid@npm:^3.3.8":
  version: 3.3.11
  resolution: "nanoid@npm:3.3.11"
  bin:
    nanoid: bin/nanoid.cjs
  checksum: 10/73b5afe5975a307aaa3c95dfe3334c52cdf9ae71518176895229b8d65ab0d1c0417dd081426134eb7571c055720428ea5d57c645138161e7d10df80815527c48
  languageName: node
  linkType: hard

"napi-postinstall@npm:^0.3.0":
  version: 0.3.3
  resolution: "napi-postinstall@npm:0.3.3"
  bin:
    napi-postinstall: lib/cli.js
  checksum: 10/8e3d1ab7bb324491eb20b7548e05033139e6c48c9cf2d8cd3ae0c17c43716751a8cda0a2e740bb09c822dfc1a0dca207e34c107067b4f66842e30c60c599c901
  languageName: node
  linkType: hard

"natural-compare@npm:^1.4.0":
  version: 1.4.0
  resolution: "natural-compare@npm:1.4.0"
  checksum: 10/23ad088b08f898fc9b53011d7bb78ec48e79de7627e01ab5518e806033861bef68d5b0cd0e2205c2f36690ac9571ff6bcb05eb777ced2eeda8d4ac5b44592c3d
  languageName: node
  linkType: hard

"negotiator@npm:0.6.3":
  version: 0.6.3
  resolution: "negotiator@npm:0.6.3"
  checksum: 10/2723fb822a17ad55c93a588a4bc44d53b22855bf4be5499916ca0cab1e7165409d0b288ba2577d7b029f10ce18cf2ed8e703e5af31c984e1e2304277ef979837
  languageName: node
  linkType: hard

"negotiator@npm:^1.0.0":
  version: 1.0.0
  resolution: "negotiator@npm:1.0.0"
  checksum: 10/b5734e87295324fabf868e36fb97c84b7d7f3156ec5f4ee5bf6e488079c11054f818290fc33804cef7b1ee21f55eeb14caea83e7dafae6492a409b3e573153e5
  languageName: node
  linkType: hard

"no-case@npm:^3.0.4":
  version: 3.0.4
  resolution: "no-case@npm:3.0.4"
  dependencies:
    lower-case: "npm:^2.0.2"
    tslib: "npm:^2.0.3"
  checksum: 10/0b2ebc113dfcf737d48dde49cfebf3ad2d82a8c3188e7100c6f375e30eafbef9e9124aadc3becef237b042fd5eb0aad2fd78669c20972d045bbe7fea8ba0be5c
  languageName: node
  linkType: hard

"node-fetch@npm:^2.6.1":
  version: 2.7.0
  resolution: "node-fetch@npm:2.7.0"
  dependencies:
    whatwg-url: "npm:^5.0.0"
  peerDependencies:
    encoding: ^0.1.0
  peerDependenciesMeta:
    encoding:
      optional: true
  checksum: 10/b24f8a3dc937f388192e59bcf9d0857d7b6940a2496f328381641cb616efccc9866e89ec43f2ec956bbd6c3d3ee05524ce77fe7b29ccd34692b3a16f237d6676
  languageName: node
  linkType: hard

"node-gyp@npm:latest":
  version: 11.4.2
  resolution: "node-gyp@npm:11.4.2"
  dependencies:
    env-paths: "npm:^2.2.0"
    exponential-backoff: "npm:^3.1.1"
    graceful-fs: "npm:^4.2.6"
    make-fetch-happen: "npm:^14.0.3"
    nopt: "npm:^8.0.0"
    proc-log: "npm:^5.0.0"
    semver: "npm:^7.3.5"
    tar: "npm:^7.4.3"
    tinyglobby: "npm:^0.2.12"
    which: "npm:^5.0.0"
  bin:
    node-gyp: bin/node-gyp.js
  checksum: 10/de0fdd1a23d27976974f2480b1c5a2954180050f4d7d682b2fcd36a7c996100981fc37ba0c893d02471ccf1730240f73c3073a6a9397c5eb3bb7578ca82808ed
  languageName: node
  linkType: hard

"node-html-parser@npm:^5.3.3":
  version: 5.4.2
  resolution: "node-html-parser@npm:5.4.2"
  dependencies:
    css-select: "npm:^4.2.1"
    he: "npm:1.2.0"
  checksum: 10/90b6a2f21aeed6e5bc8553b6bda51324ae6679e6322dff655890559ee621a156e53a747643eeaab18c3434e1ab9463f6b4718ed4ccb8fed72567e58cf82d6cb7
  languageName: node
  linkType: hard

"node-releases@npm:^2.0.19":
  version: 2.0.19
  resolution: "node-releases@npm:2.0.19"
  checksum: 10/c2b33b4f0c40445aee56141f13ca692fa6805db88510e5bbb3baadb2da13e1293b738e638e15e4a8eb668bb9e97debb08e7a35409b477b5cc18f171d35a83045
  languageName: node
  linkType: hard

"nopt@npm:^8.0.0":
  version: 8.1.0
  resolution: "nopt@npm:8.1.0"
  dependencies:
    abbrev: "npm:^3.0.0"
  bin:
    nopt: bin/nopt.js
  checksum: 10/26ab456c51a96f02a9e5aa8d1b80ef3219f2070f3f3528a040e32fb735b1e651e17bdf0f1476988d3a46d498f35c65ed662d122f340d38ce4a7e71dd7b20c4bc
  languageName: node
  linkType: hard

"normalize-path@npm:^3.0.0, normalize-path@npm:~3.0.0":
  version: 3.0.0
  resolution: "normalize-path@npm:3.0.0"
  checksum: 10/88eeb4da891e10b1318c4b2476b6e2ecbeb5ff97d946815ffea7794c31a89017c70d7f34b3c2ebf23ef4e9fc9fb99f7dffe36da22011b5b5c6ffa34f4873ec20
  languageName: node
  linkType: hard

"npm-run-path@npm:^6.0.0":
  version: 6.0.0
  resolution: "npm-run-path@npm:6.0.0"
  dependencies:
    path-key: "npm:^4.0.0"
    unicorn-magic: "npm:^0.3.0"
  checksum: 10/1a1b50aba6e6af7fd34a860ba2e252e245c4a59b316571a990356417c0cdf0414cabf735f7f52d9c330899cb56f0ab804a8e21fb12a66d53d7843e39ada4a3b6
  languageName: node
  linkType: hard

"nth-check@npm:^2.0.1":
  version: 2.1.1
  resolution: "nth-check@npm:2.1.1"
  dependencies:
    boolbase: "npm:^1.0.0"
  checksum: 10/5afc3dafcd1573b08877ca8e6148c52abd565f1d06b1eb08caf982e3fa289a82f2cae697ffb55b5021e146d60443f1590a5d6b944844e944714a5b549675bcd3
  languageName: node
  linkType: hard

"nwsapi@npm:^2.2.16":
  version: 2.2.21
  resolution: "nwsapi@npm:2.2.21"
  checksum: 10/3d84e7e0691640028fd7b1e93f3368cb1b5958332cecdcb31f335178177a6efdd00a07fb68b99cc476f0ca835bed5bd79b1010a16b97d33ce6c3c3c94bebd05c
  languageName: node
  linkType: hard

"object-assign@npm:^4.1.1":
  version: 4.1.1
  resolution: "object-assign@npm:4.1.1"
  checksum: 10/fcc6e4ea8c7fe48abfbb552578b1c53e0d194086e2e6bbbf59e0a536381a292f39943c6e9628af05b5528aa5e3318bb30d6b2e53cadaf5b8fe9e12c4b69af23f
  languageName: node
  linkType: hard

"object-inspect@npm:^1.13.3, object-inspect@npm:^1.13.4":
  version: 1.13.4
  resolution: "object-inspect@npm:1.13.4"
  checksum: 10/aa13b1190ad3e366f6c83ad8a16ed37a19ed57d267385aa4bfdccda833d7b90465c057ff6c55d035a6b2e52c1a2295582b294217a0a3a1ae7abdd6877ef781fb
  languageName: node
  linkType: hard

"object-is@npm:^1.1.5":
  version: 1.1.6
  resolution: "object-is@npm:1.1.6"
  dependencies:
    call-bind: "npm:^1.0.7"
    define-properties: "npm:^1.2.1"
  checksum: 10/4f6f544773a595da21c69a7531e0e1d6250670f4e09c55f47eb02c516035cfcb1b46ceb744edfd3ecb362309dbccb6d7f88e43bf42e4d4595ac10a329061053a
  languageName: node
  linkType: hard

"object-keys@npm:^1.1.1":
  version: 1.1.1
  resolution: "object-keys@npm:1.1.1"
  checksum: 10/3d81d02674115973df0b7117628ea4110d56042e5326413e4b4313f0bcdf7dd78d4a3acef2c831463fa3796a66762c49daef306f4a0ea1af44877d7086d73bde
  languageName: node
  linkType: hard

"object.assign@npm:^4.1.2, object.assign@npm:^4.1.4, object.assign@npm:^4.1.7":
  version: 4.1.7
  resolution: "object.assign@npm:4.1.7"
  dependencies:
    call-bind: "npm:^1.0.8"
    call-bound: "npm:^1.0.3"
    define-properties: "npm:^1.2.1"
    es-object-atoms: "npm:^1.0.0"
    has-symbols: "npm:^1.1.0"
    object-keys: "npm:^1.1.1"
  checksum: 10/3fe28cdd779f2a728a9a66bd688679ba231a2b16646cd1e46b528fe7c947494387dda4bc189eff3417f3717ef4f0a8f2439347cf9a9aa3cef722fbfd9f615587
  languageName: node
  linkType: hard

"object.entries@npm:^1.1.5, object.entries@npm:^1.1.9":
  version: 1.1.9
  resolution: "object.entries@npm:1.1.9"
  dependencies:
    call-bind: "npm:^1.0.8"
    call-bound: "npm:^1.0.4"
    define-properties: "npm:^1.2.1"
    es-object-atoms: "npm:^1.1.1"
  checksum: 10/24163ab1e1e013796693fc5f5d349e8b3ac0b6a34a7edb6c17d3dd45c6a8854145780c57d302a82512c1582f63720f4b4779d6c1cfba12cbb1420b978802d8a3
  languageName: node
  linkType: hard

"object.fromentries@npm:^2.0.8":
  version: 2.0.8
  resolution: "object.fromentries@npm:2.0.8"
  dependencies:
    call-bind: "npm:^1.0.7"
    define-properties: "npm:^1.2.1"
    es-abstract: "npm:^1.23.2"
    es-object-atoms: "npm:^1.0.0"
  checksum: 10/5b2e80f7af1778b885e3d06aeb335dcc86965e39464671adb7167ab06ac3b0f5dd2e637a90d8ebd7426d69c6f135a4753ba3dd7d0fe2a7030cf718dcb910fd92
  languageName: node
  linkType: hard

"object.groupby@npm:^1.0.3":
  version: 1.0.3
  resolution: "object.groupby@npm:1.0.3"
  dependencies:
    call-bind: "npm:^1.0.7"
    define-properties: "npm:^1.2.1"
    es-abstract: "npm:^1.23.2"
  checksum: 10/44cb86dd2c660434be65f7585c54b62f0425b0c96b5c948d2756be253ef06737da7e68d7106e35506ce4a44d16aa85a413d11c5034eb7ce5579ec28752eb42d0
  languageName: node
  linkType: hard

"object.values@npm:^1.1.6, object.values@npm:^1.2.0, object.values@npm:^1.2.1":
  version: 1.2.1
  resolution: "object.values@npm:1.2.1"
  dependencies:
    call-bind: "npm:^1.0.8"
    call-bound: "npm:^1.0.3"
    define-properties: "npm:^1.2.1"
    es-object-atoms: "npm:^1.0.0"
  checksum: 10/f5ec9eccdefeaaa834b089c525663436812a65ff13de7964a1c3a9110f32054f2d58aa476a645bb14f75a79f3fe1154fb3e7bfdae7ac1e80affe171b2ef74bce
  languageName: node
  linkType: hard

"on-finished@npm:2.4.1":
  version: 2.4.1
  resolution: "on-finished@npm:2.4.1"
  dependencies:
    ee-first: "npm:1.1.1"
  checksum: 10/8e81472c5028125c8c39044ac4ab8ba51a7cdc19a9fbd4710f5d524a74c6d8c9ded4dd0eed83f28d3d33ac1d7a6a439ba948ccb765ac6ce87f30450a26bfe2ea
  languageName: node
  linkType: hard

"on-finished@npm:~2.3.0":
  version: 2.3.0
  resolution: "on-finished@npm:2.3.0"
  dependencies:
    ee-first: "npm:1.1.1"
  checksum: 10/1db595bd963b0124d6fa261d18320422407b8f01dc65863840f3ddaaf7bcad5b28ff6847286703ca53f4ec19595bd67a2f1253db79fc4094911ec6aa8df1671b
  languageName: node
  linkType: hard

"open@npm:^8.4.0":
  version: 8.4.2
  resolution: "open@npm:8.4.2"
  dependencies:
    define-lazy-prop: "npm:^2.0.0"
    is-docker: "npm:^2.1.1"
    is-wsl: "npm:^2.2.0"
  checksum: 10/acd81a1d19879c818acb3af2d2e8e9d81d17b5367561e623248133deb7dd3aefaed527531df2677d3e6aaf0199f84df57b6b2262babff8bf46ea0029aac536c9
  languageName: node
  linkType: hard

"optionator@npm:^0.9.3":
  version: 0.9.4
  resolution: "optionator@npm:0.9.4"
  dependencies:
    deep-is: "npm:^0.1.3"
    fast-levenshtein: "npm:^2.0.6"
    levn: "npm:^0.4.1"
    prelude-ls: "npm:^1.2.1"
    type-check: "npm:^0.4.0"
    word-wrap: "npm:^1.2.5"
  checksum: 10/a8398559c60aef88d7f353a4f98dcdff6090a4e70f874c827302bf1213d9106a1c4d5fcb68dacb1feb3c30a04c4102f41047aa55d4c576b863d6fc876e001af6
  languageName: node
  linkType: hard

"own-keys@npm:^1.0.1":
  version: 1.0.1
  resolution: "own-keys@npm:1.0.1"
  dependencies:
    get-intrinsic: "npm:^1.2.6"
    object-keys: "npm:^1.1.1"
    safe-push-apply: "npm:^1.0.0"
  checksum: 10/ab4bb3b8636908554fc19bf899e225444195092864cb61503a0d048fdaf662b04be2605b636a4ffeaf6e8811f6fcfa8cbb210ec964c0eb1a41eb853e1d5d2f41
  languageName: node
  linkType: hard

"p-limit@npm:^3.0.2":
  version: 3.1.0
  resolution: "p-limit@npm:3.1.0"
  dependencies:
    yocto-queue: "npm:^0.1.0"
  checksum: 10/7c3690c4dbf62ef625671e20b7bdf1cbc9534e83352a2780f165b0d3ceba21907e77ad63401708145ca4e25bfc51636588d89a8c0aeb715e6c37d1c066430360
  languageName: node
  linkType: hard

"p-locate@npm:^5.0.0":
  version: 5.0.0
  resolution: "p-locate@npm:5.0.0"
  dependencies:
    p-limit: "npm:^3.0.2"
  checksum: 10/1623088f36cf1cbca58e9b61c4e62bf0c60a07af5ae1ca99a720837356b5b6c5ba3eb1b2127e47a06865fee59dd0453cad7cc844cda9d5a62ac1a5a51b7c86d3
  languageName: node
  linkType: hard

"p-map@npm:^7.0.2":
  version: 7.0.3
  resolution: "p-map@npm:7.0.3"
  checksum: 10/2ef48ccfc6dd387253d71bf502604f7893ed62090b2c9d73387f10006c342606b05233da0e4f29388227b61eb5aeface6197e166520c465c234552eeab2fe633
  languageName: node
  linkType: hard

"package-json-from-dist@npm:^1.0.0":
  version: 1.0.1
  resolution: "package-json-from-dist@npm:1.0.1"
  checksum: 10/58ee9538f2f762988433da00e26acc788036914d57c71c246bf0be1b60cdbd77dd60b6a3e1a30465f0b248aeb80079e0b34cb6050b1dfa18c06953bb1cbc7602
  languageName: node
  linkType: hard

"parent-module@npm:^1.0.0":
  version: 1.0.1
  resolution: "parent-module@npm:1.0.1"
  dependencies:
    callsites: "npm:^3.0.0"
  checksum: 10/6ba8b255145cae9470cf5551eb74be2d22281587af787a2626683a6c20fbb464978784661478dd2a3f1dad74d1e802d403e1b03c1a31fab310259eec8ac560ff
  languageName: node
  linkType: hard

"parse-json@npm:^5.2.0":
  version: 5.2.0
  resolution: "parse-json@npm:5.2.0"
  dependencies:
    "@babel/code-frame": "npm:^7.0.0"
    error-ex: "npm:^1.3.1"
    json-parse-even-better-errors: "npm:^2.3.0"
    lines-and-columns: "npm:^1.1.6"
  checksum: 10/62085b17d64da57f40f6afc2ac1f4d95def18c4323577e1eced571db75d9ab59b297d1d10582920f84b15985cbfc6b6d450ccbf317644cfa176f3ed982ad87e2
  languageName: node
  linkType: hard

"parse5@npm:^7.2.1":
  version: 7.3.0
  resolution: "parse5@npm:7.3.0"
  dependencies:
    entities: "npm:^6.0.0"
  checksum: 10/b0e48be20b820c655b138b86fa6fb3a790de6c891aa2aba536524f8027b4dca4fe538f11a0e5cf2f6f847d120dbb9e4822dcaeb933ff1e10850a2ef0154d1d88
  languageName: node
  linkType: hard

"parseurl@npm:~1.3.3":
  version: 1.3.3
  resolution: "parseurl@npm:1.3.3"
  checksum: 10/407cee8e0a3a4c5cd472559bca8b6a45b82c124e9a4703302326e9ab60fc1081442ada4e02628efef1eb16197ddc7f8822f5a91fd7d7c86b51f530aedb17dfa2
  languageName: node
  linkType: hard

"path-exists@npm:^4.0.0":
  version: 4.0.0
  resolution: "path-exists@npm:4.0.0"
  checksum: 10/505807199dfb7c50737b057dd8d351b82c033029ab94cb10a657609e00c1bc53b951cfdbccab8de04c5584d5eff31128ce6afd3db79281874a5ef2adbba55ed1
  languageName: node
  linkType: hard

"path-key@npm:^3.1.0":
  version: 3.1.1
  resolution: "path-key@npm:3.1.1"
  checksum: 10/55cd7a9dd4b343412a8386a743f9c746ef196e57c823d90ca3ab917f90ab9f13dd0ded27252ba49dbdfcab2b091d998bc446f6220cd3cea65db407502a740020
  languageName: node
  linkType: hard

"path-key@npm:^4.0.0":
  version: 4.0.0
  resolution: "path-key@npm:4.0.0"
  checksum: 10/8e6c314ae6d16b83e93032c61020129f6f4484590a777eed709c4a01b50e498822b00f76ceaf94bc64dbd90b327df56ceadce27da3d83393790f1219e07721d7
  languageName: node
  linkType: hard

"path-parse@npm:^1.0.7":
  version: 1.0.7
  resolution: "path-parse@npm:1.0.7"
  checksum: 10/49abf3d81115642938a8700ec580da6e830dde670be21893c62f4e10bd7dd4c3742ddc603fe24f898cba7eb0c6bc1777f8d9ac14185d34540c6d4d80cd9cae8a
  languageName: node
  linkType: hard

"path-scurry@npm:^1.11.1":
  version: 1.11.1
  resolution: "path-scurry@npm:1.11.1"
  dependencies:
    lru-cache: "npm:^10.2.0"
    minipass: "npm:^5.0.0 || ^6.0.2 || ^7.0.0"
  checksum: 10/5e8845c159261adda6f09814d7725683257fcc85a18f329880ab4d7cc1d12830967eae5d5894e453f341710d5484b8fdbbd4d75181b4d6e1eb2f4dc7aeadc434
  languageName: node
  linkType: hard

"path-to-regexp@npm:0.1.12":
  version: 0.1.12
  resolution: "path-to-regexp@npm:0.1.12"
  checksum: 10/2e30f6a0144679c1f95c98e166b96e6acd1e72be9417830fefc8de7ac1992147eb9a4c7acaa59119fb1b3c34eec393b2129ef27e24b2054a3906fc4fb0d1398e
  languageName: node
  linkType: hard

"path-to-regexp@npm:^6.2.1":
  version: 6.3.0
  resolution: "path-to-regexp@npm:6.3.0"
  checksum: 10/6822f686f01556d99538b350722ef761541ec0ce95ca40ce4c29e20a5b492fe8361961f57993c71b2418de12e604478dcf7c430de34b2c31a688363a7a944d9c
  languageName: node
  linkType: hard

"path-type@npm:^4.0.0":
  version: 4.0.0
  resolution: "path-type@npm:4.0.0"
  checksum: 10/5b1e2daa247062061325b8fdbfd1fb56dde0a448fb1455453276ea18c60685bdad23a445dc148cf87bc216be1573357509b7d4060494a6fd768c7efad833ee45
  languageName: node
  linkType: hard

"pathe@npm:^2.0.3":
  version: 2.0.3
  resolution: "pathe@npm:2.0.3"
  checksum: 10/01e9a69928f39087d96e1751ce7d6d50da8c39abf9a12e0ac2389c42c83bc76f78c45a475bd9026a02e6a6f79be63acc75667df855862fe567d99a00a540d23d
  languageName: node
  linkType: hard

"pathval@npm:^2.0.0":
  version: 2.0.1
  resolution: "pathval@npm:2.0.1"
  checksum: 10/f5e8b82f6b988a5bba197970af050268fd800780d0f9ee026e6f0b544ac4b17ab52bebeabccb790d63a794530a1641ae399ad07ecfc67ad337504c85dc9e5693
  languageName: node
  linkType: hard

"performance-now@npm:^2.1.0":
  version: 2.1.0
  resolution: "performance-now@npm:2.1.0"
  checksum: 10/534e641aa8f7cba160f0afec0599b6cecefbb516a2e837b512be0adbe6c1da5550e89c78059c7fabc5c9ffdf6627edabe23eb7c518c4500067a898fa65c2b550
  languageName: node
  linkType: hard

"picocolors@npm:^1.0.0, picocolors@npm:^1.1.1":
  version: 1.1.1
  resolution: "picocolors@npm:1.1.1"
  checksum: 10/e1cf46bf84886c79055fdfa9dcb3e4711ad259949e3565154b004b260cd356c5d54b31a1437ce9782624bf766272fe6b0154f5f0c744fb7af5d454d2b60db045
  languageName: node
  linkType: hard

"picomatch@npm:^2.0.4, picomatch@npm:^2.2.1, picomatch@npm:^2.3.1":
  version: 2.3.1
  resolution: "picomatch@npm:2.3.1"
  checksum: 10/60c2595003b05e4535394d1da94850f5372c9427ca4413b71210f437f7b2ca091dbd611c45e8b37d10036fa8eade25c1b8951654f9d3973bfa66a2ff4d3b08bc
  languageName: node
  linkType: hard

"picomatch@npm:^4.0.2, picomatch@npm:^4.0.3":
  version: 4.0.3
  resolution: "picomatch@npm:4.0.3"
  checksum: 10/57b99055f40b16798f2802916d9c17e9744e620a0db136554af01d19598b96e45e2f00014c91d1b8b13874b80caa8c295b3d589a3f72373ec4aaf54baa5962d5
  languageName: node
  linkType: hard

"possible-typed-array-names@npm:^1.0.0":
  version: 1.1.0
  resolution: "possible-typed-array-names@npm:1.1.0"
  checksum: 10/2f44137b8d3dd35f4a7ba7469eec1cd9cfbb46ec164b93a5bc1f4c3d68599c9910ee3b91da1d28b4560e9cc8414c3cd56fedc07259c67e52cc774476270d3302
  languageName: node
  linkType: hard

"postcss-value-parser@npm:^4.0.2":
  version: 4.2.0
  resolution: "postcss-value-parser@npm:4.2.0"
  checksum: 10/e4e4486f33b3163a606a6ed94f9c196ab49a37a7a7163abfcd469e5f113210120d70b8dd5e33d64636f41ad52316a3725655421eb9a1094f1bcab1db2f555c62
  languageName: node
  linkType: hard

"postcss@npm:8.4.49":
  version: 8.4.49
  resolution: "postcss@npm:8.4.49"
  dependencies:
    nanoid: "npm:^3.3.7"
    picocolors: "npm:^1.1.1"
    source-map-js: "npm:^1.2.1"
  checksum: 10/28fe1005b1339870e0a5006375ba5ac1213fd69800f79e7db09c398e074421ba6e162898e94f64942fed554037fd292db3811d87835d25ab5ef7f3c9daacb6ca
  languageName: node
  linkType: hard

"postcss@npm:8.5.3":
  version: 8.5.3
  resolution: "postcss@npm:8.5.3"
  dependencies:
    nanoid: "npm:^3.3.8"
    picocolors: "npm:^1.1.1"
    source-map-js: "npm:^1.2.1"
  checksum: 10/6d7e21a772e8b05bf102636918654dac097bac013f0dc8346b72ac3604fc16829646f94ea862acccd8f82e910b00e2c11c1f0ea276543565d278c7ca35516a7c
  languageName: node
  linkType: hard

"postcss@npm:^8.4.18, postcss@npm:^8.5.6":
  version: 8.5.6
  resolution: "postcss@npm:8.5.6"
  dependencies:
    nanoid: "npm:^3.3.11"
    picocolors: "npm:^1.1.1"
    source-map-js: "npm:^1.2.1"
  checksum: 10/9e4fbe97574091e9736d0e82a591e29aa100a0bf60276a926308f8c57249698935f35c5d2f4e80de778d0cbb8dcffab4f383d85fd50c5649aca421c3df729b86
  languageName: node
  linkType: hard

"prelude-ls@npm:^1.2.1":
  version: 1.2.1
  resolution: "prelude-ls@npm:1.2.1"
  checksum: 10/0b9d2c76801ca652a7f64892dd37b7e3fab149a37d2424920099bf894acccc62abb4424af2155ab36dea8744843060a2d8ddc983518d0b1e22265a22324b72ed
  languageName: node
  linkType: hard

"prettier-linter-helpers@npm:^1.0.0":
  version: 1.0.0
  resolution: "prettier-linter-helpers@npm:1.0.0"
  dependencies:
    fast-diff: "npm:^1.1.2"
  checksum: 10/00ce8011cf6430158d27f9c92cfea0a7699405633f7f1d4a45f07e21bf78e99895911cbcdc3853db3a824201a7c745bd49bfea8abd5fb9883e765a90f74f8392
  languageName: node
  linkType: hard

"prettier@npm:3.5.3":
  version: 3.5.3
  resolution: "prettier@npm:3.5.3"
  bin:
    prettier: bin/prettier.cjs
  checksum: 10/7050c08f674d9e49fbd9a4c008291d0715471f64e94cc5e4b01729affce221dfc6875c8de7e66b728c64abc9352eefb7eaae071b5f79d30081be207b53774b78
  languageName: node
  linkType: hard

"prettier@npm:^3.5.0":
  version: 3.6.2
  resolution: "prettier@npm:3.6.2"
  bin:
    prettier: bin/prettier.cjs
  checksum: 10/1213691706bcef1371d16ef72773c8111106c3533b660b1cc8ec158bd109cdf1462804125f87f981f23c4a3dba053b6efafda30ab0114cc5b4a725606bb9ff26
  languageName: node
  linkType: hard

"pretty-format@npm:^27.0.2":
  version: 27.5.1
  resolution: "pretty-format@npm:27.5.1"
  dependencies:
    ansi-regex: "npm:^5.0.1"
    ansi-styles: "npm:^5.0.0"
    react-is: "npm:^17.0.1"
  checksum: 10/248990cbef9e96fb36a3e1ae6b903c551ca4ddd733f8d0912b9cc5141d3d0b3f9f8dfb4d799fb1c6723382c9c2083ffbfa4ad43ff9a0e7535d32d41fd5f01da6
  languageName: node
  linkType: hard

"proc-log@npm:^5.0.0":
  version: 5.0.0
  resolution: "proc-log@npm:5.0.0"
  checksum: 10/35610bdb0177d3ab5d35f8827a429fb1dc2518d9e639f2151ac9007f01a061c30e0c635a970c9b00c39102216160f6ec54b62377c92fac3b7bfc2ad4b98d195c
  languageName: node
  linkType: hard

"promise-retry@npm:^2.0.1":
  version: 2.0.1
  resolution: "promise-retry@npm:2.0.1"
  dependencies:
    err-code: "npm:^2.0.2"
    retry: "npm:^0.12.0"
  checksum: 10/96e1a82453c6c96eef53a37a1d6134c9f2482f94068f98a59145d0986ca4e497bf110a410adf73857e588165eab3899f0ebcf7b3890c1b3ce802abc0d65967d4
  languageName: node
  linkType: hard

"prop-types@npm:^15.8.1":
  version: 15.8.1
  resolution: "prop-types@npm:15.8.1"
  dependencies:
    loose-envify: "npm:^1.4.0"
    object-assign: "npm:^4.1.1"
    react-is: "npm:^16.13.1"
  checksum: 10/7d959caec002bc964c86cdc461ec93108b27337dabe6192fb97d69e16a0c799a03462713868b40749bfc1caf5f57ef80ac3e4ffad3effa636ee667582a75e2c0
  languageName: node
  linkType: hard

"proxy-addr@npm:~2.0.7":
  version: 2.0.7
  resolution: "proxy-addr@npm:2.0.7"
  dependencies:
    forwarded: "npm:0.2.0"
    ipaddr.js: "npm:1.9.1"
  checksum: 10/f24a0c80af0e75d31e3451398670d73406ec642914da11a2965b80b1898ca6f66a0e3e091a11a4327079b2b268795f6fa06691923fef91887215c3d0e8ea3f68
  languageName: node
  linkType: hard

"proxy-from-env@npm:^1.1.0":
  version: 1.1.0
  resolution: "proxy-from-env@npm:1.1.0"
  checksum: 10/f0bb4a87cfd18f77bc2fba23ae49c3b378fb35143af16cc478171c623eebe181678f09439707ad80081d340d1593cd54a33a0113f3ccb3f4bc9451488780ee23
  languageName: node
  linkType: hard

"punycode@npm:^2.1.0, punycode@npm:^2.3.1":
  version: 2.3.1
  resolution: "punycode@npm:2.3.1"
  checksum: 10/febdc4362bead22f9e2608ff0171713230b57aff9dddc1c273aa2a651fbd366f94b7d6a71d78342a7c0819906750351ca7f2edd26ea41b626d87d6a13d1bd059
  languageName: node
  linkType: hard

"qs@npm:6.13.0":
  version: 6.13.0
  resolution: "qs@npm:6.13.0"
  dependencies:
    side-channel: "npm:^1.0.6"
  checksum: 10/f548b376e685553d12e461409f0d6e5c59ec7c7d76f308e2a888fd9db3e0c5e89902bedd0754db3a9038eda5f27da2331a6f019c8517dc5e0a16b3c9a6e9cef8
  languageName: node
  linkType: hard

"queue-microtask@npm:^1.2.2":
  version: 1.2.3
  resolution: "queue-microtask@npm:1.2.3"
  checksum: 10/72900df0616e473e824202113c3df6abae59150dfb73ed13273503127235320e9c8ca4aaaaccfd58cf417c6ca92a6e68ee9a5c3182886ae949a768639b388a7b
  languageName: node
  linkType: hard

"raf@npm:3.4.1":
  version: 3.4.1
  resolution: "raf@npm:3.4.1"
  dependencies:
    performance-now: "npm:^2.1.0"
  checksum: 10/4c4b4c826b09d2aec6ca809f1a3c3c12136e7ec8d13fbb91f495dd2c99cd43345240e003da3bfd16036a432e635049fc6d9f69f9187f5f22ea88bb146ec75881
  languageName: node
  linkType: hard

"range-parser@npm:~1.2.1":
  version: 1.2.1
  resolution: "range-parser@npm:1.2.1"
  checksum: 10/ce21ef2a2dd40506893157970dc76e835c78cf56437e26e19189c48d5291e7279314477b06ac38abd6a401b661a6840f7b03bd0b1249da9b691deeaa15872c26
  languageName: node
  linkType: hard

"raw-body@npm:2.5.2":
  version: 2.5.2
  resolution: "raw-body@npm:2.5.2"
  dependencies:
    bytes: "npm:3.1.2"
    http-errors: "npm:2.0.0"
    iconv-lite: "npm:0.4.24"
    unpipe: "npm:1.0.0"
  checksum: 10/863b5171e140546a4d99f349b720abac4410338e23df5e409cfcc3752538c9caf947ce382c89129ba976f71894bd38b5806c774edac35ebf168d02aa1ac11a95
  languageName: node
  linkType: hard

"react-dom@npm:19.1.1":
  version: 19.1.1
  resolution: "react-dom@npm:19.1.1"
  dependencies:
    scheduler: "npm:^0.26.0"
  peerDependencies:
    react: ^19.1.1
  checksum: 10/9005415d2175b1f1eb4a544ad04afb29691bb7b6dd43bbdaa09932146b310b73bd4552bc772ad78fa481f409eada1560cf887606c83c1a53a922c1e30f1b3a34
  languageName: node
  linkType: hard

"react-is@npm:*":
  version: 19.1.1
  resolution: "react-is@npm:19.1.1"
  checksum: 10/44e0937da1f0da1d5dbd4f01972870768ef207f8a49717f4491f5022454f34c956cb66be560aee5286387169853b0283a81e1f419b51dc62654c8710dc98065a
  languageName: node
  linkType: hard

"react-is@npm:^16.13.1":
  version: 16.13.1
  resolution: "react-is@npm:16.13.1"
  checksum: 10/5aa564a1cde7d391ac980bedee21202fc90bdea3b399952117f54fb71a932af1e5902020144fb354b4690b2414a0c7aafe798eb617b76a3d441d956db7726fdf
  languageName: node
  linkType: hard

"react-is@npm:^17.0.1":
  version: 17.0.2
  resolution: "react-is@npm:17.0.2"
  checksum: 10/73b36281e58eeb27c9cc6031301b6ae19ecdc9f18ae2d518bdb39b0ac564e65c5779405d623f1df9abf378a13858b79442480244bd579968afc1faf9a2ce5e05
  languageName: node
  linkType: hard

"react@npm:19.1.1":
  version: 19.1.1
  resolution: "react@npm:19.1.1"
  checksum: 10/9801530fdc939e1a7a499422e930515b2400809cb39c2872984e99f832d233f61659a693871183dac3155c2f9b2c9dcf4440a56bd18983277ae92860e38c3a61
  languageName: node
  linkType: hard

"readdirp@npm:^4.0.1":
  version: 4.1.2
  resolution: "readdirp@npm:4.1.2"
  checksum: 10/7b817c265940dba90bb9c94d82920d76c3a35ea2d67f9f9d8bd936adcfe02d50c802b14be3dd2e725e002dddbe2cc1c7a0edfb1bc3a365c9dfd5a61e612eea1e
  languageName: node
  linkType: hard

"readdirp@npm:~3.6.0":
  version: 3.6.0
  resolution: "readdirp@npm:3.6.0"
  dependencies:
    picomatch: "npm:^2.2.1"
  checksum: 10/196b30ef6ccf9b6e18c4e1724b7334f72a093d011a99f3b5920470f0b3406a51770867b3e1ae9711f227ef7a7065982f6ee2ce316746b2cb42c88efe44297fe7
  languageName: node
  linkType: hard

"recast@npm:^0.23.11":
  version: 0.23.11
  resolution: "recast@npm:0.23.11"
  dependencies:
    ast-types: "npm:^0.16.1"
    esprima: "npm:~4.0.0"
    source-map: "npm:~0.6.1"
    tiny-invariant: "npm:^1.3.3"
    tslib: "npm:^2.0.1"
  checksum: 10/a622b7848efe13a59a40c9a1a3a8178433eae1048780e04d7392406e2d67fc29e3efa84b3aa8cfda28fd58989f4b59fa968bed295b739987a666bd11cc57a5b2
  languageName: node
  linkType: hard

"redent@npm:^3.0.0":
  version: 3.0.0
  resolution: "redent@npm:3.0.0"
  dependencies:
    indent-string: "npm:^4.0.0"
    strip-indent: "npm:^3.0.0"
  checksum: 10/fa1ef20404a2d399235e83cc80bd55a956642e37dd197b4b612ba7327bf87fa32745aeb4a1634b2bab25467164ab4ed9c15be2c307923dd08b0fe7c52431ae6b
  languageName: node
  linkType: hard

"refa@npm:^0.12.0, refa@npm:^0.12.1":
  version: 0.12.1
  resolution: "refa@npm:0.12.1"
  dependencies:
    "@eslint-community/regexpp": "npm:^4.8.0"
  checksum: 10/b89411434e31637a519c065acd8fd1ec9eabc1dec38eec58dbc69a386ec21d88f97fa175e56fb3133e21c090ddb68fe7b5653ffc4bbcc9f069abc0e88c0d290c
  languageName: node
  linkType: hard

"reflect.getprototypeof@npm:^1.0.6, reflect.getprototypeof@npm:^1.0.9":
  version: 1.0.10
  resolution: "reflect.getprototypeof@npm:1.0.10"
  dependencies:
    call-bind: "npm:^1.0.8"
    define-properties: "npm:^1.2.1"
    es-abstract: "npm:^1.23.9"
    es-errors: "npm:^1.3.0"
    es-object-atoms: "npm:^1.0.0"
    get-intrinsic: "npm:^1.2.7"
    get-proto: "npm:^1.0.1"
    which-builtin-type: "npm:^1.2.1"
  checksum: 10/80a4e2be716f4fe46a89a08ccad0863b47e8ce0f49616cab2d65dab0fbd53c6fdba0f52935fd41d37a2e4e22355c272004f920d63070de849f66eea7aeb4a081
  languageName: node
  linkType: hard

"regenerate-unicode-properties@npm:^10.2.0":
  version: 10.2.0
  resolution: "regenerate-unicode-properties@npm:10.2.0"
  dependencies:
    regenerate: "npm:^1.4.2"
  checksum: 10/9150eae6fe04a8c4f2ff06077396a86a98e224c8afad8344b1b656448e89e84edcd527e4b03aa5476774129eb6ad328ed684f9c1459794a935ec0cc17ce14329
  languageName: node
  linkType: hard

"regenerate@npm:^1.4.2":
  version: 1.4.2
  resolution: "regenerate@npm:1.4.2"
  checksum: 10/dc6c95ae4b3ba6adbd7687cafac260eee4640318c7a95239d5ce847d9b9263979758389e862fe9c93d633b5792ea4ada5708df75885dc5aa05a309fa18140a87
  languageName: node
  linkType: hard

"regenerator-runtime@npm:0.14.1":
  version: 0.14.1
  resolution: "regenerator-runtime@npm:0.14.1"
  checksum: 10/5db3161abb311eef8c45bcf6565f4f378f785900ed3945acf740a9888c792f75b98ecb77f0775f3bf95502ff423529d23e94f41d80c8256e8fa05ed4b07cf471
  languageName: node
  linkType: hard

"regexp-ast-analysis@npm:^0.7.0":
  version: 0.7.1
  resolution: "regexp-ast-analysis@npm:0.7.1"
  dependencies:
    "@eslint-community/regexpp": "npm:^4.8.0"
    refa: "npm:^0.12.1"
  checksum: 10/92299636d9c941ee27db7568a775354d36024504c104c5d7981a89dda1b0ff1e2a56db16f92d7e166a50a1164593788c0849c5840ec9d79b39c1c040d59c442c
  languageName: node
  linkType: hard

"regexp-tree@npm:^0.1.21":
  version: 0.1.27
  resolution: "regexp-tree@npm:0.1.27"
  bin:
    regexp-tree: bin/regexp-tree
  checksum: 10/08c70c8adb5a0d4af1061bf9eb05d3b6e1d948c433d6b7008e4b5eb12a49429c2d6ca8e9106339a432aa0d07bd6e1bccc638d8f4ab0d045f3adad22182b300a2
  languageName: node
  linkType: hard

"regexp.prototype.flags@npm:^1.5.1, regexp.prototype.flags@npm:^1.5.3, regexp.prototype.flags@npm:^1.5.4":
  version: 1.5.4
  resolution: "regexp.prototype.flags@npm:1.5.4"
  dependencies:
    call-bind: "npm:^1.0.8"
    define-properties: "npm:^1.2.1"
    es-errors: "npm:^1.3.0"
    get-proto: "npm:^1.0.1"
    gopd: "npm:^1.2.0"
    set-function-name: "npm:^2.0.2"
  checksum: 10/8ab897ca445968e0b96f6237641510f3243e59c180ee2ee8d83889c52ff735dd1bf3657fcd36db053e35e1d823dd53f2565d0b8021ea282c9fe62401c6c3bd6d
  languageName: node
  linkType: hard

"regexpu-core@npm:^6.2.0":
  version: 6.2.0
  resolution: "regexpu-core@npm:6.2.0"
  dependencies:
    regenerate: "npm:^1.4.2"
    regenerate-unicode-properties: "npm:^10.2.0"
    regjsgen: "npm:^0.8.0"
    regjsparser: "npm:^0.12.0"
    unicode-match-property-ecmascript: "npm:^2.0.0"
    unicode-match-property-value-ecmascript: "npm:^2.1.0"
  checksum: 10/4d054ffcd98ca4f6ca7bf0df6598ed5e4a124264602553308add41d4fa714a0c5bcfb5bc868ac91f7060a9c09889cc21d3180a3a14c5f9c5838442806129ced3
  languageName: node
  linkType: hard

"regjsgen@npm:^0.8.0":
  version: 0.8.0
  resolution: "regjsgen@npm:0.8.0"
  checksum: 10/b930f03347e4123c917d7b40436b4f87f625b8dd3e705b447ddd44804e4616c3addb7453f0902d6e914ab0446c30e816e445089bb641a4714237fe8141a0ef9d
  languageName: node
  linkType: hard

"regjsparser@npm:^0.12.0":
  version: 0.12.0
  resolution: "regjsparser@npm:0.12.0"
  dependencies:
    jsesc: "npm:~3.0.2"
  bin:
    regjsparser: bin/parser
  checksum: 10/c2d6506b3308679de5223a8916984198e0493649a67b477c66bdb875357e3785abbf3bedf7c5c2cf8967d3b3a7bdf08b7cbd39e65a70f9e1ffad584aecf5f06a
  languageName: node
  linkType: hard

"requireindex@npm:~1.1.0":
  version: 1.1.0
  resolution: "requireindex@npm:1.1.0"
  checksum: 10/97c6f7a8a5981f3ae38ca36ec998c0e8ff33a15a5769ce18ca9db5df2ffb6558ddc7d9983777e20c07af83094b2923a02a0019aeded90b6f73619f99bf52ede0
  languageName: node
  linkType: hard

"resolve-from@npm:^4.0.0":
  version: 4.0.0
  resolution: "resolve-from@npm:4.0.0"
  checksum: 10/91eb76ce83621eea7bbdd9b55121a5c1c4a39e54a9ce04a9ad4517f102f8b5131c2cf07622c738a6683991bf54f2ce178f5a42803ecbd527ddc5105f362cc9e3
  languageName: node
  linkType: hard

"resolve-pkg-maps@npm:^1.0.0":
  version: 1.0.0
  resolution: "resolve-pkg-maps@npm:1.0.0"
  checksum: 10/0763150adf303040c304009231314d1e84c6e5ebfa2d82b7d94e96a6e82bacd1dcc0b58ae257315f3c8adb89a91d8d0f12928241cba2df1680fbe6f60bf99b0e
  languageName: node
  linkType: hard

"resolve@npm:^1.22.1, resolve@npm:^1.22.10, resolve@npm:^1.22.4":
  version: 1.22.10
  resolution: "resolve@npm:1.22.10"
  dependencies:
    is-core-module: "npm:^2.16.0"
    path-parse: "npm:^1.0.7"
    supports-preserve-symlinks-flag: "npm:^1.0.0"
  bin:
    resolve: bin/resolve
  checksum: 10/0a398b44da5c05e6e421d70108822c327675febb880eebe905587628de401854c61d5df02866ff34fc4cb1173a51c9f0e84a94702738df3611a62e2acdc68181
  languageName: node
  linkType: hard

"resolve@npm:^2.0.0-next.5":
  version: 2.0.0-next.5
  resolution: "resolve@npm:2.0.0-next.5"
  dependencies:
    is-core-module: "npm:^2.13.0"
    path-parse: "npm:^1.0.7"
    supports-preserve-symlinks-flag: "npm:^1.0.0"
  bin:
    resolve: bin/resolve
  checksum: 10/2d6fd28699f901744368e6f2032b4268b4c7b9185fd8beb64f68c93ac6b22e52ae13560ceefc96241a665b985edf9ffd393ae26d2946a7d3a07b7007b7d51e79
  languageName: node
  linkType: hard

"resolve@patch:resolve@npm%3A^1.22.1#optional!builtin<compat/resolve>, resolve@patch:resolve@npm%3A^1.22.10#optional!builtin<compat/resolve>, resolve@patch:resolve@npm%3A^1.22.4#optional!builtin<compat/resolve>":
  version: 1.22.10
  resolution: "resolve@patch:resolve@npm%3A1.22.10#optional!builtin<compat/resolve>::version=1.22.10&hash=c3c19d"
  dependencies:
    is-core-module: "npm:^2.16.0"
    path-parse: "npm:^1.0.7"
    supports-preserve-symlinks-flag: "npm:^1.0.0"
  bin:
    resolve: bin/resolve
  checksum: 10/d4d878bfe3702d215ea23e75e0e9caf99468e3db76f5ca100d27ebdc527366fee3877e54bce7d47cc72ca8952fc2782a070d238bfa79a550eeb0082384c3b81a
  languageName: node
  linkType: hard

"resolve@patch:resolve@npm%3A^2.0.0-next.5#optional!builtin<compat/resolve>":
  version: 2.0.0-next.5
  resolution: "resolve@patch:resolve@npm%3A2.0.0-next.5#optional!builtin<compat/resolve>::version=2.0.0-next.5&hash=c3c19d"
  dependencies:
    is-core-module: "npm:^2.13.0"
    path-parse: "npm:^1.0.7"
    supports-preserve-symlinks-flag: "npm:^1.0.0"
  bin:
    resolve: bin/resolve
  checksum: 10/05fa778de9d0347c8b889eb7a18f1f06bf0f801b0eb4610b4871a4b2f22e220900cf0ad525e94f990bb8d8921c07754ab2122c0c225ab4cdcea98f36e64fa4c2
  languageName: node
  linkType: hard

"retry@npm:^0.12.0":
  version: 0.12.0
  resolution: "retry@npm:0.12.0"
  checksum: 10/1f914879f97e7ee931ad05fe3afa629bd55270fc6cf1c1e589b6a99fab96d15daad0fa1a52a00c729ec0078045fe3e399bd4fd0c93bcc906957bdc17f89cb8e6
  languageName: node
  linkType: hard

"reusify@npm:^1.0.4":
  version: 1.1.0
  resolution: "reusify@npm:1.1.0"
  checksum: 10/af47851b547e8a8dc89af144fceee17b80d5beaf5e6f57ed086432d79943434ff67ca526e92275be6f54b6189f6920a24eace75c2657eed32d02c400312b21ec
  languageName: node
  linkType: hard

"rollup@npm:^2.79.1":
  version: 2.79.2
  resolution: "rollup@npm:2.79.2"
  dependencies:
    fsevents: "npm:~2.3.2"
  dependenciesMeta:
    fsevents:
      optional: true
  bin:
    rollup: dist/bin/rollup
  checksum: 10/095ba0a82811b1866a76d826987743278db0a87c45092656986bfff490326b66187d5f9ff0c24cf8d5682bc470aa00c36654e0044d6b6335ac0c1201b8280880
  languageName: node
  linkType: hard

"rollup@npm:^4.43.0":
  version: 4.50.0
  resolution: "rollup@npm:4.50.0"
  dependencies:
    "@rollup/rollup-android-arm-eabi": "npm:4.50.0"
    "@rollup/rollup-android-arm64": "npm:4.50.0"
    "@rollup/rollup-darwin-arm64": "npm:4.50.0"
    "@rollup/rollup-darwin-x64": "npm:4.50.0"
    "@rollup/rollup-freebsd-arm64": "npm:4.50.0"
    "@rollup/rollup-freebsd-x64": "npm:4.50.0"
    "@rollup/rollup-linux-arm-gnueabihf": "npm:4.50.0"
    "@rollup/rollup-linux-arm-musleabihf": "npm:4.50.0"
    "@rollup/rollup-linux-arm64-gnu": "npm:4.50.0"
    "@rollup/rollup-linux-arm64-musl": "npm:4.50.0"
    "@rollup/rollup-linux-loongarch64-gnu": "npm:4.50.0"
    "@rollup/rollup-linux-ppc64-gnu": "npm:4.50.0"
    "@rollup/rollup-linux-riscv64-gnu": "npm:4.50.0"
    "@rollup/rollup-linux-riscv64-musl": "npm:4.50.0"
    "@rollup/rollup-linux-s390x-gnu": "npm:4.50.0"
    "@rollup/rollup-linux-x64-gnu": "npm:4.50.0"
    "@rollup/rollup-linux-x64-musl": "npm:4.50.0"
    "@rollup/rollup-openharmony-arm64": "npm:4.50.0"
    "@rollup/rollup-win32-arm64-msvc": "npm:4.50.0"
    "@rollup/rollup-win32-ia32-msvc": "npm:4.50.0"
    "@rollup/rollup-win32-x64-msvc": "npm:4.50.0"
    "@types/estree": "npm:1.0.8"
    fsevents: "npm:~2.3.2"
  dependenciesMeta:
    "@rollup/rollup-android-arm-eabi":
      optional: true
    "@rollup/rollup-android-arm64":
      optional: true
    "@rollup/rollup-darwin-arm64":
      optional: true
    "@rollup/rollup-darwin-x64":
      optional: true
    "@rollup/rollup-freebsd-arm64":
      optional: true
    "@rollup/rollup-freebsd-x64":
      optional: true
    "@rollup/rollup-linux-arm-gnueabihf":
      optional: true
    "@rollup/rollup-linux-arm-musleabihf":
      optional: true
    "@rollup/rollup-linux-arm64-gnu":
      optional: true
    "@rollup/rollup-linux-arm64-musl":
      optional: true
    "@rollup/rollup-linux-loongarch64-gnu":
      optional: true
    "@rollup/rollup-linux-ppc64-gnu":
      optional: true
    "@rollup/rollup-linux-riscv64-gnu":
      optional: true
    "@rollup/rollup-linux-riscv64-musl":
      optional: true
    "@rollup/rollup-linux-s390x-gnu":
      optional: true
    "@rollup/rollup-linux-x64-gnu":
      optional: true
    "@rollup/rollup-linux-x64-musl":
      optional: true
    "@rollup/rollup-openharmony-arm64":
      optional: true
    "@rollup/rollup-win32-arm64-msvc":
      optional: true
    "@rollup/rollup-win32-ia32-msvc":
      optional: true
    "@rollup/rollup-win32-x64-msvc":
      optional: true
    fsevents:
      optional: true
  bin:
    rollup: dist/bin/rollup
  checksum: 10/76561d1cfa786c3ea308b8cae94da40486e96c050d3c966337062eaf8260a779329df50df00cb02cda4d71fc9c948c9adf2ec6aa3f399901f5d56ae3f2ed31d3
  languageName: node
  linkType: hard

"rrweb-cssom@npm:^0.8.0":
  version: 0.8.0
  resolution: "rrweb-cssom@npm:0.8.0"
  checksum: 10/07521ee36fb6569c17906afad1ac7ff8f099d49ade9249e190693ac36cdf27f88d9acf0cc66978935d5d0a23fca105643d7e9125b9a9d91ed9db9e02d31d7d80
  languageName: node
  linkType: hard

"run-parallel@npm:^1.1.9":
  version: 1.2.0
  resolution: "run-parallel@npm:1.2.0"
  dependencies:
    queue-microtask: "npm:^1.2.2"
  checksum: 10/cb4f97ad25a75ebc11a8ef4e33bb962f8af8516bb2001082ceabd8902e15b98f4b84b4f8a9b222e5d57fc3bd1379c483886ed4619367a7680dad65316993021d
  languageName: node
  linkType: hard

"safe-array-concat@npm:^1.1.3":
  version: 1.1.3
  resolution: "safe-array-concat@npm:1.1.3"
  dependencies:
    call-bind: "npm:^1.0.8"
    call-bound: "npm:^1.0.2"
    get-intrinsic: "npm:^1.2.6"
    has-symbols: "npm:^1.1.0"
    isarray: "npm:^2.0.5"
  checksum: 10/fac4f40f20a3f7da024b54792fcc61059e814566dcbb04586bfefef4d3b942b2408933f25b7b3dd024affd3f2a6bbc916bef04807855e4f192413941369db864
  languageName: node
  linkType: hard

"safe-buffer@npm:5.2.1":
  version: 5.2.1
  resolution: "safe-buffer@npm:5.2.1"
  checksum: 10/32872cd0ff68a3ddade7a7617b8f4c2ae8764d8b7d884c651b74457967a9e0e886267d3ecc781220629c44a865167b61c375d2da6c720c840ecd73f45d5d9451
  languageName: node
  linkType: hard

"safe-push-apply@npm:^1.0.0":
  version: 1.0.0
  resolution: "safe-push-apply@npm:1.0.0"
  dependencies:
    es-errors: "npm:^1.3.0"
    isarray: "npm:^2.0.5"
  checksum: 10/2bd4e53b6694f7134b9cf93631480e7fafc8637165f0ee91d5a4af5e7f33d37de9562d1af5021178dd4217d0230cde8d6530fa28cfa1ebff9a431bf8fff124b4
  languageName: node
  linkType: hard

"safe-regex-test@npm:^1.0.3, safe-regex-test@npm:^1.1.0":
  version: 1.1.0
  resolution: "safe-regex-test@npm:1.1.0"
  dependencies:
    call-bound: "npm:^1.0.2"
    es-errors: "npm:^1.3.0"
    is-regex: "npm:^1.2.1"
  checksum: 10/ebdb61f305bf4756a5b023ad86067df5a11b26898573afe9e52a548a63c3bd594825d9b0e2dde2eb3c94e57e0e04ac9929d4107c394f7b8e56a4613bed46c69a
  languageName: node
  linkType: hard

"safer-buffer@npm:>= 2.1.2 < 3, safer-buffer@npm:>= 2.1.2 < 3.0.0":
  version: 2.1.2
  resolution: "safer-buffer@npm:2.1.2"
  checksum: 10/7eaf7a0cf37cc27b42fb3ef6a9b1df6e93a1c6d98c6c6702b02fe262d5fcbd89db63320793b99b21cb5348097d0a53de81bd5f4e8b86e20cc9412e3f1cfb4e83
  languageName: node
  linkType: hard

"saxes@npm:^6.0.0":
  version: 6.0.0
  resolution: "saxes@npm:6.0.0"
  dependencies:
    xmlchars: "npm:^2.2.0"
  checksum: 10/97b50daf6ca3a153e89842efa18a862e446248296622b7473c169c84c823ee8a16e4a43bac2f73f11fc8cb9168c73fbb0d73340f26552bac17970e9052367aa9
  languageName: node
  linkType: hard

"scheduler@npm:^0.26.0":
  version: 0.26.0
  resolution: "scheduler@npm:0.26.0"
  checksum: 10/1ecf2e5d7de1a7a132796834afe14a2d589ba7e437615bd8c06f3e0786a3ac3434655e67aac8755d9b14e05754c177e49c064261de2673aaa3c926bc98caa002
  languageName: node
  linkType: hard

"scslre@npm:0.3.0":
  version: 0.3.0
  resolution: "scslre@npm:0.3.0"
  dependencies:
    "@eslint-community/regexpp": "npm:^4.8.0"
    refa: "npm:^0.12.0"
    regexp-ast-analysis: "npm:^0.7.0"
  checksum: 10/164ec9b9a9d819838240b1df613b6c60ae00c69c4472264f354a191f73b538c064d43c0ac3accf89f5c05880ddab33846077b0cda3ad383701623d468960c005
  languageName: node
  linkType: hard

"semver@npm:7.7.1":
  version: 7.7.1
  resolution: "semver@npm:7.7.1"
  bin:
    semver: bin/semver.js
  checksum: 10/4cfa1eb91ef3751e20fc52e47a935a0118d56d6f15a837ab814da0c150778ba2ca4f1a4d9068b33070ea4273629e615066664c2cfcd7c272caf7a8a0f6518b2c
  languageName: node
  linkType: hard

"semver@npm:^6.3.0, semver@npm:^6.3.1":
  version: 6.3.1
  resolution: "semver@npm:6.3.1"
  bin:
    semver: bin/semver.js
  checksum: 10/1ef3a85bd02a760c6ef76a45b8c1ce18226de40831e02a00bad78485390b98b6ccaa31046245fc63bba4a47a6a592b6c7eedc65cc47126e60489f9cc1ce3ed7e
  languageName: node
  linkType: hard

"semver@npm:^7.3.5, semver@npm:^7.5.3, semver@npm:^7.5.4, semver@npm:^7.6.0, semver@npm:^7.7.1":
  version: 7.7.2
  resolution: "semver@npm:7.7.2"
  bin:
    semver: bin/semver.js
  checksum: 10/7a24cffcaa13f53c09ce55e05efe25cd41328730b2308678624f8b9f5fc3093fc4d189f47950f0b811ff8f3c3039c24a2c36717ba7961615c682045bf03e1dda
  languageName: node
  linkType: hard

"send@npm:0.19.0":
  version: 0.19.0
  resolution: "send@npm:0.19.0"
  dependencies:
    debug: "npm:2.6.9"
    depd: "npm:2.0.0"
    destroy: "npm:1.2.0"
    encodeurl: "npm:~1.0.2"
    escape-html: "npm:~1.0.3"
    etag: "npm:~1.8.1"
    fresh: "npm:0.5.2"
    http-errors: "npm:2.0.0"
    mime: "npm:1.6.0"
    ms: "npm:2.1.3"
    on-finished: "npm:2.4.1"
    range-parser: "npm:~1.2.1"
    statuses: "npm:2.0.1"
  checksum: 10/1f6064dea0ae4cbe4878437aedc9270c33f2a6650a77b56a16b62d057527f2766d96ee282997dd53ec0339082f2aad935bc7d989b46b48c82fc610800dc3a1d0
  languageName: node
  linkType: hard

"seroval-plugins@npm:^1.3.2, seroval-plugins@npm:~1.3.0":
  version: 1.3.3
  resolution: "seroval-plugins@npm:1.3.3"
  peerDependencies:
    seroval: ^1.0
  checksum: 10/2d30ec90313b3cf3d85c81b5ee8fb535c71489dc8e70073be767eba08ef8b1e18826cede1586a34ec87ad0ae8000a013506b02c99f4f5e3b995b16760e2abf18
  languageName: node
  linkType: hard

"seroval@npm:^1.3.2, seroval@npm:~1.3.0":
  version: 1.3.2
  resolution: "seroval@npm:1.3.2"
  checksum: 10/663380c9dbd55bc58e4fb2577c1339551d1266bec41bbe9e16dfef6182295c766b3774826cdbc6311bd466f8af458a21ffcb7cab02fb57224a349322cc8989eb
  languageName: node
  linkType: hard

"serve-static@npm:1.16.2":
  version: 1.16.2
  resolution: "serve-static@npm:1.16.2"
  dependencies:
    encodeurl: "npm:~2.0.0"
    escape-html: "npm:~1.0.3"
    parseurl: "npm:~1.3.3"
    send: "npm:0.19.0"
  checksum: 10/7fa9d9c68090f6289976b34fc13c50ac8cd7f16ae6bce08d16459300f7fc61fbc2d7ebfa02884c073ec9d6ab9e7e704c89561882bbe338e99fcacb2912fde737
  languageName: node
  linkType: hard

"set-function-length@npm:^1.2.2":
  version: 1.2.2
  resolution: "set-function-length@npm:1.2.2"
  dependencies:
    define-data-property: "npm:^1.1.4"
    es-errors: "npm:^1.3.0"
    function-bind: "npm:^1.1.2"
    get-intrinsic: "npm:^1.2.4"
    gopd: "npm:^1.0.1"
    has-property-descriptors: "npm:^1.0.2"
  checksum: 10/505d62b8e088468917ca4e3f8f39d0e29f9a563b97dbebf92f4bd2c3172ccfb3c5b8e4566d5fcd00784a00433900e7cb8fbc404e2dbd8c3818ba05bb9d4a8a6d
  languageName: node
  linkType: hard

"set-function-name@npm:^2.0.2":
  version: 2.0.2
  resolution: "set-function-name@npm:2.0.2"
  dependencies:
    define-data-property: "npm:^1.1.4"
    es-errors: "npm:^1.3.0"
    functions-have-names: "npm:^1.2.3"
    has-property-descriptors: "npm:^1.0.2"
  checksum: 10/c7614154a53ebf8c0428a6c40a3b0b47dac30587c1a19703d1b75f003803f73cdfa6a93474a9ba678fa565ef5fbddc2fae79bca03b7d22ab5fd5163dbe571a74
  languageName: node
  linkType: hard

"set-proto@npm:^1.0.0":
  version: 1.0.0
  resolution: "set-proto@npm:1.0.0"
  dependencies:
    dunder-proto: "npm:^1.0.1"
    es-errors: "npm:^1.3.0"
    es-object-atoms: "npm:^1.0.0"
  checksum: 10/b87f8187bca595ddc3c0721ece4635015fd9d7cb294e6dd2e394ce5186a71bbfa4dc8a35010958c65e43ad83cde09642660e61a952883c24fd6b45ead15f045c
  languageName: node
  linkType: hard

"setprototypeof@npm:1.2.0":
  version: 1.2.0
  resolution: "setprototypeof@npm:1.2.0"
  checksum: 10/fde1630422502fbbc19e6844346778f99d449986b2f9cdcceb8326730d2f3d9964dbcb03c02aaadaefffecd0f2c063315ebea8b3ad895914bf1afc1747fc172e
  languageName: node
  linkType: hard

"shallowequal@npm:1.1.0":
  version: 1.1.0
  resolution: "shallowequal@npm:1.1.0"
  checksum: 10/f4c1de0837f106d2dbbfd5d0720a5d059d1c66b42b580965c8f06bb1db684be8783538b684092648c981294bf817869f743a066538771dbecb293df78f765e00
  languageName: node
  linkType: hard

"shebang-command@npm:^2.0.0":
  version: 2.0.0
  resolution: "shebang-command@npm:2.0.0"
  dependencies:
    shebang-regex: "npm:^3.0.0"
  checksum: 10/6b52fe87271c12968f6a054e60f6bde5f0f3d2db483a1e5c3e12d657c488a15474121a1d55cd958f6df026a54374ec38a4a963988c213b7570e1d51575cea7fa
  languageName: node
  linkType: hard

"shebang-regex@npm:^3.0.0":
  version: 3.0.0
  resolution: "shebang-regex@npm:3.0.0"
  checksum: 10/1a2bcae50de99034fcd92ad4212d8e01eedf52c7ec7830eedcf886622804fe36884278f2be8be0ea5fde3fd1c23911643a4e0f726c8685b61871c8908af01222
  languageName: node
  linkType: hard

"side-channel-list@npm:^1.0.0":
  version: 1.0.0
  resolution: "side-channel-list@npm:1.0.0"
  dependencies:
    es-errors: "npm:^1.3.0"
    object-inspect: "npm:^1.13.3"
  checksum: 10/603b928997abd21c5a5f02ae6b9cc36b72e3176ad6827fab0417ead74580cc4fb4d5c7d0a8a2ff4ead34d0f9e35701ed7a41853dac8a6d1a664fcce1a044f86f
  languageName: node
  linkType: hard

"side-channel-map@npm:^1.0.1":
  version: 1.0.1
  resolution: "side-channel-map@npm:1.0.1"
  dependencies:
    call-bound: "npm:^1.0.2"
    es-errors: "npm:^1.3.0"
    get-intrinsic: "npm:^1.2.5"
    object-inspect: "npm:^1.13.3"
  checksum: 10/5771861f77feefe44f6195ed077a9e4f389acc188f895f570d56445e251b861754b547ea9ef73ecee4e01fdada6568bfe9020d2ec2dfc5571e9fa1bbc4a10615
  languageName: node
  linkType: hard

"side-channel-weakmap@npm:^1.0.2":
  version: 1.0.2
  resolution: "side-channel-weakmap@npm:1.0.2"
  dependencies:
    call-bound: "npm:^1.0.2"
    es-errors: "npm:^1.3.0"
    get-intrinsic: "npm:^1.2.5"
    object-inspect: "npm:^1.13.3"
    side-channel-map: "npm:^1.0.1"
  checksum: 10/a815c89bc78c5723c714ea1a77c938377ea710af20d4fb886d362b0d1f8ac73a17816a5f6640f354017d7e292a43da9c5e876c22145bac00b76cfb3468001736
  languageName: node
  linkType: hard

"side-channel@npm:^1.0.4, side-channel@npm:^1.0.6, side-channel@npm:^1.1.0":
  version: 1.1.0
  resolution: "side-channel@npm:1.1.0"
  dependencies:
    es-errors: "npm:^1.3.0"
    object-inspect: "npm:^1.13.3"
    side-channel-list: "npm:^1.0.0"
    side-channel-map: "npm:^1.0.1"
    side-channel-weakmap: "npm:^1.0.2"
  checksum: 10/7d53b9db292c6262f326b6ff3bc1611db84ece36c2c7dc0e937954c13c73185b0406c56589e2bb8d071d6fee468e14c39fb5d203ee39be66b7b8174f179afaba
  languageName: node
  linkType: hard

"siginfo@npm:^2.0.0":
  version: 2.0.0
  resolution: "siginfo@npm:2.0.0"
  checksum: 10/e93ff66c6531a079af8fb217240df01f980155b5dc408d2d7bebc398dd284e383eb318153bf8acd4db3c4fe799aa5b9a641e38b0ba3b1975700b1c89547ea4e7
  languageName: node
  linkType: hard

"signal-exit@npm:^4.0.1":
  version: 4.1.0
  resolution: "signal-exit@npm:4.1.0"
  checksum: 10/c9fa63bbbd7431066174a48ba2dd9986dfd930c3a8b59de9c29d7b6854ec1c12a80d15310869ea5166d413b99f041bfa3dd80a7947bcd44ea8e6eb3ffeabfa1f
  languageName: node
  linkType: hard

"single-spa-react@npm:6.0.2":
  version: 6.0.2
  resolution: "single-spa-react@npm:6.0.2"
  dependencies:
    browserslist-config-single-spa: "npm:^1.0.1"
  peerDependencies:
    "@types/react": "*"
    "@types/react-dom": "*"
    react: "*"
  peerDependenciesMeta:
    "@types/react":
      optional: true
    "@types/react-dom":
      optional: true
  checksum: 10/4d2eedcf3b3e46963fbe6a7f1aae569ff0f5f69ea4baf0d7e38b591c51bd9c2814f62060da94490280e40a3e73be959d6dc5d5ca35fde3c56679fc567a62a568
  languageName: node
  linkType: hard

"sirv@npm:^3.0.1":
  version: 3.0.2
  resolution: "sirv@npm:3.0.2"
  dependencies:
    "@polka/url": "npm:^1.0.0-next.24"
    mrmime: "npm:^2.0.0"
    totalist: "npm:^3.0.0"
  checksum: 10/259617f4ab57664be6d963f5b27b38a6351d3e91ce70d6726985d087b40efd595fcf7f72ae010babf5e0acb63bcb3e3d6db8de34604da1011be6e28ee32aa15d
  languageName: node
  linkType: hard

"slash@npm:^3.0.0":
  version: 3.0.0
  resolution: "slash@npm:3.0.0"
  checksum: 10/94a93fff615f25a999ad4b83c9d5e257a7280c90a32a7cb8b4a87996e4babf322e469c42b7f649fd5796edd8687652f3fb452a86dc97a816f01113183393f11c
  languageName: node
  linkType: hard

"smart-buffer@npm:^4.2.0":
  version: 4.2.0
  resolution: "smart-buffer@npm:4.2.0"
  checksum: 10/927484aa0b1640fd9473cee3e0a0bcad6fce93fd7bbc18bac9ad0c33686f5d2e2c422fba24b5899c184524af01e11dd2bd051c2bf2b07e47aff8ca72cbfc60d2
  languageName: node
  linkType: hard

"snake-case@npm:^3.0.4":
  version: 3.0.4
  resolution: "snake-case@npm:3.0.4"
  dependencies:
    dot-case: "npm:^3.0.4"
    tslib: "npm:^2.0.3"
  checksum: 10/0a7a79900bbb36f8aaa922cf111702a3647ac6165736d5dc96d3ef367efc50465cac70c53cd172c382b022dac72ec91710608e5393de71f76d7142e6fd80e8a3
  languageName: node
  linkType: hard

"socks-proxy-agent@npm:^8.0.3":
  version: 8.0.5
  resolution: "socks-proxy-agent@npm:8.0.5"
  dependencies:
    agent-base: "npm:^7.1.2"
    debug: "npm:^4.3.4"
    socks: "npm:^2.8.3"
  checksum: 10/ee99e1dacab0985b52cbe5a75640be6e604135e9489ebdc3048635d186012fbaecc20fbbe04b177dee434c319ba20f09b3e7dfefb7d932466c0d707744eac05c
  languageName: node
  linkType: hard

"socks@npm:^2.8.3":
  version: 2.8.7
  resolution: "socks@npm:2.8.7"
  dependencies:
    ip-address: "npm:^10.0.1"
    smart-buffer: "npm:^4.2.0"
  checksum: 10/d19366c95908c19db154f329bbe94c2317d315dc933a7c2b5101e73f32a555c84fb199b62174e1490082a593a4933d8d5a9b297bde7d1419c14a11a965f51356
  languageName: node
  linkType: hard

"solid-js@npm:^1.9.5":
  version: 1.9.9
  resolution: "solid-js@npm:1.9.9"
  dependencies:
    csstype: "npm:^3.1.0"
    seroval: "npm:~1.3.0"
    seroval-plugins: "npm:~1.3.0"
  checksum: 10/74e9e0ccea781d16bfc4bf91b1b0381cf27c825f24074e112b37a9a42ef30e9f3727599a1b7e6c33f13408398128377ec7f548f37c0bef5e20dd83f3c93f4943
  languageName: node
  linkType: hard

"source-map-js@npm:^1.2.0, source-map-js@npm:^1.2.1":
  version: 1.2.1
  resolution: "source-map-js@npm:1.2.1"
  checksum: 10/ff9d8c8bf096d534a5b7707e0382ef827b4dd360a577d3f34d2b9f48e12c9d230b5747974ee7c607f0df65113732711bb701fe9ece3c7edbd43cb2294d707df3
  languageName: node
  linkType: hard

"source-map-support@npm:~0.5.20":
  version: 0.5.21
  resolution: "source-map-support@npm:0.5.21"
  dependencies:
    buffer-from: "npm:^1.0.0"
    source-map: "npm:^0.6.0"
  checksum: 10/8317e12d84019b31e34b86d483dd41d6f832f389f7417faf8fc5c75a66a12d9686e47f589a0554a868b8482f037e23df9d040d29387eb16fa14cb85f091ba207
  languageName: node
  linkType: hard

"source-map@npm:^0.6.0, source-map@npm:~0.6.1":
  version: 0.6.1
  resolution: "source-map@npm:0.6.1"
  checksum: 10/59ef7462f1c29d502b3057e822cdbdae0b0e565302c4dd1a95e11e793d8d9d62006cdc10e0fd99163ca33ff2071360cf50ee13f90440806e7ed57d81cba2f7ff
  languageName: node
  linkType: hard

"source-map@npm:^0.7.4":
  version: 0.7.6
  resolution: "source-map@npm:0.7.6"
  checksum: 10/c8d2da7c57c14f3fd7568f764b39ad49bbf9dd7632b86df3542b31fed117d4af2fb74a4f886fc06baf7a510fee68e37998efc3080aacdac951c36211dc29a7a3
  languageName: node
  linkType: hard

"ssri@npm:^12.0.0":
  version: 12.0.0
  resolution: "ssri@npm:12.0.0"
  dependencies:
    minipass: "npm:^7.0.3"
  checksum: 10/7024c1a6e39b3f18aa8f1c8290e884fe91b0f9ca5a6c6d410544daad54de0ba664db879afe16412e187c6c292fd60b937f047ee44292e5c2af2dcc6d8e1a9b48
  languageName: node
  linkType: hard

"stable-hash@npm:^0.0.5":
  version: 0.0.5
  resolution: "stable-hash@npm:0.0.5"
  checksum: 10/9222ea2c558e37c4a576cb4e406966b9e6aa05b93f5c4f09ef4aaabe3577439b9b8fbff407b16840b63e2ae83de74290c7b1c2da7360d571e480e46a4aec0a56
  languageName: node
  linkType: hard

"stackback@npm:0.0.2":
  version: 0.0.2
  resolution: "stackback@npm:0.0.2"
  checksum: 10/2d4dc4e64e2db796de4a3c856d5943daccdfa3dd092e452a1ce059c81e9a9c29e0b9badba91b43ef0d5ff5c04ee62feb3bcc559a804e16faf447bac2d883aa99
  languageName: node
  linkType: hard

"statuses@npm:2.0.1":
  version: 2.0.1
  resolution: "statuses@npm:2.0.1"
  checksum: 10/18c7623fdb8f646fb213ca4051be4df7efb3484d4ab662937ca6fbef7ced9b9e12842709872eb3020cc3504b93bde88935c9f6417489627a7786f24f8031cbcb
  languageName: node
  linkType: hard

"statuses@npm:~1.5.0":
  version: 1.5.0
  resolution: "statuses@npm:1.5.0"
  checksum: 10/c469b9519de16a4bb19600205cffb39ee471a5f17b82589757ca7bd40a8d92ebb6ed9f98b5a540c5d302ccbc78f15dc03cc0280dd6e00df1335568a5d5758a5c
  languageName: node
  linkType: hard

"std-env@npm:^3.9.0":
  version: 3.9.0
  resolution: "std-env@npm:3.9.0"
  checksum: 10/3044b2c54a74be4f460db56725571241ab3ac89a91f39c7709519bc90fa37148784bc4cd7d3a301aa735f43bd174496f263563f76703ce3e81370466ab7c235b
  languageName: node
  linkType: hard

"stop-iteration-iterator@npm:^1.0.0, stop-iteration-iterator@npm:^1.1.0":
  version: 1.1.0
  resolution: "stop-iteration-iterator@npm:1.1.0"
  dependencies:
    es-errors: "npm:^1.3.0"
    internal-slot: "npm:^1.1.0"
  checksum: 10/ff36c4db171ee76c936ccfe9541946b77017f12703d4c446652017356816862d3aa029a64e7d4c4ceb484e00ed4a81789333896390d808458638f3a216aa1f41
  languageName: node
  linkType: hard

"string-width-cjs@npm:string-width@^4.2.0, string-width@npm:^4.1.0":
  version: 4.2.3
  resolution: "string-width@npm:4.2.3"
  dependencies:
    emoji-regex: "npm:^8.0.0"
    is-fullwidth-code-point: "npm:^3.0.0"
    strip-ansi: "npm:^6.0.1"
  checksum: 10/e52c10dc3fbfcd6c3a15f159f54a90024241d0f149cf8aed2982a2d801d2e64df0bf1dc351cf8e95c3319323f9f220c16e740b06faecd53e2462df1d2b5443fb
  languageName: node
  linkType: hard

"string-width@npm:^5.0.1, string-width@npm:^5.1.2":
  version: 5.1.2
  resolution: "string-width@npm:5.1.2"
  dependencies:
    eastasianwidth: "npm:^0.2.0"
    emoji-regex: "npm:^9.2.2"
    strip-ansi: "npm:^7.0.1"
  checksum: 10/7369deaa29f21dda9a438686154b62c2c5f661f8dda60449088f9f980196f7908fc39fdd1803e3e01541970287cf5deae336798337e9319a7055af89dafa7193
  languageName: node
  linkType: hard

"string.prototype.includes@npm:^2.0.1":
  version: 2.0.1
  resolution: "string.prototype.includes@npm:2.0.1"
  dependencies:
    call-bind: "npm:^1.0.7"
    define-properties: "npm:^1.2.1"
    es-abstract: "npm:^1.23.3"
  checksum: 10/939a5447e4a99a86f29cc97fa24f358e5071f79e34746de4c7eb2cd736ed626ad24870a1e356f33915b3b352bb87f7e4d1cebc15d1e1aaae0923777e21b1b28b
  languageName: node
  linkType: hard

"string.prototype.matchall@npm:^4.0.12":
  version: 4.0.12
  resolution: "string.prototype.matchall@npm:4.0.12"
  dependencies:
    call-bind: "npm:^1.0.8"
    call-bound: "npm:^1.0.3"
    define-properties: "npm:^1.2.1"
    es-abstract: "npm:^1.23.6"
    es-errors: "npm:^1.3.0"
    es-object-atoms: "npm:^1.0.0"
    get-intrinsic: "npm:^1.2.6"
    gopd: "npm:^1.2.0"
    has-symbols: "npm:^1.1.0"
    internal-slot: "npm:^1.1.0"
    regexp.prototype.flags: "npm:^1.5.3"
    set-function-name: "npm:^2.0.2"
    side-channel: "npm:^1.1.0"
  checksum: 10/e4ab34b9e7639211e6c5e9759adb063028c5c5c4fc32ad967838b2bd1e5ce83a66ae8ec755d24a79302849f090b59194571b2c33471e86e7821b21c0f56df316
  languageName: node
  linkType: hard

"string.prototype.repeat@npm:^1.0.0":
  version: 1.0.0
  resolution: "string.prototype.repeat@npm:1.0.0"
  dependencies:
    define-properties: "npm:^1.1.3"
    es-abstract: "npm:^1.17.5"
  checksum: 10/4b1bd91b75fa8fdf0541625184ebe80e445a465ce4253c19c3bccd633898005dadae0f74b85ae72662a53aafb8035bf48f8f5c0755aec09bc106a7f13959d05e
  languageName: node
  linkType: hard

"string.prototype.trim@npm:^1.2.10":
  version: 1.2.10
  resolution: "string.prototype.trim@npm:1.2.10"
  dependencies:
    call-bind: "npm:^1.0.8"
    call-bound: "npm:^1.0.2"
    define-data-property: "npm:^1.1.4"
    define-properties: "npm:^1.2.1"
    es-abstract: "npm:^1.23.5"
    es-object-atoms: "npm:^1.0.0"
    has-property-descriptors: "npm:^1.0.2"
  checksum: 10/47bb63cd2470a64bc5e2da1e570d369c016ccaa85c918c3a8bb4ab5965120f35e66d1f85ea544496fac84b9207a6b722adf007e6c548acd0813e5f8a82f9712a
  languageName: node
  linkType: hard

"string.prototype.trimend@npm:^1.0.8, string.prototype.trimend@npm:^1.0.9":
  version: 1.0.9
  resolution: "string.prototype.trimend@npm:1.0.9"
  dependencies:
    call-bind: "npm:^1.0.8"
    call-bound: "npm:^1.0.2"
    define-properties: "npm:^1.2.1"
    es-object-atoms: "npm:^1.0.0"
  checksum: 10/140c73899b6747de9e499c7c2e7a83d549c47a26fa06045b69492be9cfb9e2a95187499a373983a08a115ecff8bc3bd7b0fb09b8ff72fb2172abe766849272ef
  languageName: node
  linkType: hard

"string.prototype.trimstart@npm:^1.0.8":
  version: 1.0.8
  resolution: "string.prototype.trimstart@npm:1.0.8"
  dependencies:
    call-bind: "npm:^1.0.7"
    define-properties: "npm:^1.2.1"
    es-object-atoms: "npm:^1.0.0"
  checksum: 10/160167dfbd68e6f7cb9f51a16074eebfce1571656fc31d40c3738ca9e30e35496f2c046fe57b6ad49f65f238a152be8c86fd9a2dd58682b5eba39dad995b3674
  languageName: node
  linkType: hard

"strip-ansi-cjs@npm:strip-ansi@^6.0.1, strip-ansi@npm:^6.0.0, strip-ansi@npm:^6.0.1":
  version: 6.0.1
  resolution: "strip-ansi@npm:6.0.1"
  dependencies:
    ansi-regex: "npm:^5.0.1"
  checksum: 10/ae3b5436d34fadeb6096367626ce987057713c566e1e7768818797e00ac5d62023d0f198c4e681eae9e20701721980b26a64a8f5b91238869592a9c6800719a2
  languageName: node
  linkType: hard

"strip-ansi@npm:^7.0.1, strip-ansi@npm:^7.1.0":
  version: 7.1.0
  resolution: "strip-ansi@npm:7.1.0"
  dependencies:
    ansi-regex: "npm:^6.0.1"
  checksum: 10/475f53e9c44375d6e72807284024ac5d668ee1d06010740dec0b9744f2ddf47de8d7151f80e5f6190fc8f384e802fdf9504b76a7e9020c9faee7103623338be2
  languageName: node
  linkType: hard

"strip-bom@npm:^3.0.0":
  version: 3.0.0
  resolution: "strip-bom@npm:3.0.0"
  checksum: 10/8d50ff27b7ebe5ecc78f1fe1e00fcdff7af014e73cf724b46fb81ef889eeb1015fc5184b64e81a2efe002180f3ba431bdd77e300da5c6685d702780fbf0c8d5b
  languageName: node
  linkType: hard

"strip-indent@npm:^3.0.0":
  version: 3.0.0
  resolution: "strip-indent@npm:3.0.0"
  dependencies:
    min-indent: "npm:^1.0.0"
  checksum: 10/18f045d57d9d0d90cd16f72b2313d6364fd2cb4bf85b9f593523ad431c8720011a4d5f08b6591c9d580f446e78855c5334a30fb91aa1560f5d9f95ed1b4a0530
  languageName: node
  linkType: hard

"strip-json-comments@npm:^3.1.1":
  version: 3.1.1
  resolution: "strip-json-comments@npm:3.1.1"
  checksum: 10/492f73e27268f9b1c122733f28ecb0e7e8d8a531a6662efbd08e22cccb3f9475e90a1b82cab06a392f6afae6d2de636f977e231296400d0ec5304ba70f166443
  languageName: node
  linkType: hard

"strip-literal@npm:^3.0.0":
  version: 3.0.0
  resolution: "strip-literal@npm:3.0.0"
  dependencies:
    js-tokens: "npm:^9.0.1"
  checksum: 10/da1616f654f3ff481e078597b4565373a5eeed78b83de4a11a1a1b98292a9036f2474e528eff19b6eed93370428ff957a473827057c117495086436725d7efad
  languageName: node
  linkType: hard

"styled-components@npm:6.1.19":
  version: 6.1.19
  resolution: "styled-components@npm:6.1.19"
  dependencies:
    "@emotion/is-prop-valid": "npm:1.2.2"
    "@emotion/unitless": "npm:0.8.1"
    "@types/stylis": "npm:4.2.5"
    css-to-react-native: "npm:3.2.0"
    csstype: "npm:3.1.3"
    postcss: "npm:8.4.49"
    shallowequal: "npm:1.1.0"
    stylis: "npm:4.3.2"
    tslib: "npm:2.6.2"
  peerDependencies:
    react: ">= 16.8.0"
    react-dom: ">= 16.8.0"
  checksum: 10/1e7503b48619c37691791f063d4ebbb32caf3ddef2ae11c3c706c4d6df5c0538357d406ef7a5f6077b2fef6294e365d4426240155a93cab3e49af58c9404ef9a
  languageName: node
  linkType: hard

"stylis@npm:4.3.2":
  version: 4.3.2
  resolution: "stylis@npm:4.3.2"
  checksum: 10/4d3e3cb5cbfc7abdf14e424c8631a15fd15cbf0357ffc641c319587e00c2d1036b1a71cb88b42411bc3ce10d7730ad3fb9789b034d11365e8a19d23f56486c77
  languageName: node
  linkType: hard

"supports-color@npm:^7.1.0":
  version: 7.2.0
  resolution: "supports-color@npm:7.2.0"
  dependencies:
    has-flag: "npm:^4.0.0"
  checksum: 10/c8bb7afd564e3b26b50ca6ee47572c217526a1389fe018d00345856d4a9b08ffbd61fadaf283a87368d94c3dcdb8f5ffe2650a5a65863e21ad2730ca0f05210a
  languageName: node
  linkType: hard

"supports-preserve-symlinks-flag@npm:^1.0.0":
  version: 1.0.0
  resolution: "supports-preserve-symlinks-flag@npm:1.0.0"
  checksum: 10/a9dc19ae2220c952bd2231d08ddeecb1b0328b61e72071ff4000c8384e145cc07c1c0bdb3b5a1cb06e186a7b2790f1dee793418b332f6ddf320de25d9125be7e
  languageName: node
  linkType: hard

"svg-parser@npm:^2.0.4":
  version: 2.0.4
  resolution: "svg-parser@npm:2.0.4"
  checksum: 10/ec196da6ea21481868ab26911970e35488361c39ead1c6cdd977ba16c885c21a91ddcbfd113bfb01f79a822e2a751ef85b2f7f95e2cb9245558ebce12c34af1f
  languageName: node
  linkType: hard

"symbol-tree@npm:^3.2.4":
  version: 3.2.4
  resolution: "symbol-tree@npm:3.2.4"
  checksum: 10/c09a00aadf279d47d0c5c46ca3b6b2fbaeb45f0a184976d599637d412d3a70bbdc043ff33effe1206dea0e36e0ad226cb957112e7ce9a4bf2daedf7fa4f85c53
  languageName: node
  linkType: hard

"synckit@npm:^0.11.0":
  version: 0.11.11
  resolution: "synckit@npm:0.11.11"
  dependencies:
    "@pkgr/core": "npm:^0.2.9"
  checksum: 10/6ecd88212b5be80004376b6ea74babcba284566ff59a50d8803afcaa78c165b5d268635c1dd84532ee3f690a979409e1eda225a8a35bed2d135ffdcea06ce7b0
  languageName: node
  linkType: hard

"systemjs@npm:6.15.1":
  version: 6.15.1
  resolution: "systemjs@npm:6.15.1"
  checksum: 10/9454c32515cdf7b033cac547233b161d6154abc020a1a97afc8654a01d63cf2bc51101869807aa7f8529aae98475a2cb4522c1e31da6d33832c8e12f00ee4e18
  languageName: node
  linkType: hard

"tar@npm:^7.4.3":
  version: 7.4.3
  resolution: "tar@npm:7.4.3"
  dependencies:
    "@isaacs/fs-minipass": "npm:^4.0.0"
    chownr: "npm:^3.0.0"
    minipass: "npm:^7.1.2"
    minizlib: "npm:^3.0.1"
    mkdirp: "npm:^3.0.1"
    yallist: "npm:^5.0.0"
  checksum: 10/12a2a4fc6dee23e07cc47f1aeb3a14a1afd3f16397e1350036a8f4cdfee8dcac7ef5978337a4e7b2ac2c27a9a6d46388fc2088ea7c80cb6878c814b1425f8ecf
  languageName: node
  linkType: hard

"terser@npm:5.43.1":
  version: 5.43.1
  resolution: "terser@npm:5.43.1"
  dependencies:
    "@jridgewell/source-map": "npm:^0.3.3"
    acorn: "npm:^8.14.0"
    commander: "npm:^2.20.0"
    source-map-support: "npm:~0.5.20"
  bin:
    terser: bin/terser
  checksum: 10/c0a0fd62319e0ce66e800f57ae12ef4ca45f12e9422dac160b866f0d890d01f8b547c96de2557b8443d96953db36be5d900e8006436ef9f628dbd38082e8fe5d
  languageName: node
  linkType: hard

"test-exclude@npm:^7.0.1":
  version: 7.0.1
  resolution: "test-exclude@npm:7.0.1"
  dependencies:
    "@istanbuljs/schema": "npm:^0.1.2"
    glob: "npm:^10.4.1"
    minimatch: "npm:^9.0.4"
  checksum: 10/e6f6f4e1df2e7810e082e8d7dfc53be51a931e6e87925f5e1c2ef92cc1165246ba3bf2dae6b5d86251c16925683dba906bd41e40169ebc77120a2d1b5a0dbbe0
  languageName: node
  linkType: hard

"three@npm:0.180.0":
  version: 0.180.0
  resolution: "three@npm:0.180.0"
  checksum: 10/91873a9a78a99d948fffd56e5a2369ad9aeaa45c763ba74c260f4af232a4798d9fc7da9528604d2f9b1110683e63737f26313d78b7e42e42b66d11d67e48cba3
  languageName: node
  linkType: hard

"tiny-invariant@npm:^1.3.3":
  version: 1.3.3
  resolution: "tiny-invariant@npm:1.3.3"
  checksum: 10/5e185c8cc2266967984ce3b352a4e57cb89dad5a8abb0dea21468a6ecaa67cd5bb47a3b7a85d08041008644af4f667fb8b6575ba38ba5fb00b3b5068306e59fe
  languageName: node
  linkType: hard

"tiny-warning@npm:^1.0.3":
  version: 1.0.3
  resolution: "tiny-warning@npm:1.0.3"
  checksum: 10/da62c4acac565902f0624b123eed6dd3509bc9a8d30c06e017104bedcf5d35810da8ff72864400ad19c5c7806fc0a8323c68baf3e326af7cb7d969f846100d71
  languageName: node
  linkType: hard

"tinybench@npm:^2.9.0":
  version: 2.9.0
  resolution: "tinybench@npm:2.9.0"
  checksum: 10/cfa1e1418e91289219501703c4693c70708c91ffb7f040fd318d24aef419fb5a43e0c0160df9471499191968b2451d8da7f8087b08c3133c251c40d24aced06c
  languageName: node
  linkType: hard

"tinyexec@npm:^0.3.2":
  version: 0.3.2
  resolution: "tinyexec@npm:0.3.2"
  checksum: 10/b9d5fed3166fb1acd1e7f9a89afcd97ccbe18b9c1af0278e429455f6976d69271ba2d21797e7c36d57d6b05025e525d2882d88c2ab435b60d1ddf2fea361de57
  languageName: node
  linkType: hard

"tinyglobby@npm:^0.2.12, tinyglobby@npm:^0.2.14":
  version: 0.2.14
  resolution: "tinyglobby@npm:0.2.14"
  dependencies:
    fdir: "npm:^6.4.4"
    picomatch: "npm:^4.0.2"
  checksum: 10/3d306d319718b7cc9d79fb3f29d8655237aa6a1f280860a217f93417039d0614891aee6fc47c5db315f4fcc6ac8d55eb8e23e2de73b2c51a431b42456d9e5764
  languageName: node
  linkType: hard

"tinypool@npm:^1.1.1":
  version: 1.1.1
  resolution: "tinypool@npm:1.1.1"
  checksum: 10/0d54139e9dbc6ef33349768fa78890a4d708d16a7ab68e4e4ef3bb740609ddf0f9fd13292c2f413fbba756166c97051a657181c8f7ae92ade690604f183cc01d
  languageName: node
  linkType: hard

"tinyrainbow@npm:^2.0.0":
  version: 2.0.0
  resolution: "tinyrainbow@npm:2.0.0"
  checksum: 10/94d4e16246972614a5601eeb169ba94f1d49752426312d3cf8cc4f2cc663a2e354ffc653aa4de4eebccbf9eeebdd0caef52d1150271fdfde65d7ae7f3dcb9eb5
  languageName: node
  linkType: hard

"tinyspy@npm:^4.0.3":
  version: 4.0.3
  resolution: "tinyspy@npm:4.0.3"
  checksum: 10/b6a3ed40dd76a2b3c020250cf1401506b456509d1fb9dba0c7b0e644d258dac722843b85c57ccc36c8687db1e7978cb6adcc43e3b71c475910c085b96d41cb53
  languageName: node
  linkType: hard

"tldts-core@npm:^6.1.86":
  version: 6.1.86
  resolution: "tldts-core@npm:6.1.86"
  checksum: 10/cb5dff9cc15661ac773a2099e98c99a5cb3cebc35909c23cc4261ff7992032c7501995ae995de3574dbbf3431e59c47496534d52f5e96abcb231f0e72144c020
  languageName: node
  linkType: hard

"tldts@npm:^6.1.32":
  version: 6.1.86
  resolution: "tldts@npm:6.1.86"
  dependencies:
    tldts-core: "npm:^6.1.86"
  bin:
    tldts: bin/cli.js
  checksum: 10/f7e66824e44479ccdda55ea556af14ce61c4d27708be403e3f90631defde49f82a580e1ca07187cc7e3b349e257a30c2808a22903f3a0548e136ebb609ccc109
  languageName: node
  linkType: hard

"to-regex-range@npm:^5.0.1":
  version: 5.0.1
  resolution: "to-regex-range@npm:5.0.1"
  dependencies:
    is-number: "npm:^7.0.0"
  checksum: 10/10dda13571e1f5ad37546827e9b6d4252d2e0bc176c24a101252153ef435d83696e2557fe128c4678e4e78f5f01e83711c703eef9814eb12dab028580d45980a
  languageName: node
  linkType: hard

"toidentifier@npm:1.0.1":
  version: 1.0.1
  resolution: "toidentifier@npm:1.0.1"
  checksum: 10/952c29e2a85d7123239b5cfdd889a0dde47ab0497f0913d70588f19c53f7e0b5327c95f4651e413c74b785147f9637b17410ac8c846d5d4a20a5a33eb6dc3a45
  languageName: node
  linkType: hard

"totalist@npm:^3.0.0":
  version: 3.0.1
  resolution: "totalist@npm:3.0.1"
  checksum: 10/5132d562cf88ff93fd710770a92f31dbe67cc19b5c6ccae2efc0da327f0954d211bbfd9456389655d726c624f284b4a23112f56d1da931ca7cfabbe1f45e778a
  languageName: node
  linkType: hard

"tough-cookie@npm:^5.1.1":
  version: 5.1.2
  resolution: "tough-cookie@npm:5.1.2"
  dependencies:
    tldts: "npm:^6.1.32"
  checksum: 10/de430e6e6d34b794137e05b8ac2aa6b74ebbe6cdceb4126f168cf1e76101162a4b2e0e7587c3b70e728bd8654fc39958b2035be7619ee6f08e7257610ba4cd04
  languageName: node
  linkType: hard

"tr46@npm:^5.1.0":
  version: 5.1.1
  resolution: "tr46@npm:5.1.1"
  dependencies:
    punycode: "npm:^2.3.1"
  checksum: 10/833a0e1044574da5790148fd17866d4ddaea89e022de50279967bcd6b28b4ce0d30d59eb3acf9702b60918975b3bad481400337e3a2e6326cffa5c77b874753d
  languageName: node
  linkType: hard

"tr46@npm:~0.0.3":
  version: 0.0.3
  resolution: "tr46@npm:0.0.3"
  checksum: 10/8f1f5aa6cb232f9e1bdc86f485f916b7aa38caee8a778b378ffec0b70d9307873f253f5cbadbe2955ece2ac5c83d0dc14a77513166ccd0a0c7fe197e21396695
  languageName: node
  linkType: hard

"ts-api-utils@npm:^1.3.0":
  version: 1.4.3
  resolution: "ts-api-utils@npm:1.4.3"
  peerDependencies:
    typescript: ">=4.2.0"
  checksum: 10/713c51e7392323305bd4867422ba130fbf70873ef6edbf80ea6d7e9c8f41eeeb13e40e8e7fe7cd321d74e4864777329797077268c9f570464303a1723f1eed39
  languageName: node
  linkType: hard

"ts-api-utils@npm:^2.0.0, ts-api-utils@npm:^2.1.0":
  version: 2.1.0
  resolution: "ts-api-utils@npm:2.1.0"
  peerDependencies:
    typescript: ">=4.8.4"
  checksum: 10/02e55b49d9617c6eebf8aadfa08d3ca03ca0cd2f0586ad34117fdfc7aa3cd25d95051843fde9df86665ad907f99baed179e7a117b11021417f379e4d2614eacd
  languageName: node
  linkType: hard

"ts-dedent@npm:^2.2.0":
  version: 2.2.0
  resolution: "ts-dedent@npm:2.2.0"
  checksum: 10/93ed8f7878b6d5ed3c08d99b740010eede6bccfe64bce61c5a4da06a2c17d6ddbb80a8c49c2d15251de7594a4f93ffa21dd10e7be75ef66a4dc9951b4a94e2af
  languageName: node
  linkType: hard

"tsconfck@npm:^3.0.3":
  version: 3.1.6
  resolution: "tsconfck@npm:3.1.6"
  peerDependencies:
    typescript: ^5.0.0
  peerDependenciesMeta:
    typescript:
      optional: true
  bin:
    tsconfck: bin/tsconfck.js
  checksum: 10/8574595286850273bf83319b4e67ca760088df3c36f7ca1425aaf797416672e854271bd31e75c9b3e1836ed5b66410c6bc38cbbda9c638a5416c6a682ed94132
  languageName: node
  linkType: hard

"tsconfig-paths@npm:^3.15.0":
  version: 3.15.0
  resolution: "tsconfig-paths@npm:3.15.0"
  dependencies:
    "@types/json5": "npm:^0.0.29"
    json5: "npm:^1.0.2"
    minimist: "npm:^1.2.6"
    strip-bom: "npm:^3.0.0"
  checksum: 10/2041beaedc6c271fc3bedd12e0da0cc553e65d030d4ff26044b771fac5752d0460944c0b5e680f670c2868c95c664a256cec960ae528888db6ded83524e33a14
  languageName: node
  linkType: hard

"tslib@npm:2.6.2":
  version: 2.6.2
  resolution: "tslib@npm:2.6.2"
  checksum: 10/bd26c22d36736513980091a1e356378e8b662ded04204453d353a7f34a4c21ed0afc59b5f90719d4ba756e581a162ecbf93118dc9c6be5acf70aa309188166ca
  languageName: node
  linkType: hard

"tslib@npm:^2.0.1, tslib@npm:^2.0.3, tslib@npm:^2.4.0":
  version: 2.8.1
  resolution: "tslib@npm:2.8.1"
  checksum: 10/3e2e043d5c2316461cb54e5c7fe02c30ef6dccb3384717ca22ae5c6b5bc95232a6241df19c622d9c73b809bea33b187f6dbc73030963e29950c2141bc32a79f7
  languageName: node
  linkType: hard

"tsx@npm:^4.19.2":
  version: 4.20.5
  resolution: "tsx@npm:4.20.5"
  dependencies:
    esbuild: "npm:~0.25.0"
    fsevents: "npm:~2.3.3"
    get-tsconfig: "npm:^4.7.5"
  dependenciesMeta:
    fsevents:
      optional: true
  bin:
    tsx: dist/cli.mjs
  checksum: 10/161420678027c43d07b60b7b6b512cc67ff86ae3cca0641a19b0d3e742c5e262bca57034c4bff6d9346f9269e9ada24b6030e1d2bc890df5e1a9754865d3c08a
  languageName: node
  linkType: hard

"type-check@npm:^0.4.0, type-check@npm:~0.4.0":
  version: 0.4.0
  resolution: "type-check@npm:0.4.0"
  dependencies:
    prelude-ls: "npm:^1.2.1"
  checksum: 10/14687776479d048e3c1dbfe58a2409e00367810d6960c0f619b33793271ff2a27f81b52461f14a162f1f89a9b1d8da1b237fc7c99b0e1fdcec28ec63a86b1fec
  languageName: node
  linkType: hard

"type-fest@npm:^2.19.0":
  version: 2.19.0
  resolution: "type-fest@npm:2.19.0"
  checksum: 10/7bf9e8fdf34f92c8bb364c0af14ca875fac7e0183f2985498b77be129dc1b3b1ad0a6b3281580f19e48c6105c037fb966ad9934520c69c6434d17fd0af4eed78
  languageName: node
  linkType: hard

"type-is@npm:~1.6.18":
  version: 1.6.18
  resolution: "type-is@npm:1.6.18"
  dependencies:
    media-typer: "npm:0.3.0"
    mime-types: "npm:~2.1.24"
  checksum: 10/0bd9eeae5efd27d98fd63519f999908c009e148039d8e7179a074f105362d4fcc214c38b24f6cda79c87e563cbd12083a4691381ed28559220d4a10c2047bed4
  languageName: node
  linkType: hard

"typed-array-buffer@npm:^1.0.3":
  version: 1.0.3
  resolution: "typed-array-buffer@npm:1.0.3"
  dependencies:
    call-bound: "npm:^1.0.3"
    es-errors: "npm:^1.3.0"
    is-typed-array: "npm:^1.1.14"
  checksum: 10/3fb91f0735fb413b2bbaaca9fabe7b8fc14a3fa5a5a7546bab8a57e755be0e3788d893195ad9c2b842620592de0e68d4c077d4c2c41f04ec25b8b5bb82fa9a80
  languageName: node
  linkType: hard

"typed-array-byte-length@npm:^1.0.3":
  version: 1.0.3
  resolution: "typed-array-byte-length@npm:1.0.3"
  dependencies:
    call-bind: "npm:^1.0.8"
    for-each: "npm:^0.3.3"
    gopd: "npm:^1.2.0"
    has-proto: "npm:^1.2.0"
    is-typed-array: "npm:^1.1.14"
  checksum: 10/269dad101dda73e3110117a9b84db86f0b5c07dad3a9418116fd38d580cab7fc628a4fc167e29b6d7c39da2f53374b78e7cb578b3c5ec7a556689d985d193519
  languageName: node
  linkType: hard

"typed-array-byte-offset@npm:^1.0.4":
  version: 1.0.4
  resolution: "typed-array-byte-offset@npm:1.0.4"
  dependencies:
    available-typed-arrays: "npm:^1.0.7"
    call-bind: "npm:^1.0.8"
    for-each: "npm:^0.3.3"
    gopd: "npm:^1.2.0"
    has-proto: "npm:^1.2.0"
    is-typed-array: "npm:^1.1.15"
    reflect.getprototypeof: "npm:^1.0.9"
  checksum: 10/c2869aa584cdae24ecfd282f20a0f556b13a49a9d5bca1713370bb3c89dff0ccbc5ceb45cb5b784c98f4579e5e3e2a07e438c3a5b8294583e2bd4abbd5104fb5
  languageName: node
  linkType: hard

"typed-array-length@npm:^1.0.7":
  version: 1.0.7
  resolution: "typed-array-length@npm:1.0.7"
  dependencies:
    call-bind: "npm:^1.0.7"
    for-each: "npm:^0.3.3"
    gopd: "npm:^1.0.1"
    is-typed-array: "npm:^1.1.13"
    possible-typed-array-names: "npm:^1.0.0"
    reflect.getprototypeof: "npm:^1.0.6"
  checksum: 10/d6b2f0e81161682d2726eb92b1dc2b0890890f9930f33f9bcf6fc7272895ce66bc368066d273e6677776de167608adc53fcf81f1be39a146d64b630edbf2081c
  languageName: node
  linkType: hard

"typescript-eslint@npm:8.39.0":
  version: 8.39.0
  resolution: "typescript-eslint@npm:8.39.0"
  dependencies:
    "@typescript-eslint/eslint-plugin": "npm:8.39.0"
    "@typescript-eslint/parser": "npm:8.39.0"
    "@typescript-eslint/typescript-estree": "npm:8.39.0"
    "@typescript-eslint/utils": "npm:8.39.0"
  peerDependencies:
    eslint: ^8.57.0 || ^9.0.0
    typescript: ">=4.8.4 <6.0.0"
  checksum: 10/cdd28f429c10899e2b9777f63db00118dc7b1dae5cd0d79950ee6b46e0faf19119afef67b27bf14ff88a8ee8e3d0c2cd700c0fe2d59ce5b22b2112d92135553f
  languageName: node
  linkType: hard

"typescript@npm:5.8.3":
  version: 5.8.3
  resolution: "typescript@npm:5.8.3"
  bin:
    tsc: bin/tsc
    tsserver: bin/tsserver
  checksum: 10/65c40944c51b513b0172c6710ee62e951b70af6f75d5a5da745cb7fab132c09ae27ffdf7838996e3ed603bb015dadd099006658046941bd0ba30340cc563ae92
  languageName: node
  linkType: hard

"typescript@npm:^5":
  version: 5.9.2
  resolution: "typescript@npm:5.9.2"
  bin:
    tsc: bin/tsc
    tsserver: bin/tsserver
  checksum: 10/cc2fe6c822819de5d453fa25aa9f32096bf70dde215d481faa1ad84a283dfb264e33988ed8f6d36bc803dd0b16dbe943efa311a798ef76d5b3892a05dfbfd628
  languageName: node
  linkType: hard

"typescript@patch:typescript@npm%3A5.8.3#optional!builtin<compat/typescript>":
  version: 5.8.3
  resolution: "typescript@patch:typescript@npm%3A5.8.3#optional!builtin<compat/typescript>::version=5.8.3&hash=5786d5"
  bin:
    tsc: bin/tsc
    tsserver: bin/tsserver
  checksum: 10/b9b1e73dabac5dc730c041325dbd9c99467c1b0d239f1b74ec3b90d831384af3e2ba973946232df670519147eb51a2c20f6f96163cea2b359f03de1e2091cc4f
  languageName: node
  linkType: hard

"typescript@patch:typescript@npm%3A^5#optional!builtin<compat/typescript>":
  version: 5.9.2
  resolution: "typescript@patch:typescript@npm%3A5.9.2#optional!builtin<compat/typescript>::version=5.9.2&hash=5786d5"
  bin:
    tsc: bin/tsc
    tsserver: bin/tsserver
  checksum: 10/bd810ab13e8e557225a8b5122370385440b933e4e077d5c7641a8afd207fdc8be9c346e3c678adba934b64e0e70b0acf5eef9493ea05170a48ce22bef845fdc7
  languageName: node
  linkType: hard

"unbox-primitive@npm:^1.1.0":
  version: 1.1.0
  resolution: "unbox-primitive@npm:1.1.0"
  dependencies:
    call-bound: "npm:^1.0.3"
    has-bigints: "npm:^1.0.2"
    has-symbols: "npm:^1.1.0"
    which-boxed-primitive: "npm:^1.1.1"
  checksum: 10/fadb347020f66b2c8aeacf8b9a79826fa34cc5e5457af4eb0bbc4e79bd87fed0fa795949825df534320f7c13f199259516ad30abc55a6e7b91d8d996ca069e50
  languageName: node
  linkType: hard

"undici-types@npm:~5.26.4":
  version: 5.26.5
  resolution: "undici-types@npm:5.26.5"
  checksum: 10/0097779d94bc0fd26f0418b3a05472410408877279141ded2bd449167be1aed7ea5b76f756562cb3586a07f251b90799bab22d9019ceba49c037c76445f7cddd
  languageName: node
  linkType: hard

"undici-types@npm:~6.20.0":
  version: 6.20.0
  resolution: "undici-types@npm:6.20.0"
  checksum: 10/583ac7bbf4ff69931d3985f4762cde2690bb607844c16a5e2fbb92ed312fe4fa1b365e953032d469fa28ba8b224e88a595f0b10a449332f83fa77c695e567dbe
  languageName: node
  linkType: hard

"undici-types@npm:~7.10.0":
  version: 7.10.0
  resolution: "undici-types@npm:7.10.0"
  checksum: 10/1f3fe777937690ab8a7a7bccabc8fdf4b3171f4899b5a384fb5f3d6b56c4b5fec2a51fbf345c9dd002ff6716fd440a37fa8fdb0e13af8eca8889f25445875ba3
  languageName: node
  linkType: hard

"unicode-canonical-property-names-ecmascript@npm:^2.0.0":
  version: 2.0.1
  resolution: "unicode-canonical-property-names-ecmascript@npm:2.0.1"
  checksum: 10/3c3dabdb1d22aef4904399f9e810d0b71c0b12b3815169d96fac97e56d5642840c6071cf709adcace2252bc6bb80242396c2ec74b37224eb015c5f7aca40bad7
  languageName: node
  linkType: hard

"unicode-match-property-ecmascript@npm:^2.0.0":
  version: 2.0.0
  resolution: "unicode-match-property-ecmascript@npm:2.0.0"
  dependencies:
    unicode-canonical-property-names-ecmascript: "npm:^2.0.0"
    unicode-property-aliases-ecmascript: "npm:^2.0.0"
  checksum: 10/1f34a7434a23df4885b5890ac36c5b2161a809887000be560f56ad4b11126d433c0c1c39baf1016bdabed4ec54829a6190ee37aa24919aa116dc1a5a8a62965a
  languageName: node
  linkType: hard

"unicode-match-property-value-ecmascript@npm:^2.1.0":
  version: 2.2.0
  resolution: "unicode-match-property-value-ecmascript@npm:2.2.0"
  checksum: 10/9fd53c657aefe5d3cb8208931b4c34fbdb30bb5aa9a6c6bf744e2f3036f00b8889eeaf30cb55a873b76b6ee8b5801ea770e1c49b3352141309f58f0ebb3011d8
  languageName: node
  linkType: hard

"unicode-property-aliases-ecmascript@npm:^2.0.0":
  version: 2.1.0
  resolution: "unicode-property-aliases-ecmascript@npm:2.1.0"
  checksum: 10/243524431893649b62cc674d877bd64ef292d6071dd2fd01ab4d5ad26efbc104ffcd064f93f8a06b7e4ec54c172bf03f6417921a0d8c3a9994161fe1f88f815b
  languageName: node
  linkType: hard

"unicorn-magic@npm:^0.3.0":
  version: 0.3.0
  resolution: "unicorn-magic@npm:0.3.0"
  checksum: 10/bdd7d7c522f9456f32a0b77af23f8854f9a7db846088c3868ec213f9550683ab6a2bdf3803577eacbafddb4e06900974385841ccb75338d17346ccef45f9cb01
  languageName: node
  linkType: hard

"unique-filename@npm:^4.0.0":
  version: 4.0.0
  resolution: "unique-filename@npm:4.0.0"
  dependencies:
    unique-slug: "npm:^5.0.0"
  checksum: 10/6a62094fcac286b9ec39edbd1f8f64ff92383baa430af303dfed1ffda5e47a08a6b316408554abfddd9730c78b6106bef4ca4d02c1231a735ddd56ced77573df
  languageName: node
  linkType: hard

"unique-slug@npm:^5.0.0":
  version: 5.0.0
  resolution: "unique-slug@npm:5.0.0"
  dependencies:
    imurmurhash: "npm:^0.1.4"
  checksum: 10/beafdf3d6f44990e0a5ce560f8f881b4ee811be70b6ba0db25298c31c8cf525ed963572b48cd03be1c1349084f9e339be4241666d7cf1ebdad20598d3c652b27
  languageName: node
  linkType: hard

"unpipe@npm:1.0.0, unpipe@npm:~1.0.0":
  version: 1.0.0
  resolution: "unpipe@npm:1.0.0"
  checksum: 10/4fa18d8d8d977c55cb09715385c203197105e10a6d220087ec819f50cb68870f02942244f1017565484237f1f8c5d3cd413631b1ae104d3096f24fdfde1b4aa2
  languageName: node
  linkType: hard

"unplugin@npm:^2.1.2":
  version: 2.3.10
  resolution: "unplugin@npm:2.3.10"
  dependencies:
    "@jridgewell/remapping": "npm:^2.3.5"
    acorn: "npm:^8.15.0"
    picomatch: "npm:^4.0.3"
    webpack-virtual-modules: "npm:^0.6.2"
  checksum: 10/3d6d5bbc670311d78cad1a24b6961695c6c7ea7c9bc4558895693b388a635a5d1f16900b7b825f5962a5d1cb045f4e748f105058fdbb00d2f321ceaa9719da44
  languageName: node
  linkType: hard

"unrs-resolver@npm:^1.4.1":
  version: 1.11.1
  resolution: "unrs-resolver@npm:1.11.1"
  dependencies:
    "@unrs/resolver-binding-android-arm-eabi": "npm:1.11.1"
    "@unrs/resolver-binding-android-arm64": "npm:1.11.1"
    "@unrs/resolver-binding-darwin-arm64": "npm:1.11.1"
    "@unrs/resolver-binding-darwin-x64": "npm:1.11.1"
    "@unrs/resolver-binding-freebsd-x64": "npm:1.11.1"
    "@unrs/resolver-binding-linux-arm-gnueabihf": "npm:1.11.1"
    "@unrs/resolver-binding-linux-arm-musleabihf": "npm:1.11.1"
    "@unrs/resolver-binding-linux-arm64-gnu": "npm:1.11.1"
    "@unrs/resolver-binding-linux-arm64-musl": "npm:1.11.1"
    "@unrs/resolver-binding-linux-ppc64-gnu": "npm:1.11.1"
    "@unrs/resolver-binding-linux-riscv64-gnu": "npm:1.11.1"
    "@unrs/resolver-binding-linux-riscv64-musl": "npm:1.11.1"
    "@unrs/resolver-binding-linux-s390x-gnu": "npm:1.11.1"
    "@unrs/resolver-binding-linux-x64-gnu": "npm:1.11.1"
    "@unrs/resolver-binding-linux-x64-musl": "npm:1.11.1"
    "@unrs/resolver-binding-wasm32-wasi": "npm:1.11.1"
    "@unrs/resolver-binding-win32-arm64-msvc": "npm:1.11.1"
    "@unrs/resolver-binding-win32-ia32-msvc": "npm:1.11.1"
    "@unrs/resolver-binding-win32-x64-msvc": "npm:1.11.1"
    napi-postinstall: "npm:^0.3.0"
  dependenciesMeta:
    "@unrs/resolver-binding-android-arm-eabi":
      optional: true
    "@unrs/resolver-binding-android-arm64":
      optional: true
    "@unrs/resolver-binding-darwin-arm64":
      optional: true
    "@unrs/resolver-binding-darwin-x64":
      optional: true
    "@unrs/resolver-binding-freebsd-x64":
      optional: true
    "@unrs/resolver-binding-linux-arm-gnueabihf":
      optional: true
    "@unrs/resolver-binding-linux-arm-musleabihf":
      optional: true
    "@unrs/resolver-binding-linux-arm64-gnu":
      optional: true
    "@unrs/resolver-binding-linux-arm64-musl":
      optional: true
    "@unrs/resolver-binding-linux-ppc64-gnu":
      optional: true
    "@unrs/resolver-binding-linux-riscv64-gnu":
      optional: true
    "@unrs/resolver-binding-linux-riscv64-musl":
      optional: true
    "@unrs/resolver-binding-linux-s390x-gnu":
      optional: true
    "@unrs/resolver-binding-linux-x64-gnu":
      optional: true
    "@unrs/resolver-binding-linux-x64-musl":
      optional: true
    "@unrs/resolver-binding-wasm32-wasi":
      optional: true
    "@unrs/resolver-binding-win32-arm64-msvc":
      optional: true
    "@unrs/resolver-binding-win32-ia32-msvc":
      optional: true
    "@unrs/resolver-binding-win32-x64-msvc":
      optional: true
  checksum: 10/4de653508cbaae47883a896bd5cdfef0e5e87b428d62620d16fd35cd534beaebf08ebf0cf2f8b4922aa947b2fe745180facf6cc3f39ba364f7ce0f974cb06a70
  languageName: node
  linkType: hard

"update-browserslist-db@npm:^1.1.3":
  version: 1.1.3
  resolution: "update-browserslist-db@npm:1.1.3"
  dependencies:
    escalade: "npm:^3.2.0"
    picocolors: "npm:^1.1.1"
  peerDependencies:
    browserslist: ">= 4.21.0"
  bin:
    update-browserslist-db: cli.js
  checksum: 10/87af2776054ffb9194cf95e0201547d041f72ee44ce54b144da110e65ea7ca01379367407ba21de5c9edd52c74d95395366790de67f3eb4cc4afa0fe4424e76f
  languageName: node
  linkType: hard

"uri-js@npm:^4.2.2":
  version: 4.4.1
  resolution: "uri-js@npm:4.4.1"
  dependencies:
    punycode: "npm:^2.1.0"
  checksum: 10/b271ca7e3d46b7160222e3afa3e531505161c9a4e097febae9664e4b59912f4cbe94861361a4175edac3a03fee99d91e44b6a58c17a634bc5a664b19fc76fbcb
  languageName: node
  linkType: hard

"use-sync-external-store@npm:^1.5.0":
  version: 1.5.0
  resolution: "use-sync-external-store@npm:1.5.0"
  peerDependencies:
    react: ^16.8.0 || ^17.0.0 || ^18.0.0 || ^19.0.0
  checksum: 10/ddae7c4572511f7f641d6977bd0725340aa7dbeda8250418b54c1a57ec285083d96cf50d1a1acbd6cf729f7a87071b2302c6fbd29310432bf1b21a961a313279
  languageName: node
  linkType: hard

"utils-merge@npm:1.0.1":
  version: 1.0.1
  resolution: "utils-merge@npm:1.0.1"
  checksum: 10/5d6949693d58cb2e636a84f3ee1c6e7b2f9c16cb1d42d0ecb386d8c025c69e327205aa1c69e2868cc06a01e5e20681fbba55a4e0ed0cce913d60334024eae798
  languageName: node
  linkType: hard

"vary@npm:~1.1.2":
  version: 1.1.2
  resolution: "vary@npm:1.1.2"
  checksum: 10/31389debef15a480849b8331b220782230b9815a8e0dbb7b9a8369559aed2e9a7800cd904d4371ea74f4c3527db456dc8e7ac5befce5f0d289014dbdf47b2242
  languageName: node
  linkType: hard

"vite-node@npm:3.2.4":
  version: 3.2.4
  resolution: "vite-node@npm:3.2.4"
  dependencies:
    cac: "npm:^6.7.14"
    debug: "npm:^4.4.1"
    es-module-lexer: "npm:^1.7.0"
    pathe: "npm:^2.0.3"
    vite: "npm:^5.0.0 || ^6.0.0 || ^7.0.0-0"
  bin:
    vite-node: vite-node.mjs
  checksum: 10/343244ecabbab3b6e1a3065dabaeefa269965a7a7c54652d4b7a7207ee82185e887af97268c61755dcb2dd6a6ce5d9e114400cbd694229f38523e935703cc62f
  languageName: node
  linkType: hard

"vite-plugin-checker@npm:0.10.2":
  version: 0.10.2
  resolution: "vite-plugin-checker@npm:0.10.2"
  dependencies:
    "@babel/code-frame": "npm:^7.27.1"
    chokidar: "npm:^4.0.3"
    npm-run-path: "npm:^6.0.0"
    picocolors: "npm:^1.1.1"
    picomatch: "npm:^4.0.3"
    strip-ansi: "npm:^7.1.0"
    tiny-invariant: "npm:^1.3.3"
    tinyglobby: "npm:^0.2.14"
    vscode-uri: "npm:^3.1.0"
  peerDependencies:
    "@biomejs/biome": ">=1.7"
    eslint: ">=7"
    meow: ^13.2.0
    optionator: ^0.9.4
    stylelint: ">=16"
    typescript: "*"
    vite: ">=2.0.0"
    vls: "*"
    vti: "*"
    vue-tsc: ~2.2.10 || ^3.0.0
  peerDependenciesMeta:
    "@biomejs/biome":
      optional: true
    eslint:
      optional: true
    meow:
      optional: true
    optionator:
      optional: true
    stylelint:
      optional: true
    typescript:
      optional: true
    vls:
      optional: true
    vti:
      optional: true
    vue-tsc:
      optional: true
  checksum: 10/8b8e82ef37b04fa6bbe6d2ae62b50f6a13e0f459a1f3a9b23905e722cda6e538ac227894c25ba43a988f8b2ae85b4e12a4a0fd0aa25579d4b1311710302d2fc6
  languageName: node
  linkType: hard

"vite-plugin-css-injected-by-js@npm:3.5.2":
  version: 3.5.2
  resolution: "vite-plugin-css-injected-by-js@npm:3.5.2"
  peerDependencies:
    vite: ">2.0.0-0"
  checksum: 10/be1ca1c71c31042dbca49d78ed58c88d3ffbbcf84024120f72d1daeebd489ad0ec4a0f1179adfeb196052d52b09226bff7d0979e0ef0be3e87e47b15eb728270
  languageName: node
  linkType: hard

"vite-plugin-dynamic-base@npm:1.0.0":
  version: 1.0.0
  resolution: "vite-plugin-dynamic-base@npm:1.0.0"
  dependencies:
    "@swc/core": "npm:^1.3.61"
    node-html-parser: "npm:^5.3.3"
  checksum: 10/0ac463526517528b6fb5cc3c62acc7d1e091c6634e424966af3225edc247aea0c754b7e2555e2a75b9e19c5da43d205a6a48e195c0a142e4998709c96e3f561c
  languageName: node
  linkType: hard

"vite-plugin-mkcert@npm:1.17.8":
  version: 1.17.8
  resolution: "vite-plugin-mkcert@npm:1.17.8"
  dependencies:
    axios: "npm:^1.8.3"
    debug: "npm:^4.4.0"
    picocolors: "npm:^1.1.1"
  peerDependencies:
    vite: ">=3"
  checksum: 10/7b26333c737ecdd5e1f3663202938839dc07e897f553f8d82597830db3d8a6d40b7d07e1b51e6bae2d7b02c28650589c55984dea63b2dec1f2f41eb650875c64
  languageName: node
  linkType: hard

"vite-plugin-mock@npm:3.0.2":
  version: 3.0.2
  resolution: "vite-plugin-mock@npm:3.0.2"
  dependencies:
    bundle-require: "npm:^4.0.1"
    chokidar: "npm:^3.5.3"
    connect: "npm:^3.7.0"
    debug: "npm:^4.3.4"
    fast-glob: "npm:^3.2.12"
    path-to-regexp: "npm:^6.2.1"
    picocolors: "npm:^1.0.0"
  peerDependencies:
    esbuild: ">=0.17"
    mockjs: ">=1.1.0"
    vite: ">=4.0.0"
  checksum: 10/c00a92e6481e59a5a62fa845ae1c4fe728b94bf4a270ca56097e908a4c6809c28188a6195a4abf62b0a55bebebf3bb1e1ec71d71ba26d9c2e985a504a73b1e08
  languageName: node
  linkType: hard

"vite-plugin-svgr@npm:4.3.0":
  version: 4.3.0
  resolution: "vite-plugin-svgr@npm:4.3.0"
  dependencies:
    "@rollup/pluginutils": "npm:^5.1.3"
    "@svgr/core": "npm:^8.1.0"
    "@svgr/plugin-jsx": "npm:^8.1.0"
  peerDependencies:
    vite: ">=2.6.0"
  checksum: 10/9ade316f20dae881f4ee65e4f2a35be11cf75b22a411bfcdb55bd61382c0249395cb925775e06a49e0fdffe483e64d5a25068c3ddfc5823fb72013cf4d932d17
  languageName: node
  linkType: hard

"vite-tsconfig-paths@npm:5.1.4":
  version: 5.1.4
  resolution: "vite-tsconfig-paths@npm:5.1.4"
  dependencies:
    debug: "npm:^4.1.1"
    globrex: "npm:^0.1.2"
    tsconfck: "npm:^3.0.3"
  peerDependencies:
    vite: "*"
  peerDependenciesMeta:
    vite:
      optional: true
  checksum: 10/b409dbd17829f560021a71dba3e473b9c06dcf5fdc9d630b72c1f787145ec478b38caff1be04868971ac8bdcbf0f5af45eeece23dbc9c59c54b901f867740ae0
  languageName: node
  linkType: hard

"vite@npm:7.1.3":
  version: 7.1.3
  resolution: "vite@npm:7.1.3"
  dependencies:
    esbuild: "npm:^0.25.0"
    fdir: "npm:^6.5.0"
    fsevents: "npm:~2.3.3"
    picomatch: "npm:^4.0.3"
    postcss: "npm:^8.5.6"
    rollup: "npm:^4.43.0"
    tinyglobby: "npm:^0.2.14"
  peerDependencies:
    "@types/node": ^20.19.0 || >=22.12.0
    jiti: ">=1.21.0"
    less: ^4.0.0
    lightningcss: ^1.21.0
    sass: ^1.70.0
    sass-embedded: ^1.70.0
    stylus: ">=0.54.8"
    sugarss: ^5.0.0
    terser: ^5.16.0
    tsx: ^4.8.1
    yaml: ^2.4.2
  dependenciesMeta:
    fsevents:
      optional: true
  peerDependenciesMeta:
    "@types/node":
      optional: true
    jiti:
      optional: true
    less:
      optional: true
    lightningcss:
      optional: true
    sass:
      optional: true
    sass-embedded:
      optional: true
    stylus:
      optional: true
    sugarss:
      optional: true
    terser:
      optional: true
    tsx:
      optional: true
    yaml:
      optional: true
  bin:
    vite: bin/vite.js
  checksum: 10/792cba6d5090cc6713a1f72ee43578902df0e231db0b4d313589ec380919d3612a01903615e183ebca6a903f83ee4fbfceb3d07a5443d5698d5d992dbc60a995
  languageName: node
  linkType: hard

"vite@npm:^3.0.0":
  version: 3.2.11
  resolution: "vite@npm:3.2.11"
  dependencies:
    esbuild: "npm:^0.15.9"
    fsevents: "npm:~2.3.2"
    postcss: "npm:^8.4.18"
    resolve: "npm:^1.22.1"
    rollup: "npm:^2.79.1"
  peerDependencies:
    "@types/node": ">= 14"
    less: "*"
    sass: "*"
    stylus: "*"
    sugarss: "*"
    terser: ^5.4.0
  dependenciesMeta:
    fsevents:
      optional: true
  peerDependenciesMeta:
    "@types/node":
      optional: true
    less:
      optional: true
    sass:
      optional: true
    stylus:
      optional: true
    sugarss:
      optional: true
    terser:
      optional: true
  bin:
    vite: bin/vite.js
  checksum: 10/3933d93165235001e79c99b69434a91a85404572d698c5160e910237d9ae3833ecea0dcfe1ca2967543e6bf28b419f90c3cb4899782ec9d43bf17ca2accaf361
  languageName: node
  linkType: hard

"vite@npm:^5.0.0 || ^6.0.0 || ^7.0.0-0":
  version: 7.1.4
  resolution: "vite@npm:7.1.4"
  dependencies:
    esbuild: "npm:^0.25.0"
    fdir: "npm:^6.5.0"
    fsevents: "npm:~2.3.3"
    picomatch: "npm:^4.0.3"
    postcss: "npm:^8.5.6"
    rollup: "npm:^4.43.0"
    tinyglobby: "npm:^0.2.14"
  peerDependencies:
    "@types/node": ^20.19.0 || >=22.12.0
    jiti: ">=1.21.0"
    less: ^4.0.0
    lightningcss: ^1.21.0
    sass: ^1.70.0
    sass-embedded: ^1.70.0
    stylus: ">=0.54.8"
    sugarss: ^5.0.0
    terser: ^5.16.0
    tsx: ^4.8.1
    yaml: ^2.4.2
  dependenciesMeta:
    fsevents:
      optional: true
  peerDependenciesMeta:
    "@types/node":
      optional: true
    jiti:
      optional: true
    less:
      optional: true
    lightningcss:
      optional: true
    sass:
      optional: true
    sass-embedded:
      optional: true
    stylus:
      optional: true
    sugarss:
      optional: true
    terser:
      optional: true
    tsx:
      optional: true
    yaml:
      optional: true
  bin:
    vite: bin/vite.js
  checksum: 10/40c6292227e8177470b9f1014fabe2fa324222d6bdc06eb994f7aa6872a56790cd1e45a2f7017a381413eb2fde48fde0f2f58933a045936ca6af2061862bf338
  languageName: node
  linkType: hard

"vitest-preview@npm:^0.0.1":
  version: 0.0.1
  resolution: "vitest-preview@npm:0.0.1"
  dependencies:
    "@types/express": "npm:^4.17.14"
    "@types/node": "npm:^18.11.3"
    "@vitest-preview/dev-utils": "npm:0.0.1"
    express: "npm:^4.18.2"
    vite: "npm:^3.0.0"
  bin:
    vitest-preview: dist/previewServer.mjs
  checksum: 10/a723c132a866d2ccbf0360ec0f08e947c95eea53d94cefc4f630f846dcd01dcdfb0f11bab36412039c84092675fd879ab61ea9686f0e77b08d965b2aca748803
  languageName: node
  linkType: hard

"vitest@npm:3.2.4":
  version: 3.2.4
  resolution: "vitest@npm:3.2.4"
  dependencies:
    "@types/chai": "npm:^5.2.2"
    "@vitest/expect": "npm:3.2.4"
    "@vitest/mocker": "npm:3.2.4"
    "@vitest/pretty-format": "npm:^3.2.4"
    "@vitest/runner": "npm:3.2.4"
    "@vitest/snapshot": "npm:3.2.4"
    "@vitest/spy": "npm:3.2.4"
    "@vitest/utils": "npm:3.2.4"
    chai: "npm:^5.2.0"
    debug: "npm:^4.4.1"
    expect-type: "npm:^1.2.1"
    magic-string: "npm:^0.30.17"
    pathe: "npm:^2.0.3"
    picomatch: "npm:^4.0.2"
    std-env: "npm:^3.9.0"
    tinybench: "npm:^2.9.0"
    tinyexec: "npm:^0.3.2"
    tinyglobby: "npm:^0.2.14"
    tinypool: "npm:^1.1.1"
    tinyrainbow: "npm:^2.0.0"
    vite: "npm:^5.0.0 || ^6.0.0 || ^7.0.0-0"
    vite-node: "npm:3.2.4"
    why-is-node-running: "npm:^2.3.0"
  peerDependencies:
    "@edge-runtime/vm": "*"
    "@types/debug": ^4.1.12
    "@types/node": ^18.0.0 || ^20.0.0 || >=22.0.0
    "@vitest/browser": 3.2.4
    "@vitest/ui": 3.2.4
    happy-dom: "*"
    jsdom: "*"
  peerDependenciesMeta:
    "@edge-runtime/vm":
      optional: true
    "@types/debug":
      optional: true
    "@types/node":
      optional: true
    "@vitest/browser":
      optional: true
    "@vitest/ui":
      optional: true
    happy-dom:
      optional: true
    jsdom:
      optional: true
  bin:
    vitest: vitest.mjs
  checksum: 10/f10bbce093ecab310ecbe484536ef4496fb9151510b2be0c5907c65f6d31482d9c851f3182531d1d27d558054aa78e8efd9d4702ba6c82058657e8b6a52507ee
  languageName: node
  linkType: hard

"vscode-json-languageservice@npm:^4.1.6":
  version: 4.2.1
  resolution: "vscode-json-languageservice@npm:4.2.1"
  dependencies:
    jsonc-parser: "npm:^3.0.0"
    vscode-languageserver-textdocument: "npm:^1.0.3"
    vscode-languageserver-types: "npm:^3.16.0"
    vscode-nls: "npm:^5.0.0"
    vscode-uri: "npm:^3.0.3"
  checksum: 10/ebd3dd037cfb5ede73eaf5c634463aa2609014d544b6b97505d6e062e9bc8cb80f049d52939354a27acd7d26f48af3d95e6546598d60b1a384e6877f1eca7d0c
  languageName: node
  linkType: hard

"vscode-languageserver-textdocument@npm:^1.0.3":
  version: 1.0.12
  resolution: "vscode-languageserver-textdocument@npm:1.0.12"
  checksum: 10/2bc0fde952d40f35a31179623d1491b0fafdee156aaf58557f40f5d394a25fc84826763cdde55fa6ce2ed9cd35a931355ad6dd7fe5db82e7f21e5d865f0af8c6
  languageName: node
  linkType: hard

"vscode-languageserver-types@npm:^3.16.0":
  version: 3.17.5
  resolution: "vscode-languageserver-types@npm:3.17.5"
  checksum: 10/900d0b81df5bef8d90933e75be089142f6989cc70fdb2d5a3a5f11fa20feb396aaea23ccffc8fbcc83a2f0e1b13c6ee48ff8151f236cbd6e61a4f856efac1a58
  languageName: node
  linkType: hard

"vscode-nls@npm:^5.0.0":
  version: 5.2.0
  resolution: "vscode-nls@npm:5.2.0"
  checksum: 10/7dd0d07e5f1fd7a7d1b35eabea20322e2efef4e93cebe6de45ee015ea0152ec1c354fd763d3987aed1db261f466ff31631d6acd679ac67af608fb49b9868e1ab
  languageName: node
  linkType: hard

"vscode-uri@npm:^3.0.3, vscode-uri@npm:^3.1.0":
  version: 3.1.0
  resolution: "vscode-uri@npm:3.1.0"
  checksum: 10/80c2a2421f44b64008ef1f91dfa52a2d68105cbb4dcea197dbf5b00c65ccaccf218b615e93ec587f26fc3ba04796898f3631a9406e3b04cda970c3ca8eadf646
  languageName: node
  linkType: hard

"w3c-xmlserializer@npm:^5.0.0":
  version: 5.0.0
  resolution: "w3c-xmlserializer@npm:5.0.0"
  dependencies:
    xml-name-validator: "npm:^5.0.0"
  checksum: 10/d78f59e6b4f924aa53b6dfc56949959229cae7fe05ea9374eb38d11edcec01398b7f5d7a12576bd5acc57ff446abb5c9115cd83b9d882555015437cf858d42f0
  languageName: node
  linkType: hard

"webidl-conversions@npm:^3.0.0":
  version: 3.0.1
  resolution: "webidl-conversions@npm:3.0.1"
  checksum: 10/b65b9f8d6854572a84a5c69615152b63371395f0c5dcd6729c45789052296df54314db2bc3e977df41705eacb8bc79c247cee139a63fa695192f95816ed528ad
  languageName: node
  linkType: hard

"webidl-conversions@npm:^7.0.0":
  version: 7.0.0
  resolution: "webidl-conversions@npm:7.0.0"
  checksum: 10/4c4f65472c010eddbe648c11b977d048dd96956a625f7f8b9d64e1b30c3c1f23ea1acfd654648426ce5c743c2108a5a757c0592f02902cf7367adb7d14e67721
  languageName: node
  linkType: hard

"webpack-virtual-modules@npm:^0.6.2":
  version: 0.6.2
  resolution: "webpack-virtual-modules@npm:0.6.2"
  checksum: 10/d9a0d035f7ec0c7f1055aaf88bfe48b7f96458043916a1b2926d9012fd61de3810a6b768e31a8cd4b3c84a9b6d55824361a9dd20aaf9f5ccfb6f017af216a178
  languageName: node
  linkType: hard

"whatwg-encoding@npm:^3.1.1":
  version: 3.1.1
  resolution: "whatwg-encoding@npm:3.1.1"
  dependencies:
    iconv-lite: "npm:0.6.3"
  checksum: 10/bbef815eb67f91487c7f2ef96329743f5fd8357d7d62b1119237d25d41c7e452dff8197235b2d3c031365a17f61d3bb73ca49d0ed1582475aa4a670815e79534
  languageName: node
  linkType: hard

"whatwg-fetch@npm:^3.4.1":
  version: 3.6.20
  resolution: "whatwg-fetch@npm:3.6.20"
  checksum: 10/2b4ed92acd6a7ad4f626a6cb18b14ec982bbcaf1093e6fe903b131a9c6decd14d7f9c9ca3532663c2759d1bdf01d004c77a0adfb2716a5105465c20755a8c57c
  languageName: node
  linkType: hard

"whatwg-mimetype@npm:^4.0.0":
  version: 4.0.0
  resolution: "whatwg-mimetype@npm:4.0.0"
  checksum: 10/894a618e2d90bf444b6f309f3ceb6e58cf21b2beaa00c8b333696958c4076f0c7b30b9d33413c9ffff7c5832a0a0c8569e5bb347ef44beded72aeefd0acd62e8
  languageName: node
  linkType: hard

"whatwg-url@npm:^14.0.0, whatwg-url@npm:^14.1.1":
  version: 14.2.0
  resolution: "whatwg-url@npm:14.2.0"
  dependencies:
    tr46: "npm:^5.1.0"
    webidl-conversions: "npm:^7.0.0"
  checksum: 10/f0a95b0601c64f417c471536a2d828b4c16fe37c13662483a32f02f183ed0f441616609b0663fb791e524e8cd56d9a86dd7366b1fc5356048ccb09b576495e7c
  languageName: node
  linkType: hard

"whatwg-url@npm:^5.0.0":
  version: 5.0.0
  resolution: "whatwg-url@npm:5.0.0"
  dependencies:
    tr46: "npm:~0.0.3"
    webidl-conversions: "npm:^3.0.0"
  checksum: 10/f95adbc1e80820828b45cc671d97da7cd5e4ef9deb426c31bcd5ab00dc7103042291613b3ef3caec0a2335ed09e0d5ed026c940755dbb6d404e2b27f940fdf07
  languageName: node
  linkType: hard

"which-boxed-primitive@npm:^1.0.2, which-boxed-primitive@npm:^1.1.0, which-boxed-primitive@npm:^1.1.1":
  version: 1.1.1
  resolution: "which-boxed-primitive@npm:1.1.1"
  dependencies:
    is-bigint: "npm:^1.1.0"
    is-boolean-object: "npm:^1.2.1"
    is-number-object: "npm:^1.1.1"
    is-string: "npm:^1.1.1"
    is-symbol: "npm:^1.1.1"
  checksum: 10/a877c0667bc089518c83ad4d845cf8296b03efe3565c1de1940c646e00a2a1ae9ed8a185bcfa27cbf352de7906f0616d83b9d2f19ca500ee02a551fb5cf40740
  languageName: node
  linkType: hard

"which-builtin-type@npm:^1.2.1":
  version: 1.2.1
  resolution: "which-builtin-type@npm:1.2.1"
  dependencies:
    call-bound: "npm:^1.0.2"
    function.prototype.name: "npm:^1.1.6"
    has-tostringtag: "npm:^1.0.2"
    is-async-function: "npm:^2.0.0"
    is-date-object: "npm:^1.1.0"
    is-finalizationregistry: "npm:^1.1.0"
    is-generator-function: "npm:^1.0.10"
    is-regex: "npm:^1.2.1"
    is-weakref: "npm:^1.0.2"
    isarray: "npm:^2.0.5"
    which-boxed-primitive: "npm:^1.1.0"
    which-collection: "npm:^1.0.2"
    which-typed-array: "npm:^1.1.16"
  checksum: 10/22c81c5cb7a896c5171742cd30c90d992ff13fb1ea7693e6cf80af077791613fb3f89aa9b4b7f890bd47b6ce09c6322c409932359580a2a2a54057f7b52d1cbe
  languageName: node
  linkType: hard

"which-collection@npm:^1.0.1, which-collection@npm:^1.0.2":
  version: 1.0.2
  resolution: "which-collection@npm:1.0.2"
  dependencies:
    is-map: "npm:^2.0.3"
    is-set: "npm:^2.0.3"
    is-weakmap: "npm:^2.0.2"
    is-weakset: "npm:^2.0.3"
  checksum: 10/674bf659b9bcfe4055f08634b48a8588e879161b9fefed57e9ec4ff5601e4d50a05ccd76cf10f698ef5873784e5df3223336d56c7ce88e13bcf52ebe582fc8d7
  languageName: node
  linkType: hard

"which-typed-array@npm:^1.1.13, which-typed-array@npm:^1.1.16, which-typed-array@npm:^1.1.19":
  version: 1.1.19
  resolution: "which-typed-array@npm:1.1.19"
  dependencies:
    available-typed-arrays: "npm:^1.0.7"
    call-bind: "npm:^1.0.8"
    call-bound: "npm:^1.0.4"
    for-each: "npm:^0.3.5"
    get-proto: "npm:^1.0.1"
    gopd: "npm:^1.2.0"
    has-tostringtag: "npm:^1.0.2"
  checksum: 10/12be30fb88567f9863186bee1777f11bea09dd59ed8b3ce4afa7dd5cade75e2f4cc56191a2da165113cc7cf79987ba021dac1e22b5b62aa7e5c56949f2469a68
  languageName: node
  linkType: hard

"which@npm:^2.0.1":
  version: 2.0.2
  resolution: "which@npm:2.0.2"
  dependencies:
    isexe: "npm:^2.0.0"
  bin:
    node-which: ./bin/node-which
  checksum: 10/4782f8a1d6b8fc12c65e968fea49f59752bf6302dc43036c3bf87da718a80710f61a062516e9764c70008b487929a73546125570acea95c5b5dcc8ac3052c70f
  languageName: node
  linkType: hard

"which@npm:^5.0.0":
  version: 5.0.0
  resolution: "which@npm:5.0.0"
  dependencies:
    isexe: "npm:^3.1.1"
  bin:
    node-which: bin/which.js
  checksum: 10/6ec99e89ba32c7e748b8a3144e64bfc74aa63e2b2eacbb61a0060ad0b961eb1a632b08fb1de067ed59b002cec3e21de18299216ebf2325ef0f78e0f121e14e90
  languageName: node
  linkType: hard

"why-is-node-running@npm:^2.3.0":
  version: 2.3.0
  resolution: "why-is-node-running@npm:2.3.0"
  dependencies:
    siginfo: "npm:^2.0.0"
    stackback: "npm:0.0.2"
  bin:
    why-is-node-running: cli.js
  checksum: 10/0de6e6cd8f2f94a8b5ca44e84cf1751eadcac3ebedcdc6e5fbbe6c8011904afcbc1a2777c53496ec02ced7b81f2e7eda61e76bf8262a8bc3ceaa1f6040508051
  languageName: node
  linkType: hard

"word-wrap@npm:^1.2.5":
  version: 1.2.5
  resolution: "word-wrap@npm:1.2.5"
  checksum: 10/1ec6f6089f205f83037be10d0c4b34c9183b0b63fca0834a5b3cee55dd321429d73d40bb44c8fc8471b5203d6e8f8275717f49a8ff4b2b0ab41d7e1b563e0854
  languageName: node
  linkType: hard

"wrap-ansi-cjs@npm:wrap-ansi@^7.0.0":
  version: 7.0.0
  resolution: "wrap-ansi@npm:7.0.0"
  dependencies:
    ansi-styles: "npm:^4.0.0"
    string-width: "npm:^4.1.0"
    strip-ansi: "npm:^6.0.0"
  checksum: 10/cebdaeca3a6880da410f75209e68cd05428580de5ad24535f22696d7d9cab134d1f8498599f344c3cf0fb37c1715807a183778d8c648d6cc0cb5ff2bb4236540
  languageName: node
  linkType: hard

"wrap-ansi@npm:^8.1.0":
  version: 8.1.0
  resolution: "wrap-ansi@npm:8.1.0"
  dependencies:
    ansi-styles: "npm:^6.1.0"
    string-width: "npm:^5.0.1"
    strip-ansi: "npm:^7.0.1"
  checksum: 10/7b1e4b35e9bb2312d2ee9ee7dc95b8cb5f8b4b5a89f7dde5543fe66c1e3715663094defa50d75454ac900bd210f702d575f15f3f17fa9ec0291806d2578d1ddf
  languageName: node
  linkType: hard

"ws@npm:^8.18.0":
  version: 8.18.3
  resolution: "ws@npm:8.18.3"
  peerDependencies:
    bufferutil: ^4.0.1
    utf-8-validate: ">=5.0.2"
  peerDependenciesMeta:
    bufferutil:
      optional: true
    utf-8-validate:
      optional: true
  checksum: 10/725964438d752f0ab0de582cd48d6eeada58d1511c3f613485b5598a83680bedac6187c765b0fe082e2d8cc4341fc57707c813ae780feee82d0c5efe6a4c61b6
  languageName: node
  linkType: hard

"xml-name-validator@npm:^5.0.0":
  version: 5.0.0
  resolution: "xml-name-validator@npm:5.0.0"
  checksum: 10/43f30f3f6786e406dd665acf08cd742d5f8a46486bd72517edb04b27d1bcd1599664c2a4a99fc3f1e56a3194bff588b12f178b7972bc45c8047bdc4c3ac8d4a1
  languageName: node
  linkType: hard

"xmlchars@npm:^2.2.0":
  version: 2.2.0
  resolution: "xmlchars@npm:2.2.0"
  checksum: 10/4ad5924974efd004a47cce6acf5c0269aee0e62f9a805a426db3337af7bcbd331099df174b024ace4fb18971b8a56de386d2e73a1c4b020e3abd63a4a9b917f1
  languageName: node
  linkType: hard

"yallist@npm:^3.0.2":
  version: 3.1.1
  resolution: "yallist@npm:3.1.1"
  checksum: 10/9af0a4329c3c6b779ac4736c69fae4190ac03029fa27c1aef4e6bcc92119b73dea6fe5db5fe881fb0ce2a0e9539a42cdf60c7c21eda04d1a0b8c082e38509efb
  languageName: node
  linkType: hard

"yallist@npm:^4.0.0":
  version: 4.0.0
  resolution: "yallist@npm:4.0.0"
  checksum: 10/4cb02b42b8a93b5cf50caf5d8e9beb409400a8a4d85e83bb0685c1457e9ac0b7a00819e9f5991ac25ffabb56a78e2f017c1acc010b3a1babfe6de690ba531abd
  languageName: node
  linkType: hard

"yallist@npm:^5.0.0":
  version: 5.0.0
  resolution: "yallist@npm:5.0.0"
  checksum: 10/1884d272d485845ad04759a255c71775db0fac56308764b4c77ea56a20d56679fad340213054c8c9c9c26fcfd4c4b2a90df993b7e0aaf3cdb73c618d1d1a802a
  languageName: node
  linkType: hard

"yocto-queue@npm:^0.1.0":
  version: 0.1.0
  resolution: "yocto-queue@npm:0.1.0"
  checksum: 10/f77b3d8d00310def622123df93d4ee654fc6a0096182af8bd60679ddcdfb3474c56c6c7190817c84a2785648cdee9d721c0154eb45698c62176c322fb46fc700
  languageName: node
  linkType: hard

"zod@npm:^3.24.2":
  version: 3.25.76
  resolution: "zod@npm:3.25.76"
  checksum: 10/f0c963ec40cd96858451d1690404d603d36507c1fc9682f2dae59ab38b578687d542708a7fdbf645f77926f78c9ed558f57c3d3aa226c285f798df0c4da16995
  languageName: node
  linkType: hard
