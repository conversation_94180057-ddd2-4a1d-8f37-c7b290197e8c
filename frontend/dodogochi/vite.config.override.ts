import { defineConfig } from '@dodopizza/frontend-scripts';
import TanStackRouterVite from '@tanstack/router-plugin/vite';

export default defineConfig({
	plugins: [
		TanStackRouterVite({
			target: 'react',
			autoCodeSplitting: true,
			routesDirectory: './src/routes',
			generatedRouteTree: './src/app/routes.gen.ts',
		}),
	],
	server: {
		proxy: {
			// eslint-disable-next-line @typescript-eslint/naming-convention
			'^/api': {
				// eslint-disable-next-line sonarjs/no-clear-text-protocols
				target: 'http://hackadatabot-webapi.crm:8080',
				changeOrigin: true,
				ws: true,
				secure: false,
			},
		},
	},
});
