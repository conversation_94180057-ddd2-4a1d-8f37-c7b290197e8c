import { type ReactNode } from 'react';
import { createFileRoute } from '@tanstack/react-router';

import { LayoutStyled } from 'shared/ui/Layout/Layout.styles';
import { TransactionsHistory } from 'widgets/TransactionsHistory/TransactionsHistory';
import { UserStatistics } from 'widgets/UserStatistics/UserStatistics';
import { ContainerWrapperStyled } from './index.styles';

export const Route = createFileRoute('/')({
	component: Index,
});

function Index(): ReactNode {
	return (
		<LayoutStyled>
			<ContainerWrapperStyled>
				<UserStatistics />
				<TransactionsHistory />
			</ContainerWrapperStyled>
		</LayoutStyled>
	);
}
