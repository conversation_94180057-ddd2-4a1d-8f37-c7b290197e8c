import styled from 'styled-components';

export const ButtonStyled = styled.button`
	position: relative;
	padding: 16px 32px;
	width: 100%;
	border: 1px solid #2d5a73;
	border-radius: 50px;
	background: linear-gradient(145deg, #4a7a8f 0%, #2d5a73 100%);
	box-shadow:
		inset 0 2px 4px rgba(125, 211, 252, 0.2),
		0 4px 8px rgba(0, 0, 0, 0.3);
	color: #7dd3fc;
	text-transform: uppercase;
	font-weight: 600;
	font-size: 16px;
	font-family: 'Inter', sans-serif;
	cursor: pointer;
	transition: all 0.2s ease;

	@media (max-width: 768px) {
		padding: 14px 24px;
		font-size: 15px;
	}

	@media (max-width: 480px) {
		padding: 12px 20px;
		font-size: 14px;
	}

	&:hover {
		background: linear-gradient(145deg, #5a8aa5 0%, #3d6a83 100%);
		box-shadow:
			inset 0 2px 4px rgba(125, 211, 252, 0.3),
			0 6px 12px rgba(0, 0, 0, 0.4);
		transform: translateY(-1px);
	}

	&:active {
		box-shadow:
			inset 0 2px 4px rgba(125, 211, 252, 0.15),
			0 2px 4px rgba(0, 0, 0, 0.2);
		transform: translateY(0);
	}
`;
