import { createLoggerInstanceRef } from '@dodobrands/react-logger';

export const logger = createLoggerInstanceRef({
	applicationName: 'dodogochi',
	// @ts-expect-error later
	// eslint-disable-next-line @typescript-eslint/no-unsafe-assignment, @typescript-eslint/no-unsafe-member-access
	isProduction: import.meta.env.PROD,
	remoteUrl: window.initParams?.frontendLoggerUrl ?? 'https://frontlogger.d.yandex.dodois.dev',
	persistentQueue: false,
});
