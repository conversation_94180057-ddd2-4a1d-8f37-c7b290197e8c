import styled from 'styled-components';

export const WrapperStyled = styled.div`
	display: flex;
	flex-direction: column;
	padding: 24px;
	border-radius: 36px;
	background: rgba(0, 21, 47, 255);
	box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
`;

export const TitleStyled = styled.h2`
	margin: 0 0 16px 0;
	color: rgba(0, 239, 243, 255);
	font-weight: 600;
	font-size: 36px;
	font-family: 'Inter', sans-serif;
`;

export const ContentWrapperStyled = styled.ul`
	gap: 12px;
	display: flex;
	flex: 1;
	flex-direction: column;
	overflow-y: auto;
	margin: 0;
	padding: 0;
	max-height: 400px;
	list-style: none;

	&::-webkit-scrollbar {
		width: 6px;
	}

	&::-webkit-scrollbar-track {
		border-radius: 3px;
		background: #f1f5f9;
	}

	&::-webkit-scrollbar-thumb {
		border-radius: 3px;
		background: #cbd5e1;
	}

	&::-webkit-scrollbar-thumb:hover {
		background: #94a3b8;
	}
`;

export const TransactionItemStyled = styled.li`
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 16px;
	border-radius: 8px;
	background: linear-gradient(145deg, #1e3a52 0%, #0f2235 100%);
	color: #7dd3fc;

	transition: all 0.2s ease;
`;

export const TransactionTextStyled = styled.span`
	font-weight: 500;
	font-size: 14px;
`;

export const TransactionAmountStyled = styled.span<{ isPositive: boolean }>`
	color: ${props => (props.isPositive ? '#16a34a' : '#dc2626')};
	font-weight: 600;
	font-size: 14px;
`;
