import { type FC } from 'react';

import {
	ContentWrapperStyled,
	TitleStyled,
	TransactionAmountStyled,
	TransactionItemStyled,
	TransactionTextStyled,
	WrapperStyled,
} from 'widgets/TransactionsHistory/TransactionsHistory.styles';

const transactions = [
	{ text: 'Completed daily task', amount: 50, isPositive: true },
	{ text: 'Fed your pet', amount: -25, isPositive: false },
	{ text: 'Won mini-game', amount: 100, isPositive: true },
	{ text: 'Bought new item', amount: -75, isPositive: false },
	{ text: 'Weekly bonus', amount: 200, isPositive: true },
	{ text: 'Pet training', amount: -30, isPositive: false },
	{ text: 'Achievement unlocked', amount: 150, isPositive: true },
	{ text: 'Medicine purchase', amount: -40, isPositive: false },
	{ text: 'Daily login reward', amount: 25, isPositive: true },
	{ text: 'Special event bonus', amount: 300, isPositive: true },
];

export const TransactionsHistory: FC = () => (
	<WrapperStyled>
		<TitleStyled>Transactions</TitleStyled>
		<ContentWrapperStyled>
			{transactions.map((transaction, index) => (
				<TransactionItemStyled key={index}>
					<TransactionTextStyled>{transaction.text}</TransactionTextStyled>
					<TransactionAmountStyled isPositive={transaction.isPositive}>
						{transaction.isPositive ? '+' : ''}
						{transaction.amount}
					</TransactionAmountStyled>
				</TransactionItemStyled>
			))}
		</ContentWrapperStyled>
	</WrapperStyled>
);
