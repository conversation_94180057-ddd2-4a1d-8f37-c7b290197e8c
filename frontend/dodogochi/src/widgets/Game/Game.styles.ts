import styled from 'styled-components';

export const GameWrapperStyled = styled.div`
	@import url('https://fonts.googleapis.com/css?family=Press+Start+2P');
	inset: 0;
	position: fixed;
	z-index: 1000;
	background-color: rgba(0, 0, 0, 0.8);

	#close {
		position: absolute;
		top: 20px;
		right: 20px;
		padding: 10px 20px;
		background-color: red;
		color: white;
		font-size: inherit;
		font-family: inherit;
		cursor: pointer;
	}

	canvas {
		width: 100%;
		height: 100%;
	}

	#controls {
		position: absolute;
		bottom: 20px;
		display: flex;
		justify-content: center;
		align-items: flex-end;
		min-width: 100%;
	}

	#controls div {
		grid-template-columns: 50px 50px 50px;
		gap: 10px;
		display: grid;
	}

	#controls button {
		width: 100%;
		height: 40px;
		outline: none;
		border: 1px solid lightgray;
		background-color: white;
		box-shadow: 3px 5px 0px 0px rgba(0, 0, 0, 0.75);
		cursor: pointer;
	}

	#controls button:first-of-type {
		grid-column: 1/-1;
	}

	#score {
		position: absolute;
		top: 20px;
		left: 20px;

		color: white;
		font-size: 2em;
	}

	#result-container {
		position: absolute;
		top: 0;
		display: flex;
		visibility: hidden;
		justify-content: center;
		align-items: center;
		min-width: 100%;
		min-height: 100%;

		#result {
			display: flex;
			flex-direction: column;
			align-items: center;
			padding: 20px;
			background-color: white;
		}

		button {
			padding: 20px 50px 20px 50px;
			background-color: red;
			font-size: inherit;
			font-family: inherit;
			cursor: pointer;
		}
	}
`;
