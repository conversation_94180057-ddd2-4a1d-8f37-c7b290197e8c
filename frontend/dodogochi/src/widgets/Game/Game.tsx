import { useCallback, useEffect, useMemo, useRef } from 'react';
import * as THREE from 'three';

import { GameWrapperStyled } from './Game.styles';

const Texture = (width, height, rects) => {
	const canvas = document.createElement('canvas');
	canvas.width = width;
	canvas.height = height;
	const context = canvas.getContext('2d');
	context.fillStyle = '#ffffff';
	context.fillRect(0, 0, width, height);
	context.fillStyle = 'rgba(0,0,0,0.6)';
	rects.forEach(rect => {
		context.fillRect(rect.x, rect.y, rect.w, rect.h);
	});

	return new THREE.CanvasTexture(canvas);
};

export const truckFrontTexture = Texture(30, 30, [{ x: 5, y: 0, w: 10, h: 30 }]);
export const truckRightSideTexture = Texture(25, 30, [{ x: 15, y: 5, w: 10, h: 10 }]);
export const truckLeftSideTexture = Texture(25, 30, [{ x: 15, y: 15, w: 10, h: 10 }]);

const Game = ({ onClose }) => {
	// Game configuration constants
	const minTileIndex = -8;
	const maxTileIndex = 8;
	const tilesPerRow = maxTileIndex - minTileIndex + 1;
	const tileSize = 42;

	// Refs for Three.js objects and DOM elements
	const canvasRef = useRef(null);
	const sceneRef = useRef(null);
	const rendererRef = useRef(null);
	const cameraRef = useRef(null);
	const playerRef = useRef(null);
	const mapRef = useRef(null);
	const animationIdRef = useRef(null);

	// Game state refs
	const metadataRef = useRef([]);
	const positionRef = useRef({ currentRow: 0, currentTile: 0 });
	const movesQueueRef = useRef([]);
	const moveClockRef = useRef(new THREE.Clock(false));
	const clockRef = useRef(new THREE.Clock());

	const maxAchievedScoreRef = useRef(0);

	// Create textures
	const carTextures = useMemo(
		() => ({
			carFrontTexture: new Texture(40, 80, [{ x: 0, y: 10, w: 30, h: 60 }]),
			carBackTexture: new Texture(40, 80, [{ x: 10, y: 10, w: 30, h: 60 }]),
			carRightSideTexture: new Texture(110, 40, [
				{ x: 10, y: 0, w: 50, h: 30 },
				{ x: 70, y: 0, w: 30, h: 30 },
			]),
			carLeftSideTexture: new Texture(110, 40, [
				{ x: 10, y: 10, w: 50, h: 30 },
				{ x: 70, y: 10, w: 30, h: 30 },
			]),
		}),
		[],
	);

	// Three.js object creation functions
	const createCamera = useCallback(() => {
		const size = 300;
		const viewRatio = window.innerWidth / window.innerHeight;
		const width = viewRatio < 1 ? size : size * viewRatio;
		const height = viewRatio < 1 ? size / viewRatio : size;

		const camera = new THREE.OrthographicCamera(
			width / -2, // left
			width / 2, // right
			height / 2, // top
			height / -2, // bottom
			100, // near
			900, // far
		);

		camera.up.set(0, 0, 1);
		camera.position.set(300, -300, 300);
		camera.lookAt(0, 0, 0);

		return camera;
	}, []);

	const createRenderer = useCallback(() => {
		const canvas = canvasRef.current;
		if (!canvas) {
			throw new Error('Canvas not found');
		}

		const renderer = new THREE.WebGLRenderer({
			alpha: true,
			antialias: true,
			canvas,
		});
		renderer.setPixelRatio(window.devicePixelRatio);
		renderer.setSize(window.innerWidth, window.innerHeight);
		renderer.shadowMap.enabled = true;

		return renderer;
	}, []);

	const createPlayer = useCallback(() => {
		const player = new THREE.Group();

		const body = new THREE.Mesh(
			new THREE.BoxGeometry(15, 15, 20),
			new THREE.MeshLambertMaterial({
				color: 'white',
				flatShading: true,
			}),
		);
		body.position.z = 10;
		body.castShadow = true;
		body.receiveShadow = true;
		player.add(body);

		const cap = new THREE.Mesh(
			new THREE.BoxGeometry(2, 4, 2),
			new THREE.MeshLambertMaterial({
				color: 0xf0619a,
				flatShading: true,
			}),
		);
		cap.position.z = 21;
		cap.castShadow = true;
		cap.receiveShadow = true;
		player.add(cap);

		const playerContainer = new THREE.Group();
		playerContainer.add(player);

		return playerContainer;
	}, []);

	const createDirectionalLight = useCallback(() => {
		const dirLight = new THREE.DirectionalLight();
		dirLight.position.set(-100, -100, 200);
		dirLight.up.set(0, 0, 1);
		dirLight.castShadow = true;

		dirLight.shadow.mapSize.width = 2048;
		dirLight.shadow.mapSize.height = 2048;

		dirLight.shadow.camera.up.set(0, 0, 1);
		dirLight.shadow.camera.left = -400;
		dirLight.shadow.camera.right = 400;
		dirLight.shadow.camera.top = 400;
		dirLight.shadow.camera.bottom = -400;
		dirLight.shadow.camera.near = 50;
		dirLight.shadow.camera.far = 400;

		return dirLight;
	}, []);

	// Utility functions
	const calculateFinalPosition = useCallback(
		(currentPosition, moves) =>
			moves.reduce((position, direction) => {
				if (direction === 'forward') {
					return {
						rowIndex: position.rowIndex + 1,
						tileIndex: position.tileIndex,
					};
				}
				if (direction === 'backward') {
					return {
						rowIndex: position.rowIndex - 1,
						tileIndex: position.tileIndex,
					};
				}
				if (direction === 'left') {
					return {
						rowIndex: position.rowIndex,
						tileIndex: position.tileIndex - 1,
					};
				}
				if (direction === 'right') {
					return {
						rowIndex: position.rowIndex,
						tileIndex: position.tileIndex + 1,
					};
				}

				return position;
			}, currentPosition),
		[],
	);

	const endsUpInValidPosition = useCallback(
		(currentPosition, moves) => {
			// Calculate where the player would end up after the move
			const finalPosition = calculateFinalPosition(currentPosition, moves);

			// Detect if we hit the edge of the board
			if (
				finalPosition.rowIndex === -1 ||
				finalPosition.tileIndex === minTileIndex - 1 ||
				finalPosition.tileIndex === maxTileIndex + 1
			) {
				// Invalid move, ignore move command
				return false;
			}

			// Detect if we hit a tree
			const finalRow = metadataRef.current[finalPosition.rowIndex - 1];
			if (
				finalRow &&
				finalRow.type === 'forest' &&
				finalRow.trees.some(tree => tree.tileIndex === finalPosition.tileIndex)
			) {
				// Invalid move, ignore move command
				return false;
			}

			return true;
		},
		[minTileIndex, maxTileIndex, calculateFinalPosition],
	);

	// Game logic functions
	const queueMove = useCallback(
		direction => {
			const isValidMove = endsUpInValidPosition(
				{
					rowIndex: positionRef.current.currentRow,
					tileIndex: positionRef.current.currentTile,
				},
				[...movesQueueRef.current, direction],
			);

			if (!isValidMove) {
				return;
			}

			movesQueueRef.current.push(direction);
		},
		[endsUpInValidPosition],
	);

	// Random utility functions
	const randomElement = useCallback(array => array[Math.floor(Math.random() * array.length)], []);

	const generateForesMetadata = useCallback(() => {
		const occupiedTiles = new Set();
		const trees = Array.from({ length: 4 }, () => {
			let tileIndex;
			do {
				tileIndex = THREE.MathUtils.randInt(minTileIndex, maxTileIndex);
			} while (occupiedTiles.has(tileIndex));
			occupiedTiles.add(tileIndex);

			const height = randomElement([20, 45, 60]);

			return { tileIndex, height };
		});

		return { type: 'forest', trees };
	}, [minTileIndex, maxTileIndex, randomElement]);

	const generateCarLaneMetadata = useCallback(() => {
		const direction = randomElement([true, false]);
		const speed = randomElement([125, 156, 188]);

		const occupiedTiles = new Set();

		const vehicles = Array.from({ length: 3 }, () => {
			let initialTileIndex;
			do {
				initialTileIndex = THREE.MathUtils.randInt(minTileIndex, maxTileIndex);
			} while (occupiedTiles.has(initialTileIndex));
			occupiedTiles.add(initialTileIndex - 1);
			occupiedTiles.add(initialTileIndex);
			occupiedTiles.add(initialTileIndex + 1);

			const color = randomElement([0xa52523, 0xbdb638, 0x78b14b]);

			return { initialTileIndex, color };
		});

		return { type: 'car', direction, speed, vehicles };
	}, [minTileIndex, maxTileIndex, randomElement]);

	const generateTruckLaneMetadata = useCallback(() => {
		const direction = randomElement([true, false]);
		const speed = randomElement([125, 156, 188]);

		const occupiedTiles = new Set();

		const vehicles = Array.from({ length: 2 }, () => {
			let initialTileIndex;
			do {
				initialTileIndex = THREE.MathUtils.randInt(minTileIndex, maxTileIndex);
			} while (occupiedTiles.has(initialTileIndex));
			occupiedTiles.add(initialTileIndex - 2);
			occupiedTiles.add(initialTileIndex - 1);
			occupiedTiles.add(initialTileIndex);
			occupiedTiles.add(initialTileIndex + 1);
			occupiedTiles.add(initialTileIndex + 2);

			const color = randomElement([0xa52523, 0xbdb638, 0x78b14b]);

			return { initialTileIndex, color };
		});

		return { type: 'truck', direction, speed, vehicles };
	}, [minTileIndex, maxTileIndex, randomElement]);

	const generateRow = useCallback(() => {
		const type = randomElement(['car', 'truck', 'forest']);
		if (type === 'car') {
			return generateCarLaneMetadata();
		}
		if (type === 'truck') {
			return generateTruckLaneMetadata();
		}

		return generateForesMetadata();
	}, [randomElement, generateCarLaneMetadata, generateTruckLaneMetadata, generateForesMetadata]);

	const generateRows = useCallback(
		amount => {
			const rows = [];
			for (let i = 0; i < amount; i++) {
				const rowData = generateRow();
				rows.push(rowData);
			}

			return rows;
		},
		[generateRow],
	);

	// Three.js object creation functions
	const Wheel = useCallback(x => {
		const wheel = new THREE.Mesh(
			new THREE.BoxGeometry(12, 33, 12),
			new THREE.MeshLambertMaterial({
				color: 0x333333,
				flatShading: true,
			}),
		);
		wheel.position.x = x;
		wheel.position.z = 6;

		return wheel;
	}, []);

	const Car = useCallback(
		(initialTileIndex, direction, color) => {
			const car = new THREE.Group();
			car.position.x = initialTileIndex * tileSize;
			if (!direction) {
				car.rotation.z = Math.PI;
			}

			const main = new THREE.Mesh(
				new THREE.BoxGeometry(60, 30, 15),
				new THREE.MeshLambertMaterial({ color, flatShading: true }),
			);
			main.position.z = 12;
			main.castShadow = true;
			main.receiveShadow = true;
			car.add(main);

			const cabin = new THREE.Mesh(new THREE.BoxGeometry(33, 24, 12), [
				new THREE.MeshPhongMaterial({
					color: 0xcccccc,
					flatShading: true,
					map: carTextures.carBackTexture,
				}),
				new THREE.MeshPhongMaterial({
					color: 0xcccccc,
					flatShading: true,
					map: carTextures.carFrontTexture,
				}),
				new THREE.MeshPhongMaterial({
					color: 0xcccccc,
					flatShading: true,
					map: carTextures.carRightSideTexture,
				}),
				new THREE.MeshPhongMaterial({
					color: 0xcccccc,
					flatShading: true,
					map: carTextures.carLeftSideTexture,
				}),
				new THREE.MeshPhongMaterial({ color: 0xcccccc, flatShading: true }), // top
				new THREE.MeshPhongMaterial({ color: 0xcccccc, flatShading: true }), // bottom
			]);
			cabin.position.x = -6;
			cabin.position.z = 25.5;
			cabin.castShadow = true;
			cabin.receiveShadow = true;
			car.add(cabin);

			const frontWheel = Wheel(18);
			car.add(frontWheel);

			const backWheel = Wheel(-18);
			car.add(backWheel);

			return car;
		},
		[tileSize, carTextures, Wheel],
	);

	const Truck = useCallback(
		(initialTileIndex, direction, color) => {
			const truck = new THREE.Group();
			truck.position.x = initialTileIndex * tileSize;
			if (!direction) {
				truck.rotation.z = Math.PI;
			}

			const cargo = new THREE.Mesh(
				new THREE.BoxGeometry(70, 35, 35),
				new THREE.MeshLambertMaterial({
					color: 0xb4c6fc,
					flatShading: true,
				}),
			);
			cargo.position.x = -15;
			cargo.position.z = 25;
			cargo.castShadow = true;
			cargo.receiveShadow = true;
			truck.add(cargo);

			const cabin = new THREE.Mesh(new THREE.BoxGeometry(30, 30, 30), [
				new THREE.MeshLambertMaterial({
					color,
					flatShading: true,
					map: truckFrontTexture,
				}), // front
				new THREE.MeshLambertMaterial({
					color,
					flatShading: true,
				}), // back
				new THREE.MeshLambertMaterial({
					color,
					flatShading: true,
					map: truckLeftSideTexture,
				}),
				new THREE.MeshLambertMaterial({
					color,
					flatShading: true,
					map: truckRightSideTexture,
				}),
				new THREE.MeshPhongMaterial({ color, flatShading: true }), // top
				new THREE.MeshPhongMaterial({ color, flatShading: true }), // bottom
			]);
			cabin.position.x = 35;
			cabin.position.z = 20;
			cabin.castShadow = true;
			cabin.receiveShadow = true;

			truck.add(cabin);

			const frontWheel = Wheel(37);
			truck.add(frontWheel);

			const middleWheel = Wheel(5);
			truck.add(middleWheel);

			const backWheel = Wheel(-35);
			truck.add(backWheel);

			return truck;
		},
		[tileSize, Wheel],
	);

	// Three.js scene creation functions
	const createGrass = useCallback(
		rowIndex => {
			const grass = new THREE.Group();
			grass.position.y = rowIndex * tileSize;

			const createSection = color =>
				new THREE.Mesh(
					new THREE.BoxGeometry(tilesPerRow * tileSize, tileSize, 3),
					new THREE.MeshLambertMaterial({ color }),
				);

			const middle = createSection(0xbaf455);
			middle.receiveShadow = true;
			grass.add(middle);

			const left = createSection(0x99c846);
			left.position.x = -tilesPerRow * tileSize;
			grass.add(left);

			const right = createSection(0x99c846);
			right.position.x = tilesPerRow * tileSize;
			grass.add(right);

			return grass;
		},
		[tilesPerRow, tileSize],
	);

	const createRoad = useCallback(
		rowIndex => {
			const road = new THREE.Group();
			road.position.y = rowIndex * tileSize;

			const createSection = color =>
				new THREE.Mesh(
					new THREE.PlaneGeometry(tilesPerRow * tileSize, tileSize),
					new THREE.MeshLambertMaterial({ color }),
				);

			const middle = createSection(0x454a59);
			middle.receiveShadow = true;
			road.add(middle);

			const left = createSection(0x393d49);
			left.position.x = -tilesPerRow * tileSize;
			road.add(left);

			const right = createSection(0x393d49);
			right.position.x = tilesPerRow * tileSize;
			road.add(right);

			return road;
		},
		[tilesPerRow, tileSize],
	);

	const createTree = useCallback(
		(tileIndex, height) => {
			const tree = new THREE.Group();
			tree.position.x = tileIndex * tileSize;

			const trunk = new THREE.Mesh(
				new THREE.BoxGeometry(15, 15, 20),
				new THREE.MeshLambertMaterial({
					color: 0x4d2926,
					flatShading: true,
				}),
			);
			trunk.position.z = 10;
			tree.add(trunk);

			const crown = new THREE.Mesh(
				new THREE.BoxGeometry(30, 30, height),
				new THREE.MeshLambertMaterial({
					color: 0x7aa21d,
					flatShading: true,
				}),
			);
			crown.position.z = height / 2 + 20;
			crown.castShadow = true;
			crown.receiveShadow = true;
			tree.add(crown);

			return tree;
		},
		[tileSize],
	);

	const addRows = useCallback(() => {
		if (!mapRef.current) {
			return;
		}

		const newMetadata = generateRows(20);

		const startIndex = metadataRef.current.length;
		metadataRef.current.push(...newMetadata);

		newMetadata.forEach((rowData, index) => {
			const rowIndex = startIndex + index + 1;

			if (rowData.type === 'forest') {
				const row = createGrass(rowIndex);

				rowData.trees.forEach(({ tileIndex, height }) => {
					const tree = createTree(tileIndex, height);
					row.add(tree);
				});

				mapRef.current.add(row);
			}

			if (rowData.type === 'car') {
				const row = createRoad(rowIndex);

				rowData.vehicles.forEach(vehicle => {
					const car = Car(vehicle.initialTileIndex, rowData.direction, vehicle.color);
					vehicle.ref = car;
					row.add(car);
				});

				mapRef.current.add(row);
			}

			if (rowData.type === 'truck') {
				const row = createRoad(rowIndex);

				rowData.vehicles.forEach(vehicle => {
					const truck = Truck(vehicle.initialTileIndex, rowData.direction, vehicle.color);
					vehicle.ref = truck;
					row.add(truck);
				});

				mapRef.current.add(row);
			}
		});
	}, [createGrass, createRoad, createTree, Car, Truck, generateRows]);

	const initializeMap = useCallback(() => {
		if (!mapRef.current) {
			return;
		}

		// Remove all rows
		metadataRef.current.length = 0;
		mapRef.current.remove(...mapRef.current.children);

		// Add new rows
		for (let rowIndex = 0; rowIndex > -10; rowIndex--) {
			const grass = createGrass(rowIndex);
			mapRef.current.add(grass);
		}
		addRows();
	}, [createGrass, addRows]);

	const stepCompleted = useCallback(() => {
		const direction = movesQueueRef.current.shift();

		if (direction === 'forward') {
			positionRef.current.currentRow += 1;
		}
		if (direction === 'backward') {
			positionRef.current.currentRow -= 1;
		}
		if (direction === 'left') {
			positionRef.current.currentTile -= 1;
		}
		if (direction === 'right') {
			positionRef.current.currentTile += 1;
		}

		// Add new rows if the player is running out of them
		if (positionRef.current.currentRow > metadataRef.current.length - 10) {
			addRows();
		}

		const scoreDOM = document.getElementById('score');
		if (scoreDOM) {
			if (positionRef.current.currentRow > maxAchievedScoreRef.current) {
				maxAchievedScoreRef.current = positionRef.current.currentRow;
			}
			scoreDOM.innerText = positionRef.current.currentRow.toString();
		}
	}, [addRows]);

	const initializePlayer = useCallback(() => {
		// Initialize the Three.js player object
		if (playerRef.current) {
			playerRef.current.position.x = 0;
			playerRef.current.position.y = 0;
			playerRef.current.children[0].position.z = 0;
		}

		// Initialize metadata
		positionRef.current.currentRow = 0;
		positionRef.current.currentTile = 0;

		// Clear the moves queue
		movesQueueRef.current.length = 0;
	}, []);

	// Animation functions
	const setPosition = useCallback(
		progress => {
			if (!playerRef.current) {
				return;
			}

			const startX = positionRef.current.currentTile * tileSize;
			const startY = positionRef.current.currentRow * tileSize;
			let endX = startX;
			let endY = startY;

			if (movesQueueRef.current[0] === 'left') {
				endX -= tileSize;
			}
			if (movesQueueRef.current[0] === 'right') {
				endX += tileSize;
			}
			if (movesQueueRef.current[0] === 'forward') {
				endY += tileSize;
			}
			if (movesQueueRef.current[0] === 'backward') {
				endY -= tileSize;
			}

			playerRef.current.position.x = THREE.MathUtils.lerp(startX, endX, progress);
			playerRef.current.position.y = THREE.MathUtils.lerp(startY, endY, progress);
			playerRef.current.children[0].position.z = Math.sin(progress * Math.PI) * 8;
		},
		[tileSize],
	);

	const setRotation = useCallback(progress => {
		if (!playerRef.current) {
			return;
		}

		let endRotation = 0;
		if (movesQueueRef.current[0] === 'forward') {
			endRotation = 0;
		}
		if (movesQueueRef.current[0] === 'left') {
			endRotation = Math.PI / 2;
		}
		if (movesQueueRef.current[0] === 'right') {
			endRotation = -Math.PI / 2;
		}
		if (movesQueueRef.current[0] === 'backward') {
			endRotation = Math.PI;
		}

		playerRef.current.children[0].rotation.z = THREE.MathUtils.lerp(
			playerRef.current.children[0].rotation.z,
			endRotation,
			progress,
		);
	}, []);

	const animatePlayer = useCallback(() => {
		if (!movesQueueRef.current.length) {
			return;
		}

		if (!moveClockRef.current.running) {
			moveClockRef.current.start();
		}

		const stepTime = 0.2; // Seconds it takes to take a step
		const progress = Math.min(1, moveClockRef.current.getElapsedTime() / stepTime);

		setPosition(progress);
		setRotation(progress);

		// Once a step has ended
		if (progress >= 1) {
			stepCompleted();
			moveClockRef.current.stop();
		}
	}, [setPosition, setRotation, stepCompleted]);

	const animateVehicles = useCallback(() => {
		const delta = clockRef.current.getDelta();

		// Animate cars and trucks
		metadataRef.current.forEach(rowData => {
			if (rowData.type === 'car' || rowData.type === 'truck') {
				const beginningOfRow = (minTileIndex - 2) * tileSize;
				const endOfRow = (maxTileIndex + 2) * tileSize;

				rowData.vehicles.forEach(({ ref }) => {
					if (!ref) {
						throw Error('Vehicle reference is missing');
					}

					if (rowData.direction) {
						ref.position.x =
							ref.position.x > endOfRow ? beginningOfRow : ref.position.x + rowData.speed * delta;
					} else {
						ref.position.x =
							ref.position.x < beginningOfRow ? endOfRow : ref.position.x - rowData.speed * delta;
					}
				});
			}
		});
	}, [minTileIndex, maxTileIndex, tileSize]);

	const hitTest = useCallback(() => {
		if (!playerRef.current) {
			return;
		}

		const row = metadataRef.current[positionRef.current.currentRow - 1];
		if (!row) {
			return;
		}

		if (row.type === 'car' || row.type === 'truck') {
			const playerBoundingBox = new THREE.Box3();
			playerBoundingBox.setFromObject(playerRef.current);

			row.vehicles.forEach(({ ref }) => {
				if (!ref) {
					throw Error('Vehicle reference is missing');
				}

				const vehicleBoundingBox = new THREE.Box3();
				vehicleBoundingBox.setFromObject(ref);

				if (playerBoundingBox.intersectsBox(vehicleBoundingBox)) {
					const resultDOM = document.getElementById('result-container');
					const finalScoreDOM = document.getElementById('final-score');
					const bestScoreDOM = document.getElementById('best-score');
					if (!resultDOM || !finalScoreDOM || !bestScoreDOM) {
						return;
					}
					resultDOM.style.visibility = 'visible';
					finalScoreDOM.innerText = positionRef.current.currentRow.toString();
					bestScoreDOM.innerText = maxAchievedScoreRef.current.toString();
				}
			});
		}
	}, []);

	const initializeGame = useCallback(() => {
		initializePlayer();
		initializeMap();

		// Initialize UI
		const scoreDOM = document.getElementById('score');
		const resultDOM = document.getElementById('result-container');
		if (scoreDOM) {
			scoreDOM.innerText = '0';
		}
		if (resultDOM) {
			resultDOM.style.visibility = 'hidden';
		}
	}, [initializePlayer, initializeMap]);

	// Initialize Three.js scene when component mounts
	useEffect(() => {
		if (!canvasRef.current) {
			return;
		}

		// Create Three.js objects
		const scene = new THREE.Scene();
		const camera = createCamera();
		const renderer = createRenderer();
		const player = createPlayer();
		const map = new THREE.Group();
		const dirLight = createDirectionalLight();

		// Store refs
		sceneRef.current = scene;
		cameraRef.current = camera;
		rendererRef.current = renderer;
		playerRef.current = player;
		mapRef.current = map;

		// Set up scene
		const ambientLight = new THREE.AmbientLight();
		scene.add(ambientLight);
		scene.add(player);
		scene.add(map);

		dirLight.target = player;
		player.add(dirLight);
		player.add(camera);

		// Initialize game
		initializeGame();

		// Animation loop
		const animate = () => {
			animationIdRef.current = requestAnimationFrame(animate);

			animateVehicles();
			animatePlayer();
			hitTest();

			renderer.render(scene, camera);
		};
		animate();

		// Keyboard event handlers
		const handleKeyDown = event => {
			if (event.key === 'ArrowUp') {
				event.preventDefault();
				queueMove('forward');
			} else if (event.key === 'ArrowDown') {
				event.preventDefault();
				queueMove('backward');
			} else if (event.key === 'ArrowLeft') {
				event.preventDefault();
				queueMove('left');
			} else if (event.key === 'ArrowRight') {
				event.preventDefault();
				queueMove('right');
			}
		};

		window.addEventListener('keydown', handleKeyDown);

		// Cleanup function
		return () => {
			if (animationIdRef.current) {
				cancelAnimationFrame(animationIdRef.current);
			}
			window.removeEventListener('keydown', handleKeyDown);
			renderer.dispose();
		};
	}, [
		createCamera,
		createRenderer,
		createPlayer,
		createDirectionalLight,
		initializeGame,
		animateVehicles,
		animatePlayer,
		hitTest,
		queueMove,
	]);

	return (
		<GameWrapperStyled>
			<canvas ref={canvasRef} className='game' />
			<div id='controls'>
				<div>
					<button id='forward' onClick={() => queueMove('forward')}>
						▲
					</button>
					<button id='left' onClick={() => queueMove('left')}>
						◀
					</button>
					<button id='backward' onClick={() => queueMove('backward')}>
						▼
					</button>
					<button id='right' onClick={() => queueMove('right')}>
						▶
					</button>
				</div>
			</div>
			<div id='score'>0</div>
			<div>
				<button id='close' onClick={() => onClose(maxAchievedScoreRef.current)}>
					x
				</button>
			</div>
			<div id='result-container'>
				<div id='result'>
					<h1>Game Over</h1>
					<p>
						Your current score: <span id='final-score' />
					</p>
					<p>
						Your best score: <span id='best-score' />
					</p>
					<button id='retry' onClick={initializeGame}>
						Retry
					</button>
				</div>
			</div>
		</GameWrapperStyled>
	);
};

export default Game;
