import styled from 'styled-components';

export const WrapperStyled = styled.div`
	gap: 24px;
	display: flex;
	flex-direction: column;
	margin: 0 auto;
	padding: 24px;
	max-width: 1200px;
	border-radius: 36px;
	background: rgba(0, 21, 47, 255);

	@media (max-width: 768px) {
		padding: 16px;
		gap: 16px;
		border-radius: 24px;
	}

	@media (max-width: 480px) {
		padding: 12px;
		gap: 12px;
		border-radius: 16px;
	}
`;

export const CharacterWrapperStyled = styled.div`
	gap: 12px;
	display: flex;
	flex: 1;
	justify-content: space-between;

	@media (max-width: 768px) {
		flex-direction: column;
		gap: 16px;
	}
`;

export const CharacterSectionStyled = styled.div`
	gap: 12px;
	display: flex;
	flex: 1;
	flex-direction: column;

	@media (max-width: 768px) {
		flex: none;
	}
`;
export const CharacterImageWrapperStyled = styled.div`
	flex: 1;
	overflow: hidden;
	min-height: 200px;
	border-radius: 36px;
	box-shadow:
		0 0 20px rgba(28, 50, 51, 0.3),
		inset 0 1px 3px rgba(125, 211, 252, 0.1);

	@media (max-width: 768px) {
		min-height: 250px;
		border-radius: 24px;
	}

	@media (max-width: 480px) {
		min-height: 200px;
		border-radius: 16px;
	}
`;

export const CharacterImageStyled = styled.img`
	object-fit: cover;
	width: 100%;
	height: 100%;
`;

export const TotalScoreWrapperStyled = styled.div`
	display: flex;
	flex: 1;
	flex-direction: column;
	justify-content: center;
	align-items: center;

	@media (max-width: 768px) {
		flex: none;
		align-self: center;
	}
`;

export const ScoreItemWrapperStyled = styled.div`
	flex: 1;
`;

export const AdditionalStatisticsWrapperStyled = styled.div`
	gap: 12px;
	display: flex;
	justify-content: center;
	align-items: center;

	@media (max-width: 768px) {
		flex-direction: column;
		gap: 16px;
	}
`;

export const AdditionalStatisticsItemWrapperStyled = styled.div`
	display: flex;
	flex: 1;

	@media (max-width: 768px) {
		flex: none;
		width: 100%;
		max-width: 300px;
	}
`;

export const GameButtonsWrapperStyled = styled.div`
	gap: 12px;
	display: flex;

	@media (max-width: 480px) {
		flex-direction: column;
		gap: 16px;
	}
`;

export const GameButtonWrapperStyled = styled.div`
	flex: 1;

	@media (max-width: 480px) {
		flex: none;
	}
`;
