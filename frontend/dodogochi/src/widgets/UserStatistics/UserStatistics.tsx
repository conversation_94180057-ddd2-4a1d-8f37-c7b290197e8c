import { type FC, useState } from "react";

import CommentImageSrc from "shared/assets/comment.png";
import CharacterImageSrc from "shared/assets/dodogochi.png";
import LikeImageSrc from "shared/assets/like.png";
import PizzaImageSrc from "shared/assets/pizza.png";
import { But<PERSON> } from "shared/ui/Button/Button";
import Game from "widgets/Game/Game";
import { AdditionalStatisticsItem } from "widgets/UserStatistics/components/AdditionalStatisticsItem/AdditionalStatisticsItem";
import { EnergyPoints } from "widgets/UserStatistics/components/EnergyPoints/EnergyPoints";
import { LevelProgressBar } from "widgets/UserStatistics/components/LevelProgressBar/LevelProgressBar";
import {
    AdditionalStatisticsItemWrapperStyled,
    AdditionalStatisticsWrapperStyled,
    CharacterImageStyled,
    CharacterImageWrapperStyled,
    CharacterSectionStyled,
    CharacterWrapperStyled,
    GameButtonsWrapperStyled,
    GameButtonWrapperStyled,
    TotalScoreWrapperStyled,
    WrapperStyled,
} from "widgets/UserStatistics/UserStatistics.styles";

export const UserStatistics: FC = () => {
    const [isGameOpen, setIsGameOpen] = useState(false);
    const rewardPlayer = async (score: number) => {
        console.log("Rewarding player with score: ", score);
    };
    const handleCloseGame = async (score: number) => {
        console.log("Closing game");
        await rewardPlayer(score);
        setIsGameOpen(false);
    };

    return (
        <WrapperStyled>
            <CharacterWrapperStyled>
                <CharacterSectionStyled>
                    <CharacterImageWrapperStyled>
                        {/* eslint-disable-next-line @typescript-eslint/no-unsafe-assignment */}
                        <CharacterImageStyled src={CharacterImageSrc} />
                    </CharacterImageWrapperStyled>
                    <LevelProgressBar currentLevel={5} currentXP={750} maxXP={1000} />
                </CharacterSectionStyled>
                <TotalScoreWrapperStyled>
                    <EnergyPoints points={500} />
                </TotalScoreWrapperStyled>
            </CharacterWrapperStyled>

            <AdditionalStatisticsWrapperStyled>
                <AdditionalStatisticsItemWrapperStyled>
                    <AdditionalStatisticsItem count={120} imageUrl={PizzaImageSrc} />
                </AdditionalStatisticsItemWrapperStyled>
                <AdditionalStatisticsItemWrapperStyled>
                    <AdditionalStatisticsItem count={250} imageUrl={LikeImageSrc} />
                </AdditionalStatisticsItemWrapperStyled>
                <AdditionalStatisticsItemWrapperStyled>
                    <AdditionalStatisticsItem count={40} imageUrl={CommentImageSrc} />
                </AdditionalStatisticsItemWrapperStyled>
            </AdditionalStatisticsWrapperStyled>

            <GameButtonsWrapperStyled>
                <GameButtonWrapperStyled>
                    {/* eslint-disable-next-line i18next/no-literal-string */}
                    <Button onClick={() => setIsGameOpen(true)}>Game</Button>
                </GameButtonWrapperStyled>
            </GameButtonsWrapperStyled>

            {isGameOpen && <Game onClose={handleCloseGame} />}
        </WrapperStyled>
    );
};
