import { type FC } from 'react';

import EnergyImageSrc from 'shared/assets/enegry.png';
import {
	EnergyImageStyled,
	PointsStyled,
	WrapperStyled,
} from 'widgets/UserStatistics/components/EnergyPoints/EnergyPoints.styles';

interface Props {
	points: number;
}

export const EnergyPoints: FC<Props> = ({ points }) => (
	<WrapperStyled>
		<PointsStyled>{points}</PointsStyled>
		<EnergyImageStyled src={EnergyImageSrc} />
	</WrapperStyled>
);
