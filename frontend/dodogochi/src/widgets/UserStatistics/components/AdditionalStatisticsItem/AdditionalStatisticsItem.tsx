import { type FC } from 'react';

import {
	ImageStyled,
	PercentageStyled,
	WrapperStyled,
} from 'widgets/UserStatistics/components/AdditionalStatisticsItem/AdditionalStatisticsItem.styles';

interface Props {
	count: number;
	imageUrl: string;
}

export const AdditionalStatisticsItem: FC<Props> = ({ count, imageUrl }) => (
	<WrapperStyled>
		{/* <TitleStyled>{title}</TitleStyled> */}
		<ImageStyled src={imageUrl} />
		<PercentageStyled>{count}</PercentageStyled>
	</WrapperStyled>
);
