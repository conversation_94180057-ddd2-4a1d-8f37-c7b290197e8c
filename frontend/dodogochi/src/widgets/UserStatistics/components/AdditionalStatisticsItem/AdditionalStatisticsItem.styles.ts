import styled from 'styled-components';

export const WrapperStyled = styled.div`
	display: flex;
	flex-direction: column;
	align-items: center;
	padding: 16px;
	width: 100%;

	@media (max-width: 768px) {
		padding: 12px;
	}

	@media (max-width: 480px) {
		padding: 8px;
	}
`;

export const TitleStyled = styled.h3`
	margin: 0 0 16px 0;
	color: #7dd3fc;
	text-transform: uppercase;
	letter-spacing: 1px;
	font-weight: 500;
	font-size: 12px;
	font-family: 'Inter', sans-serif;
`;

export const PercentageStyled = styled.div`
	color: rgba(0, 239, 243, 255);
	font-weight: 700;
	font-size: 56px;

	@media (max-width: 768px) {
		font-size: 48px;
	}

	@media (max-width: 480px) {
		font-size: 40px;
	}
`;

export const ImageStyled = styled.img`
	width: auto;
	height: 150px;

	@media (max-width: 768px) {
		height: 120px;
	}

	@media (max-width: 480px) {
		height: 100px;
	}
`;
