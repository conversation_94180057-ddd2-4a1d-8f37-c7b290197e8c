import { type FC } from 'react';

import {
	LevelLabelStyled,
	ProgressBarFillStyled,
	ProgressBarStyled,
	WrapperStyled,
} from 'widgets/UserStatistics/components/LevelProgressBar/LevelProgressBar.styles';

interface Props {
	currentLevel: number;
	currentXP: number;
	maxXP: number;
}

export const LevelProgressBar: FC<Props> = ({ currentLevel, currentXP, maxXP }) => {
	const progress = Math.min((currentXP / maxXP) * 100, 100);
	const nextLevel = currentLevel + 1;

	return (
		<WrapperStyled>
			<LevelLabelStyled>{currentLevel}</LevelLabelStyled>
			<ProgressBarStyled>
				<ProgressBarFillStyled progress={progress} />
			</ProgressBarStyled>
			<LevelLabelStyled>{nextLevel}</LevelLabelStyled>
		</WrapperStyled>
	);
};
