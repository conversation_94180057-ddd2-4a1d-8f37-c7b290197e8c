import styled from 'styled-components';

export const WrapperStyled = styled.div`
	gap: 12px;
	display: flex;
	align-items: center;
	padding: 8px 0;
	width: 100%;

	@media (max-width: 480px) {
		gap: 8px;
		padding: 6px 0;
	}
`;

export const LevelLabelStyled = styled.div`
	min-width: 24px;
	color: #7dd3fc;
	text-align: center;
	text-shadow: 0 0 8px rgba(125, 211, 252, 0.6);
	font-weight: 700;
	font-size: 18px;
	font-family: 'Inter', sans-serif;

	@media (max-width: 768px) {
		font-size: 16px;
		min-width: 20px;
	}

	@media (max-width: 480px) {
		font-size: 14px;
		min-width: 18px;
	}
`;

export const ProgressBarStyled = styled.div`
	flex: 1;
	overflow: hidden;
	height: 20px;
	border: 2px solid #1a4a5a;
	border-radius: 12px;
	background: #2d5a73;
	box-shadow:
		inset 0 2px 4px rgba(0, 0, 0, 0.3),
		0 0 8px rgba(125, 211, 252, 0.2);

	@media (max-width: 768px) {
		height: 18px;
		border-radius: 10px;
	}

	@media (max-width: 480px) {
		height: 16px;
		border-radius: 8px;
		border-width: 1px;
	}
`;

export const ProgressBarFillStyled = styled.div<{ progress: number }>`
	position: relative;
	width: ${({ progress }) => progress}%;
	height: 100%;
	border-radius: 8px;
	background: linear-gradient(90deg, #7dd3fc 0%, #38bdf8 50%, #0ea5e9 100%);
	box-shadow:
		0 0 12px rgba(125, 211, 252, 0.6),
		inset 0 1px 2px rgba(255, 255, 255, 0.2);
	transition: width 0.6s ease;

	&::after {
		position: absolute;
		top: 0;
		right: 0;
		left: 0;
		height: 50%;
		border-radius: 8px 8px 0 0;
		background: linear-gradient(180deg, rgba(255, 255, 255, 0.3) 0%, transparent 100%);
		content: '';
	}
`;
