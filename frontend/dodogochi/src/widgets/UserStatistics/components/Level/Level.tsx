import { type FC } from 'react';

import LevelImageSrc from 'shared/assets/level.png';
import { LevelImageStyled, LevelStyled, WrapperStyled } from 'widgets/UserStatistics/components/Level/Level.styles';

interface Props {
	level: number;
}

export const Level: FC<Props> = ({ level }) => (
	<WrapperStyled>
		<LevelStyled>{level}</LevelStyled>
		<LevelImageStyled src={LevelImageSrc} />
	</WrapperStyled>
);
