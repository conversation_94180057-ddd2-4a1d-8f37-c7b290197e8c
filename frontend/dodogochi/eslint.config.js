import dodopizzaPlugin from '@dodopizza/eslint-plugin';
import pluginQuery from '@tanstack/eslint-plugin-query';

export default [
	...dodopizzaPlugin.configs.base,
	...dodopizzaPlugin.configs.vitest,
	...dodopizzaPlugin.configs.i18n,
	...pluginQuery.configs['flat/recommended'],
	{
		files: ['**/routes/**'],
		rules: {
			'prefer-arrow-functions/prefer-arrow-functions': 'off',
			'react/function-component-definition': 'off',
		},
	},
];
