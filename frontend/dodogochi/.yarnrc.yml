compressionLevel: mixed

defaultSemverRangePrefix: ""

enableGlobalCache: true

nodeLinker: pnp

npmScopes:
  dodopizza:
    npmAuthToken: "${NPM_TOKEN}"
    npmPublishRegistry: "https://npm.pkg.github.com"
    npmRegistryServer: "https://npm.pkg.github.com"

packageExtensions:
  bundle-require@*:
    dependencies:
      esbuild: "*"
  eslint-module-utils@*:
    dependencies:
      eslint-import-resolver-typescript: 4.3.2
  local-pkg@*:
    dependencies:
      jsdom: "*"
  styled-components@*:
    dependencies:
      react-is: "*"

progressBarStyle: simba

supportedArchitectures:
  cpu:
    - x64
    - arm64
  libc:
    - current
    - glibc
  os:
    - darwin
    - linux
    - win32

tsEnableAutoTypes: true

yarnPath: .yarn/releases/yarn-4.9.4.cjs

plugins:
  - .yarn/plugins/@dodopizza/node-version-check-plugin.cjs