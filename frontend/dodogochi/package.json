{"name": "<PERSON><PERSON><PERSON><PERSON>", "license": "MIT", "type": "module", "scripts": {"build": "dodo-react-scripts build", "build:watch": "dodo-react-scripts build --watch", "serve": "dodo-react-scripts serve", "serve:mock": "dodo-react-scripts serve --mock", "lint": "eslint \"{**/*,*}.{ts,tsx,js,json}\"", "lint:fix": "eslint \"{**/*,*}.{ts,tsx,js,json}\" --fix", "test": "dodo-react-scripts test", "test:preview": "dodo-react-scripts test --preview", "test:ui": "dodo-react-scripts test --ui", "test:run": "dodo-react-scripts test run"}, "devDependencies": {"@dodobrands/react-logger": "6.5.1", "@dodopizza/eslint-plugin": "4.1.4", "@dodopizza/frontend-scripts": "3.5.1", "@dodopizza/ts-config": "1.0.1", "@tanstack/eslint-plugin-query": "5.86.0", "@tanstack/react-query-devtools": "5.86.0", "@tanstack/router-plugin": "1.131.35", "@testing-library/dom": "^9.0.0", "@testing-library/react": "14.1.2", "@testing-library/user-event": "14.5.1", "@types/node": "22.13.10", "@types/react": "^19", "@types/react-dom": "^19", "@types/three": "^0", "eslint": "9.24.0", "typescript": "5.8.3"}, "engines": {"node": "22"}, "packageManager": "yarn@4.9.4", "dependencies": {"@tanstack/react-query": "5.86.0", "@tanstack/react-router": "1.131.35", "@tanstack/react-router-devtools": "1.131.35", "react": "19.1.1", "react-dom": "19.1.1", "single-spa-react": "6.0.2", "styled-components": "6.1.19", "three": "0.180.0"}}